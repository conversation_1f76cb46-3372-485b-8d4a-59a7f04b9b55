import { Dropdown, Tooltip } from 'flowbite-react';
import { Logo, Logout, MineIcon, UserIcon } from '../../../assets/icons/icons';
import { useNavigate } from 'react-router';
import decodeJWT from '../../../utils/jwtdecoder';
import { MdMenu } from 'react-icons/md';
import mixpanel from 'mixpanel-browser';

export function NavBar(props: any) {
  const decoded = decodeJWT();
  return (
    <div className="flex justify-between mx-2">
      <div className="block md:hidden">
        <div
          className="m-3 md:hidden cursor-pointer  "
          onClick={() => props?.setOpen(true)}
        >
          <MdMenu size={25} className="text-white" />
        </div>
      </div>
      <div className="hidden md:block z-[1000]">
        {props?.open ? (
          <div>
            <svg
              width="87"
              height="38"
              viewBox="0 0 87 38"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="w-20 h-12 md:w-24 md:h-16 absolute z-[1000] mt-[9px]"
            >
              <path
                d="M44.6288 25.5703H41.5169V7.82954H44.6288V25.5703ZM65.5557 25.5703H62.231L58.7733 12.4842L55.3421 25.5703H52.0174L46.9372 7.82954H50.4215L53.8793 21.5008L57.5498 7.82954H60.0234L63.6939 21.5008L67.125 7.82954H70.6093L65.5557 25.5703ZM80.2995 25.5703H77.1875V10.5691H71.8147V7.82954H85.6722V10.5691H80.2995V25.5703Z"
                fill="white"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17.7982 0L35.5963 17.7982L17.7982 35.5963L0 17.7982L17.7982 0ZM12.2674 17.6794C12.2674 19.9092 10.4598 21.7168 8.23 21.7168C6.00024 21.7168 4.19265 19.9092 4.19265 17.6794C4.19265 15.4497 6.00024 13.6421 8.23 13.6421C10.4598 13.6421 12.2674 15.4497 12.2674 17.6794ZM14.7206 17.6795C14.7206 21.3499 12.2184 23.8141 8.54055 24.3253C13.1139 24.5671 16.1494 21.3499 16.1494 17.6795C16.1494 14.009 13.3174 10.8911 8.54055 11.0336C12.2184 11.6238 14.7206 14.009 14.7206 17.6795ZM11.0177 27.273C15.6781 26.419 19.3938 23.0636 19.3938 17.6794C19.3938 12.2953 15.9223 9.34442 11.0177 8.08592C17.2044 7.81808 21.4172 12.2953 21.4172 17.6794C21.4172 23.0636 17.3062 27.4773 11.0177 27.273ZM24.7515 17.68C24.7515 24.3495 20.1487 28.5061 14.3757 29.5639C22.1655 29.817 27.2579 24.3495 27.2579 17.68C27.2579 11.0105 22.0394 5.46435 14.3757 5.79614C20.4512 7.35508 24.7515 11.0105 24.7515 17.68Z"
                fill="white"
              />
              <path
                d="M24.9163 34.6623V33.1566H25.6793C25.7406 33.1566 25.8072 33.1743 25.8793 33.2097C25.9514 33.2437 26.0126 33.2967 26.0629 33.3688C26.1146 33.4395 26.1405 33.5293 26.1405 33.6381C26.1405 33.7483 26.1139 33.8421 26.0609 33.9197C26.0078 33.9958 25.9432 34.0537 25.8671 34.0931C25.7922 34.1325 25.7215 34.1523 25.6549 34.1523H25.104V33.9074H25.5528C25.5977 33.9074 25.646 33.885 25.6977 33.8401C25.7508 33.7952 25.7773 33.7279 25.7773 33.6381C25.7773 33.5456 25.7508 33.4831 25.6977 33.4504C25.646 33.4178 25.6005 33.4014 25.561 33.4014H25.2468V34.6623H24.9163ZM25.8262 33.9523L26.2017 34.6623H25.8385L25.4712 33.9523H25.8262ZM25.4876 35.5601C25.2618 35.5601 25.0503 35.5179 24.853 35.4336C24.6558 35.3492 24.4824 35.2323 24.3328 35.0826C24.1831 34.933 24.0662 34.7596 23.9818 34.5624C23.8975 34.3651 23.8553 34.1536 23.8553 33.9278C23.8553 33.702 23.8975 33.4905 23.9818 33.2933C24.0662 33.0961 24.1831 32.9227 24.3328 32.773C24.4824 32.6234 24.6558 32.5064 24.853 32.4221C25.0503 32.3378 25.2618 32.2956 25.4876 32.2956C25.7134 32.2956 25.9249 32.3378 26.1221 32.4221C26.3193 32.5064 26.4927 32.6234 26.6424 32.773C26.792 32.9227 26.909 33.0961 26.9933 33.2933C27.0776 33.4905 27.1198 33.702 27.1198 33.9278C27.1198 34.1536 27.0776 34.3651 26.9933 34.5624C26.909 34.7596 26.792 34.933 26.6424 35.0826C26.4927 35.2323 26.3193 35.3492 26.1221 35.4336C25.9249 35.5179 25.7134 35.5601 25.4876 35.5601ZM25.4876 35.1683C25.7161 35.1683 25.9242 35.1126 26.1119 35.001C26.2996 34.8895 26.4492 34.7399 26.5608 34.5522C26.6723 34.3645 26.7281 34.1564 26.7281 33.9278C26.7281 33.6993 26.6723 33.4912 26.5608 33.3035C26.4492 33.1158 26.2996 32.9662 26.1119 32.8546C25.9242 32.7431 25.7161 32.6873 25.4876 32.6873C25.259 32.6873 25.0509 32.7431 24.8632 32.8546C24.6755 32.9662 24.5259 33.1158 24.4144 33.3035C24.3028 33.4912 24.2471 33.6993 24.2471 33.9278C24.2471 34.1564 24.3028 34.3645 24.4144 34.5522C24.5259 34.7399 24.6755 34.8895 24.8632 35.001C25.0509 35.1126 25.259 35.1683 25.4876 35.1683Z"
                fill="white"
              />
            </svg>
          </div>
        ) : (
          <Logo className="w-10 h-10 mt-[20px] mx-4 z-[1000]" />
        )}
      </div>
      <div className="block md:hidden my-auto">
        <Logo className="w-8 h-8 mt-1" />
      </div>
      <div className={`my-1 cursor-pointer pr-[2.3rem] 2xl:pr-[3.7rem]`}>
        {/* <Dropdown
          label=""
          dismissOnClick={false}
          className="z-50 bg-[#1A252F] text-white border-[#1A252F] "
          renderTrigger={() => (
            <div className="flex">
              <div className="flex items-center justify-center w-8 h-8 my-auto mr-2 bg-[#FFB132] text-black rounded-full font-bold text-[12px]">
                {`${decoded?.firstname
                  ?.charAt(0)
                  .toUpperCase()}${decoded?.lastname?.charAt(0).toUpperCase()}`}
              </div>
              <div className="text-white text-[14px] font-semibold pl-1 my-2">
                {decoded?.firstname + ' ' + decoded?.lastname}
              </div>
            </div>
          )}
        >
          <Dropdown.Item
            className="text-white  hover:bg-[#4AA8FE] cursor-text"
            icon={UserIcon}
          >
            {decoded?.username}
          </Dropdown.Item>
          <Dropdown.Divider className="bg-white" />
          {decoded?.role !== 'superuser' && (
            <>
              <Dropdown.Item
                className="text-white  hover:bg-[#4AA8FE] cursor-text"
                icon={MineIcon}
              >
                {decoded?.minename}
              </Dropdown.Item>
              <Dropdown.Divider className="bg-white" />
            </>
          )}
          <Dropdown.Item
            className="text-white hover:bg-[#4AA8FE]"
            icon={Logout}
            onClick={() => {
              localStorage.removeItem('token');
              window.location.replace(window.location.origin);
              sessionStorage.clear();
              mixpanel.track('Logout', {
                UserId: decoded?.userId,
              });
              mixpanel.reset();
            }}
          >
            {'Logout'}
          </Dropdown.Item>
        </Dropdown> */}
      </div>
    </div>
  );
}
