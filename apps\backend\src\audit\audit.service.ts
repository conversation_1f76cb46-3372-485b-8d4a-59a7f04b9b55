// audit.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { AuditLog } from './entity/audit-log.entity';
import { CreateAuditLogDto } from './dto/create-audit.dto';

@Injectable()
export class AuditService {
  constructor(
    @InjectRepository(AuditLog)
    private readonly auditRepository: Repository<AuditLog>
  ) {}

  async createAuditLog(
    user: any,
    mineName: string,
    action: string,
    changedFor: string,
    changedForRole: string
  ): Promise<AuditLog> {
    const createAuditLogDto: CreateAuditLogDto = {
      mineName: mineName,
      createdBy: user.firstname + ' ' + user.lastname,
      createdByRole: user.role,
      createdFor: changedFor,
      createdForRole: changedForRole,
      action: action,
    };
    const auditLog = this.auditRepository.create(createAuditLogDto);
    return this.auditRepository.save(auditLog);
  }
  async findall(startDate: string, endDate: string): Promise<AuditLog[]> {
    const eDate = new Date(endDate);
    eDate.setHours(23, 59, 59);

    const sDate = new Date(startDate);
    sDate.setHours(0, 0, 0);
    
    return await this.auditRepository.find({
      where: {
        createDatetime: Between(sDate, eDate),
      },
      order: { createDatetime: 'DESC' },
    });
  }
}
