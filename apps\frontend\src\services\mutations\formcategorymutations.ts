import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  addFormCategory,
  deleteFormCategory
} from '../../api/forms/formcategoryapis';

export function useAddFormCategory() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: {name: string}) => addFormCategory(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-form-category'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-categories'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useDeleteFormCategory() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteFormCategory(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['delete-form-category'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-categories'].includes(query.queryKey[0]);
        }
      });
    },
  });
}