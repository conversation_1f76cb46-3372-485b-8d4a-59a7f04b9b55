import { useMemo, useRef, useState } from "react";
import { PlusIcon } from "../../../../../assets/icons/icons";
import GroupQuestion from "./GroupQuestion";
import Property from "./Property";
import { Group, Id, Question } from "../../types";
import { expandItemProperties } from "../../utilities";

interface Props {
	group: Group;
	siblings: any;
	updateGroup: (group: Group) => void;
}

export default function GroupProperties({ group, siblings, updateGroup }: Props) {
	const addQuestionRef = useRef<HTMLButtonElement>(null);
	const [focusQuestionByIndex, setFocusQuestionByIndex] = useState(0);
	const [activeTab, setActiveTab] = useState('properties');
	const questions = group?.questions ?? [];

	const properties = useMemo(() => {
		return group?.properties ?? {}
	}, [group]);

	const expandedProperties = useMemo(() => {
		const currentItem = {id: group.id, group};
		return expandItemProperties(currentItem)
	}, [properties]);

	function clearGroupValue(parent:string) {
		let parentProp = expandedProperties[parent];
		let propGroup = parentProp.propGroup;
		for(let key in propGroup) {
			delete propGroup[key].value;
			if(properties[parentProp.name]) {
				delete properties[parentProp.name][key];
			}
		}
		delete parentProp.value;
		delete properties[parentProp.name].value;
		updateGroup(group);
	}
	
	function handleUpdatePropValue(name:string, newValue:any, parent?:string, promote?:boolean) {
		let property = expandedProperties[name];
		if(parent) {
			let parentProp = expandedProperties[parent];
			if(!properties[parentProp.name]) {
				properties[parentProp.name] = {};
			}
			let itemGroup = parentProp.propGroup ?? parentProp.buttonGroup ?? null;
			if(itemGroup) {
				property = itemGroup[name];
				if(parentProp.buttonGroup) {
					properties[parentProp.name] = newValue;
				}
				else if(promote === true) {
					//parentProp.value = newValue;
					properties[parentProp.name].value = newValue;
				}
				if(parentProp.overflow) {
					for(let i = 0; i < parentProp.overflow.length; i++) {
						let prop = expandedProperties[parentProp.overflow[i]];
						if(newValue !== 'individual') {
							if(prop.propGroup) {
								//prop.propGroup[name].value = newValue;
								properties[prop.name][name] = newValue;
							}
							else {
								prop.value = newValue;
								if(!properties[prop.name]) {
									properties[prop.name] = {};
								}
								properties[prop.name][name] = newValue;
							}
						}
					}
				}
			}
			if(!parentProp.buttonGroup) {
				properties[parentProp.name][property.name] = newValue;
			}
		}
		else {
			properties[property.name] = newValue;
		}
		//property.value = newValue;
		updateGroup(group);
	}

	function shouldDisplay(property:any, parent?:any) {
		let parentValue = property.value ?? property.defaultValue ?? null;
		if(parent) {
			parentValue = parent.value ?? parent.defaultValue ?? null;
		}
		const condition = property.condition ?? null;
		const dependency = property.dependency ?? null;
		let display = true;

		if(condition && dependency) {
			if(dependency === 'parent') {
				if(condition[parentValue] !== 'display') {
					display = false;
				}
			}
			else if(expandedProperties[dependency]) {
				const depField = expandedProperties[dependency];
				const depValue = depField.value ?? depField.defaultValue ?? null;
				if(condition[depValue] !== 'display') {
					display = false;
				}
			}
		}
		if(condition?.questionLength == '> 0' && group.questions.length <= 0) {
			display = false;
		}
		return display;
	}

	function addQuestionToGroup(value?: string) {
		const newId = `question-${group.id}-${questions.length}`;
		const questionToAdd: Question = {
			id: newId,
			index: questions.length,
			groupId: group.id,
			name: value ?? '',
		}
		questions.push(questionToAdd);
		group.questions = questions;
		updateGroup(group);
		setFocusQuestionByIndex(questions.length);
	}

	function updateGroupQuestion(id: Id, value: string) {
		const questIndex = questions.findIndex(quest => quest.id === id);
		const quest = questions[questIndex];
		quest.name = value;
		group.questions = questions;
		updateGroup(group);
	}

	function deleteGroupQuestion(id: Id) {
		group.questions = questions.filter(quest => quest.id !== id);
		updateGroup(group);
	}
	
	return (
		<div className="relative">
			<h1 className="text-center text-sm py-1 opacity-80">{group.name}</h1>
			<div className="w-full flex justify-center py-2">
				<div className={`
						text-center text-lg hover:opacity-80 cursor-pointer mr-2
						${activeTab == 'properties' ? 'opacity-100' : 'opacity-50'}
					`}
					onClick={() => setActiveTab('properties')}
				>
					Properties
				</div>
				<div className={`
						text-center text-lg hover:opacity-80 cursor-pointer ml-2
						${activeTab == 'history' ? 'opacity-100' : 'opacity-50'}
					`}
					onClick={() => setActiveTab('questions')}
				>
					Questions
				</div>
			</div>
			{activeTab == 'questions' &&
				<div className="grid grid-cols-8 gap-2 items-center mb-4">
					{questions && questions.map((q:Question, index) => {
						return(
							<GroupQuestion
								key={index}
								id={q.id}
								label={(index+1).toString()}
								value={q.name ?? ''}
								currentIndex={index}
								focusIndex={focusQuestionByIndex}
								setFocusQuestionByIndex={setFocusQuestionByIndex}
								updateQuestion={updateGroupQuestion}
								deleteQuestion={deleteGroupQuestion}
								addQuestion={addQuestionToGroup}
							/>
						)
					})}
					<GroupQuestion
						id="temp-id"
						label={(questions.length+1).toString()}
						value={''}
						currentIndex={questions.length}
						focusIndex={focusQuestionByIndex}
						setFocusQuestionByIndex={setFocusQuestionByIndex}
						updateQuestion={updateGroupQuestion}
						deleteQuestion={deleteGroupQuestion}
						addQuestion={addQuestionToGroup}
					/>
				</div>
			}
			{activeTab == 'properties' && Object.keys(expandedProperties)?.length > 0 && Object.keys(expandedProperties).map((p:any, index) => {
				const property = expandedProperties[p];
				const propType = property.propType ?? '';
				const name = property.name ?? null;
				const value = property.value ?? property.defaultValue ?? null;
				const options = property.options ?? null;
				const label = property.label ?? null;
				const icon = property.icon ?? null;
				const action = property.action ?? null;
				const classes = property.classes ?? '';

				if(shouldDisplay(property)) {
					if(propType == 'propGroup') {
						const parentValue = property.value ?? property.defaultValue ?? null;
						const parentName = property.name ?? null;
						let propGroup = property.propGroup ?? null;

						if(propGroup && propGroup[parentValue]) {
							propGroup = propGroup[parentValue];
						}

						if(propGroup) {
							let basisCount = 0;
							for(let key in propGroup) {
								const g = propGroup[key];
								if(shouldDisplay(g, property)) {
									basisCount++;
								}
							}
							
							return (
								<div key={index} className="my-1">
									{label ? 
										<span className="text-white text-[14px]">{label}</span> :
										<div className="flex flex-row gap-x-2">
											{Object.keys(propGroup).map((_g:any, index) => {
												const g = propGroup[_g];
												if(shouldDisplay(g, property)) {
													return (
														<div key={`pg-label-${g.name}-${index}`} className={`basis-${(1/basisCount)*100} text-center`}>
															<span className="text-white text-[14px]">{g?.label}</span>
														</div>
													)
												}
											})}
										</div>
									}
									<div className="flex flex-row gap-x-2">
										{Object.keys(propGroup).map((_g:any, index) => {
											const g = propGroup[_g];
											if(shouldDisplay(g, property)) {
												const promote = g.promote ?? false;
												
												return (
													<div key={`pgc-prop-${g.name}-${index}`} className={`basis-${(1/basisCount)*100}`}>
														<Property
															updatePropValue={handleUpdatePropValue}
															clearGroupValue={clearGroupValue}
															key={`pg-prop-${g.name}-${index}`}
															id={`${g.name}-${index}`} propType={g.propType} name={g.name} parent={parentName}
															value={g.value ?? g.defaultValue} options={g.options} promote={promote}
															icon={g.icon} action={g.action} classes={g.classes} />
													</div>
												)
											}
										})}
									</div>
								</div>
							)
						}
					}
					if(propType == 'buttonGroup') {
						const buttonGroup = property.buttonGroup ?? null;
						const parentName = property.name ?? null;
						const parentValue = property.value ?? property.defaultValue ?? null;
						const classes = property.classes ?? '';
						return (
							<div key={index} className="my-1">
								<div className="flex flex-row px-6 pt-2">
									{Object.keys(buttonGroup).map((_b:any) => {
										const b = buttonGroup[_b];
										return (
											<div key={`label-${b.name}-${index}`} className={`w-1/${(Object.keys(buttonGroup).length)} text-center`}>
												<span className="text-white text-[14px]">{b?.label}</span>
											</div>
										)
									})}
								</div>
								<div className="flex flex-row px-6 pb-2">
									{Object.keys(buttonGroup).map((_b:any) => {
										const b = buttonGroup[_b];
										const promote = b.promote ?? false;
										return (
											<div key={`prop-${b.name}-${index}`} className={`w-1/${(Object.keys(buttonGroup).length)}`}>
												<Property
													updatePropValue={handleUpdatePropValue}
													clearGroupValue={clearGroupValue}
													key={`prop-${b.name}-${index}`} promote={promote}
													id={`${b.name}-${index}`} propType={b.propType} name={b.name}
													value={parentValue} icon={b.icon} parent={parentName} classes={b.classes} />
											</div>
										)
									})}
								</div>
							</div>
						)
					}
					return (
						<div key={`container-${name}-${index}`} className="my-1">
							<Property
								updatePropValue={handleUpdatePropValue}
								clearGroupValue={clearGroupValue}
								key={`prop-${name}-${index}`}
								id={`${name}-${index}`} propType={propType} name={name}
								value={value} options={options} label={label} icon={icon} action={action} classes={classes} />
						</div>
					)
				}
			})}
		</div>
	);
};