import { useEffect, useMemo, useState } from 'react';
import Table, { ColumnDef } from '../../common/Table';
import CommonChartForReport from './reportChartForGas';
import { CSVIcon } from '../../../../../src/assets/icons/icons';
import CommonChartForReportForVentilation from './reportChartForVentilation';
import CommonChartSync from './commonSyncChart';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../../PageName';
import { useParams } from 'react-router-dom';
import decodeJWT from '../../../../utils/jwtdecoder';

const data = [
  {
    label: '7 Scoop South Charger',
    id: 'A091',
    diffPressure: '2.45',
    compPressure: '5.12',
    airVelocityAvg: '198',
    airFlowAvg: '27,890',
    gases: ['Airflow', 'Air Velocity', 'Diff. Pressure', 'Comp. Pressure'],
    values: [
      [2890, 2305, 3120, 2550, 1850, 2750],
      [203, 196, 199, 204, 188, 196],
      [5.1, 4.95, 3.02, 5.95, 3.05, 5.18],
      [1.9, 3.1, 4.0, 1.8, 1.0, 1.7],
    ],
  },
  {
    label: '7Y S North XC-12',
    id: 'A092',
    diffPressure: '3.12',
    compPressure: '4.89',
    airVelocityAvg: '195',
    airFlowAvg: '28,320',
    gases: ['Airflow', 'Air Velocity', 'Diff. Pressure', 'Comp. Pressure'],
    values: [
      [2910, 2280, 3105, 2570, 1820, 2785],
      [202, 195, 198, 206, 186, 195],
      [5.2, 4.85, 3.12, 6.0, 3.0, 5.1],
      [2.0, 3.3, 4.2, 1.6, 0.8, 1.9],
    ],
  },
  {
    label: '7Y 5 North XC-12',
    id: 'A093',
    diffPressure: '2.98',
    compPressure: '5.22',
    airVelocityAvg: '199',
    airFlowAvg: '28,410',
    gases: ['Airflow', 'Air Velocity', 'Diff. Pressure', 'Comp. Pressure'],
    values: [
      [2885, 2310, 3110, 2590, 1835, 2760],
      [204, 198, 201, 203, 189, 198],
      [5.25, 4.92, 3.05, 6.1, 3.1, 5.3],
      [1.7, 3.4, 4.3, 1.9, 0.7, 1.6],
    ],
  },
  {
    label: 'Main West Belt XC-6',
    id: 'A094',
    diffPressure: '3.05',
    compPressure: '5.08',
    airVelocityAvg: '196',
    airFlowAvg: '28,200',
    gases: ['Airflow', 'Air Velocity', 'Diff. Pressure', 'Comp. Pressure'],
    values: [
      [2920, 2295, 3095, 2605, 1845, 2775],
      [200, 197, 199, 205, 187, 197],
      [5.31, 4.91, 3.09, 6.01, 3.01, 5.2],
      [1.8, 3.2, 4.1, 1.7, 0.9, 1.8],
    ],
  },
  {
    label: '7Y 5 North XC-3',
    id: 'A095',
    diffPressure: '3.00',
    compPressure: '5.14',
    airVelocityAvg: '197',
    airFlowAvg: '28,450',
    gases: ['Airflow', 'Air Velocity', 'Diff. Pressure', 'Comp. Pressure'],
    values: [
      [2900, 2300, 3100, 2610, 1805, 2790],
      [201, 196, 200, 202, 190, 198],
      [5.15, 4.88, 3.08, 5.98, 3.02, 5.25],
      [1.9, 3.1, 4.0, 1.8, 1.1, 1.7],
    ],
  },
  {
    label: '7 Scoop North Charger',
    id: 'A096',
    diffPressure: '3.21',
    compPressure: '5.31',
    airVelocityAvg: '197',
    airFlowAvg: '28,498',
    gases: ['Airflow', 'Air Velocity', 'Diff. Pressure', 'Comp. Pressure'],
    values: [
      [2902, 2298, 3100, 2600, 1800, 2800],
      [201, 197, 200, 205, 187, 197],
      [5.31, 4.91, 3.09, 6.01, 3.01, 5.2],
      [1.8, 3.2, 4.1, 1.7, 0.9, 1.8],
    ],
  },
  {
    label: '7Y 3 North XC-22',
    id: 'A097',
    diffPressure: '2.88',
    compPressure: '5.05',
    airVelocityAvg: '198',
    airFlowAvg: '28,370',
    gases: ['Airflow', 'Air Velocity', 'Diff. Pressure', 'Comp. Pressure'],
    values: [
      [2875, 2320, 3090, 2585, 1815, 2765],
      [205, 199, 202, 204, 188, 199],
      [5.05, 4.93, 3.06, 6.02, 3.06, 5.15],
      [1.6, 3.0, 4.4, 1.6, 1.0, 1.6],
    ],
  },
];

const ReportVentilation = () => {
  let decoded = decodeJWT();
  const params = useParams();
  const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [fileName, setFileName] = useState('');
  const [sync, setSync] = useState(false);
  const selectedGases = data?.filter((ele: any) =>
    selectedRowIds?.includes(ele?.id)
  );
  const uniqueGases = useMemo(() => {
    return [...new Set(selectedGases.flatMap((item) => item.gases))];
  }, [selectedGases]);
  const [gasStates, setGasStates] = useState<Record<string, boolean>>({});

  useEffect(() => {
    setGasStates((prev) => {
      let updated = { ...prev };
      let changed = false;

      uniqueGases.forEach((gas) => {
        if (!(gas in updated)) {
          updated[gas] = true;
          changed = true;
        }
      });

      return changed ? updated : prev; // prevent state update if no changes
    });
  }, [uniqueGases]);

  const handleToggle = (gas: string) => {
    setGasStates((prev: any) => ({
      ...prev,
      [gas]: !prev[gas],
    }));
  };

  console.log(uniqueGases);
  const handleCheckboxChange = (row: any) => {
    setSelectedRowIds((prev) => {
      if (prev.includes(row.id)) {
        return prev.filter((id) => id !== row.id);
      } else {
        return [...prev, row.id];
      }
    });
    mixpanel.track('Sensor Check', {
      Page_Name: getPageNamesFromUrl(
        params['*'] ?? 'Atmosphere Ventilation Report'
      ),
      MineName: decoded?.minename,
    });
  };
  const columns: ColumnDef[] = [
    {
      key: 'status',
      label: `<div class="inline-flex items-center pt-1">
    <label
      class="relative flex items-center cursor-pointer"
      htmlFor="selectAll">
    <input type="checkbox" id="selectAll" class="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] checked:before:bg-gray-900 hover:before:opacity-10"/>
    <span class="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-3 w-3"
          viewBox="0 0 20 20"
          fill="#1A252F"
          stroke="#1A252F"
          stroke-width="1"
        >
          <path
            fillRule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clipRule="evenodd"
          ></path>
        </svg>
      </span>
    </label>
  </div>`,
      type: 'sr_no',
      render: (row: any) => (
        <div className="inline-flex items-center pt-1">
          <label
            className="relative flex items-center cursor-pointer"
            htmlFor="check"
          >
            <input
              type="checkbox"
              className="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] checked:before:bg-gray-900 hover:before:opacity-10"
              id="selectCheckbox"
              onChange={() => {
                handleCheckboxChange(row);
              }}
            />
            <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                viewBox="0 0 20 20"
                fill="#1A252F"
                stroke="#1A252F"
                strokeWidth="1"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </span>
          </label>
        </div>
      ),
    },
    { key: 'label', label: 'Label', type: 'text' },
    { key: 'id', label: 'ID', type: 'text' },
    {
      key: 'airFlowAvg',
      label: 'Avg. Airflow',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.airFlowAvg}</div>
          <div className="text-[12px] text-white/50 float-right">CFM</div>
        </div>
      ),
    },
    {
      key: 'airVelocityAvg',
      label: 'Avg. Air Velocity',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.airVelocityAvg}</div>
          <div className="text-[12px] text-white/50 float-right">ft/min</div>
        </div>
      ),
    },
    {
      key: 'compPressure',
      label: 'Comp. Pressure',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.compPressure}</div>
          <div className="text-[12px] text-white/50 float-right">in. H20</div>
        </div>
      ),
    },
    {
      key: 'diffPressure',
      label: 'Diff. Pressure',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.diffPressure}</div>
          <div className="text-[12px] text-white/50 float-right">in. H20</div>
        </div>
      ),
    },
  ];

  const downloadCSV = () => {
    const csvRows = [];

    // Add headers
    const headers = [
      'Label',
      'ID',
      'Avg. Airflow',
      'Avg. Air Velocity',
      'Comp. Pressure',
      'Diff. Pressure',
      'Alarms',
    ];
    csvRows.push(headers.join(','));

    // Add data
    data.forEach((row) => {
      const values = [
        row.label,
        row.id,
        row.airFlowAvg,
        row.airVelocityAvg,
        row.compPressure,
        row.diffPressure,
      ];
      csvRows.push(values.join(','));
    });

    const blob = new Blob([csvRows.join('\n')], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };
  return (
    <div className="px-[4%]">
      <div className="text-white overflow-hidden">
        {sync &&
        Object.keys(gasStates).filter((key) => gasStates[key])?.length == 2 &&
        selectedRowIds?.length != 0 ? (
          <>
            <CommonChartSync
              data={{
                row:
                  selectedGases?.map((ele) => ({
                    label: ele?.label,
                    values: [...ele?.values],
                    gases: ele?.gases,
                  })) || [],
              }}
              gases={Object.keys(gasStates).filter((key) => gasStates[key])}
            />
          </>
        ) : (
          <CommonChartForReportForVentilation
            data={{
              row:
                selectedGases?.map((ele) => ({
                  label: ele?.label,
                  values: [...ele?.values],
                  gases: ele?.gases,
                })) || [],
            }}
            gases={
              selectedRowIds?.length != 0
                ? Object.keys(gasStates).filter((key) => gasStates[key])
                : []
            }
          />
        )}
      </div>
      <div className="pt-8 pb-2 userMTableBg px-5 rounded-xl">
        {' '}
        <div className="flex justify-between items-start gap-4 mb-4">
          {/* Left section: Gas buttons and Sync */}
          <div className="flex gap-4 flex-wrap">
            {uniqueGases.map((gas) => (
              <button
                key={gas}
                className={`text-white px-4 py-2 rounded-lg text-sm font-medium  ${
                  gasStates[gas] ? 'bg-[#FFB132]' : ''
                }`}
                onClick={() => {
                  handleToggle(gas);

                  mixpanel.track('Report Filter Module', {
                    Module_Name: gas,
                    Page_Name: getPageNamesFromUrl(
                      params['*'] ?? 'Atmosphere Ventilation Report'
                    ),
                  });
                }}
              >
                {gas}
              </button>
            ))}

            {uniqueGases.length > 1 && (
              <button
                onClick={() => {
                  setSync(!sync);

                  mixpanel.track('Module Sync', {
                    Module_Name: "sync",
                    Page_Name: getPageNamesFromUrl(
                      params['*'] ?? 'Atmosphere Ventilation Report'
                    ),
                  });
                }}
                className={`${
                  sync
                    ? 'bg-[#FFB132] border-none'
                    : 'border-[2px] border-dashed py-[6px] px-[30px]'
                } text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline`}
              >
                Sync
              </button>
            )}
          </div>

          {/* Right section: Export button */}
          <div
            className="flex items-center text-[16px] text-[#FFB132] gap-2 cursor-pointer "
            onClick={() => {
              mixpanel.track('CSV Export', {
                // PageName: 'Atmosphere Ventilation Report',
                Page_Name: getPageNamesFromUrl(
                  params['*'] ?? 'Atmosphere Ventilation Report'
                ),
              });

              setShowModal(true);
            }}
          >
            <CSVIcon /> Export to CSV
          </div>
        </div>
        <Table
          columns={columns}
          data={data}
          searchText={''}
          searchOnColumn=""
          backgroundColor={false}
          scrollable={true}
          sortable={true}
          dataRenderLimitMdScreen={4}
          dataRenderLimitLgScreen={5}
          tableHeightClassLg={`h-[240px]`}
          tableHeightClassMd={`h-[240px]`}
        />
      </div>
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[#0F0F0F] text-white p-4 rounded-xl w-[25%] shadow-xl">
            <h2 className="text-xl font-bold mb-4 text-center">
              Export to CSV
            </h2>

            <label className="block mb-2 font-semibold">File Name</label>
            <input
              type="text"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              className="w-full px-4 py-2 rounded-md text-black focus-none outline-none"
              placeholder="Enter file name"
            />

            <div className="flex justify-between mt-6 w-full">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 w-[48%] border border-blue-500 text-white rounded-md hover:bg-blue-500"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  downloadCSV();
                  setShowModal(false);
                }}
                className="px-4 py-2 w-[48%] bg-blue-500 hover:bg-blue-600 text-white rounded-md"
              >
                Export Data
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default ReportVentilation;
