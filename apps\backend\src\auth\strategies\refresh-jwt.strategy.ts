import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { jwtConstants } from '../constant';
import { UsersService } from '../../users/users.service';

@Injectable()
export class RefreshJwtStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh'
) {
  constructor(private userService: UsersService) {
    super({
      jwtFromRequest: ExtractJwt.fromBodyField('refreshToken'),
      igonreExpiration: false,
      secretOrKey: jwtConstants.accessTokenSecret,
    });
  }
  async validate(payload: any) {
    const existingUser = await this.userService.findUserById(payload.userId);

    if (!existingUser || !existingUser.isActive || existingUser.isDelete) {
      throw new UnauthorizedException();
    }

    return payload;
  }
}
