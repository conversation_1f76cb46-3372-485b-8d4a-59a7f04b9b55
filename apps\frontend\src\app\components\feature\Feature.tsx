import { useEffect, useState } from 'react';
import {
  useFeatureData,
  useRoleFeatureData,
  useRoles,
} from '../../../services/queries/rolefeaturequeries';
import {
  useAddRoleFeature,
  useDeleteRoleFeature,
} from '../../../services/mutations/reolefature';
import decodeJWT from '../../../utils/jwtdecoder';
import { getFeatures } from '../../../api/users/userapis';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import Loader from '../common/Loader';
import mixpanel from 'mixpanel-browser';
import { useParams } from 'react-router-dom';
import { getPageNamesFromUrl } from '../PageName';
import { UserMenu } from '../common/UserMenu';

export default function Feature() {
  const [features, setFeatures] = useState<any>();
  const [roles, setRoles] = useState<any>();
  const [roleFeatures, setRoleFeture] = useState<any>();
  const [checkChange, setCheckChange] = useState();

  const feature = useFeatureData();
  const { data: role } = useRoles();
  const roleFeature = useRoleFeatureData();
  const addRoleFeature = useAddRoleFeature();
  const deleteRoleFeature = useDeleteRoleFeature();
  const params = useParams();
  const url = getPageNamesFromUrl(params['*']);

  const mshaId = decodeJWT()?.mshaId;
  const { data: featuresSideBarData, isLoading } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    setFeatures(feature?.data);
    setRoles(
      role?.data.filter(
        (x: any) => x.name != (decodeJWT()?.role === 'admin' ? 'admin' : '')
      )
    );
    setRoleFeture(roleFeature?.data);
  }, [feature, role, roleFeature, checkChange]);

  const toggleRowSelection = (e: any, roleId: number, featureId: number) => {
    if (e.currentTarget.checked) {
      try {
        addRoleFeature.mutateAsync({ roleId, featureId });
        mixpanel.track('Add Feature', {
          MshaId: mshaId,
        });
      } catch (err: any) {
        mixpanel.track('Error Event', {
          Page_Name: url,
          Action_Name: 'Add Feature',
        });
      }
    } else {
      try {
        const feature = roleFeatures?.filter(
          (z: any) => z.role_id == roleId && z.feature_id == featureId
        );
        if (feature) deleteRoleFeature.mutateAsync(feature[0].roleFeatureId);
        mixpanel.track('Remove Feature', {
          MshaId: mshaId,
        });
      } catch (err: any) {
        mixpanel.track('Error Event', {
          Page_Name: url,
          Action_Name: 'Remove Feature',
        });
      }
    }
    setCheckChange(e.currentTarget.checked);
  };

  useEffect(() => {
    if (localStorage.getItem('refreshingPage')) {
      localStorage.removeItem('refreshingPage');
      setTimeout(() => {
        toast.info(
          `Your Role has been updated to ${
            decodeJWT()?.role.charAt(0).toUpperCase() +
            decodeJWT()?.role.slice(1)
          }`
        );
      }, 1000);
    }
  });

  if (
    featuresSideBarData?.data.some(
      (feature: any) => feature.FeatureName == 'Features'
    ) ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <div className="w-full">
        <div className="sticky w-full top-0 z-30  box  bg-top px-10 2xl:px-16">
          <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe]  pb-4 pt-4 ">
            {/* <div className="relative"> */}
            {/* <div className="relative    w-full"></div> */}
            <div className="">
              <h6 className=" font-bold text-white text-[32px] text-left ">
                Features
              </h6>
            </div>
            <div className="">
              {/* <h6 className="p-2 font-bold text-white text-[32px] text-right ">
                Settings
              </h6> */}
              <div className="flex justify-end items-center -mr-10 relative">
                <h6 className=" font-bold text-white text-[32px]">Settings</h6>
                <span className="ml-4">
                  <UserMenu />
                </span>
              </div>
            </div>
            {/* </div> */}
            {/* <div className="grid grid-cols-2 w-full">
              
            </div> */}
          </div>
        </div>
        <div className="mt-8  px-10 2xl:px-16">
          {isLoading ? (
            <div>
              <div>
                <div className="flex justify-center items-center h-full pt-[200px] white">
                  {<Loader />}
                </div>
                <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                  Loading....
                </div>
              </div>
            </div>
          ) : (
            <div className="pt-8 pb-2 userMTableBg px-5 rounded-xl">
              <div className="flex justify-between">
                <div className="block">
                  <h6 className="font-semibold text-[20px] text-white">{`Features (${
                    feature?.data?.length ? feature?.data?.length : 0
                  })`}</h6>
                  <p className="font-normal my-1 text-[14px] tracking-normal leading-5 text-[#cccccc] ">
                    Manage features for roles
                  </p>
                </div>
                <div></div>
              </div>
              <div className="w-[100%] pt-5">
                <table className={`min-w-full`}>
                  <thead className={`sticky top-0 bg-[#21303C] z-10`}>
                    <tr>
                      <th className="px-4 py-1 text-white text-[14px] font-normal capitalize  ">
                        <div
                          className="flex items-center"
                          style={{ minHeight: '2.5rem' }}
                        >
                          Feature
                        </div>
                      </th>
                      {roles?.map((y: any) => (
                        <th key={y.id} className="px-4 py-1 text-white text-[14px] font-normal capitalize ">
                          <div
                            className="flex items-center justify-center"
                            style={{ minHeight: '2.5rem' }}
                          >
                            {y.name}
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="table-fixed w-full ">
                    {features?.map((x: any) => (
                      <tr key={x.id} className="h-10 text-white  border-[#4aa8fe40]">
                        <td className="pl-5">{x.name}</td>
                        {roles?.map((y: any) => (
                          <td key={y.id} className="text-center">
                            <div className="inline-flex items-center pt-1">
                              <label
                                className="relative flex items-center cursor-pointer"
                                htmlFor="check"
                              >
                                <input
                                  type="checkbox"
                                  checked={
                                    roleFeatures?.filter(
                                      (z: any) =>
                                        z.role_id == y.id &&
                                        z.feature_id == x.id
                                    ).length > 0
                                  }
                                  className="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] disabled:bg-gray-400 disabled:cursor-not-allowed checked:before:bg-gray-900 hover:before:opacity-10"
                                  id="selectCheckbox"
                                  onChange={(e) => {
                                    toggleRowSelection(e, y.id, x.id);
                                  }}
                                  disabled={
                                    (y.name == 'manager' || y.name == 'user') &&
                                    (x.name == 'Features' || x?.name == 'Users') ||
                                    (y.name == 'user' && x.name == 'Templates')
                                      ? true
                                      : false
                                  }
                                />
                                <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-3 w-3"
                                    viewBox="0 0 20 20"
                                    fill="#1A252F"
                                    stroke="#1A252F"
                                    strokeWidth="1"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                      clipRule="evenodd"
                                    ></path>
                                  </svg>
                                </span>
                              </label>
                            </div>
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  } else {
    return '';
  }
}
