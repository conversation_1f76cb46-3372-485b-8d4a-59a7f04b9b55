// audit-log.entity.ts
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';

@Entity()
export class AuditLog {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @CreateDateColumn({ name: 'create_date' })
  createDatetime: Date;

  @Column({ name: 'mine_name' })
  mineName: string;

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'created_by_role' })
  createdByRole: string;

  @Column({ name: 'created_for' })
  createdFor: string;

  @Column({ name: 'created_for_role' })
  createdForRole: string;

  @Column({ name: 'action' })
  action: string;
}
