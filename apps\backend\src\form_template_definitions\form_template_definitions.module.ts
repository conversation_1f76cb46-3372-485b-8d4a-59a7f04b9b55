import { Module } from '@nestjs/common';
import { FormTemplateDefinitionsController } from './form_template_definitions.controller';
import { FormTemplateDefinitionsService } from './form_template_definitions.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FormTemplateDefinition } from './entities/form_template_definitions.entity';
import { RolesModule } from '../roles/roles.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    RolesModule,
    UsersModule,
    TypeOrmModule.forFeature([FormTemplateDefinition])
  ],
  controllers: [FormTemplateDefinitionsController],
  providers: [FormTemplateDefinitionsService],
  exports: [FormTemplateDefinitionsService],
})
export class FormTemplateDefinitionsModule {}
