import { Test, TestingModule } from '@nestjs/testing';
import { FormTemplatesService } from './form_templates.service';
import { FormTemplate } from './entities/form_template.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CreateFormTemplateDto } from './dto/create-form-template.dto';
import { UpdateFormTemplateDto } from './dto/update-form-template.dto';

describe('FormTemplatesService', () => {
  let service: FormTemplatesService;

  const mockFormTemplateRepository = {
    save: jest.fn(),
    find: jest.fn(),
    findOneBy: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FormTemplatesService,
        {
          provide: getRepositoryToken(FormTemplate),
          useValue: mockFormTemplateRepository,
        },
      ],
    }).compile();

    service = module.get<FormTemplatesService>(FormTemplatesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('create => Should create a new formTemplate and return its data', async () => {
    // arrange
    const createFormTemplateDto = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    } as CreateFormTemplateDto;

    const formTemplate = {
      id: 1,
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    } as FormTemplate;

    jest.spyOn(mockFormTemplateRepository, 'save').mockReturnValue(formTemplate);

    // act
    const result = await service.create(createFormTemplateDto);

    // assert
    expect(mockFormTemplateRepository.save).toHaveBeenCalled();
    expect(mockFormTemplateRepository.save).toHaveBeenCalledWith(createFormTemplateDto);

    expect(result).toEqual(formTemplate);
  });

  it('findAll => should return an array of formTemplates', async () => {
    // arrange
    const formTemplate = {
      id: 1,
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    };
    let mineId = 1;
    const formTemplates = [formTemplate];
    jest.spyOn(mockFormTemplateRepository, 'find').mockReturnValue(formTemplates);

    // act
    const result = await service.findAll(mineId);

    // assert
    expect(mockFormTemplateRepository.find).toHaveBeenCalled();
    expect(result).toEqual(formTemplates);
  });

  it('findOne => should find a formTemplate by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const formTemplate = {
      id: 1,
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    };

    jest.spyOn(mockFormTemplateRepository, 'findOneBy').mockReturnValue(formTemplate);

    // act
    const result = await service.findOne(id);

    // assert
    expect(mockFormTemplateRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormTemplateRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(result).toEqual(formTemplate);
  });

  it('update => should find a formTemplate by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateFormTemplateDto = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    } as UpdateFormTemplateDto;
    const formTemplate = {
      id: 1,
      mineId: 1,
      formCategoryId: 1,
      name: 'Shift Production Report',
      description: 'Shift Production Report',
    };

    const updatedFormTemplate = Object.assign(formTemplate, updateFormTemplateDto);
    jest.spyOn(mockFormTemplateRepository, 'save').mockReturnValue(updatedFormTemplate);

    // act
    const result = await service.update(id, formTemplate);

    // assert
    expect(mockFormTemplateRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormTemplateRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockFormTemplateRepository.save).toHaveBeenCalled();
    expect(mockFormTemplateRepository.save).toHaveBeenCalledWith(updatedFormTemplate);
    expect(result).toEqual(updatedFormTemplate);
  });

  it('remove => should find a formTemplate by a given id, remove and then return Number of affected rows', async () => {
    // arrange
    const id = 1;
    const formTemplate = {
      id: 1,
      mineId: 1,
      formCategoryId: 1,
      name: 'Shift Production Report',
      description: 'Shift Production Report',
    };

    jest.spyOn(mockFormTemplateRepository, 'remove').mockReturnValue(formTemplate);

    // act
    const result = await service.remove(id);

    // assert
    expect(mockFormTemplateRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormTemplateRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockFormTemplateRepository.remove).toHaveBeenCalled();
    expect(mockFormTemplateRepository.remove).toHaveBeenCalledWith(id);
    expect(result).toEqual(formTemplate);
  });
});
