import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  useAddFormTemplate,
  useEditFormTemplate,
  useCloneFormTemplate,
  useCheckTemplateNameMutation,
} from '../../../../services/mutations/formtemplatemutations';
import { getFormCategories } from '../../../../api/forms/formcategoryapis';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import decodeJWT from '../../../../utils/jwtdecoder';
import { CloseIcon } from '../../../../assets/icons/icons';
import { getPageNamesFromUrl } from '../../PageName';
import { useParams } from 'react-router-dom';
import { useAddFormCategory } from '../../../../services/mutations/formcategorymutations';

const schema = yup.object({
  name: yup
    .string()
    .max(100, 'Name should not exceed 100 characters')
    .required('Please enter a form name'),
  description: yup
    .string()
    .max(200, 'Description should not exceed 200 characters')
    .required('Please enter a form description'),
  formCategory: yup.string().required('Please select a form category'),
  newCategory: yup.string()
});

interface Props {
	cloneForm: boolean;
	setCloneForm: (value: boolean) => void;
	editData: any;
	setOpenAddFormTemplateForm: (value: boolean) => void;
}

export default function AddFormTemplateForm({
	cloneForm, setCloneForm,
	editData, setOpenAddFormTemplateForm,
}: Props) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    getValues,
    setError,
    clearErrors,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const addFormTemplateData = useAddFormTemplate();
  const editFormTemplate = useEditFormTemplate();
  const cloneFormTemplate = useCloneFormTemplate();
  const addFormCategory = useAddFormCategory();
  const checkTemplateNameMutation = useCheckTemplateNameMutation();
  const [categoryValue, setCategoryValue] = useState('');
  const decoded = decodeJWT();
  const params = useParams();
  const url = getPageNamesFromUrl(params['*'] ?? '');

  const { data, isLoading } = useQuery({
    queryKey: ['form-categories'],
    queryFn: getFormCategories,
    refetchOnWindowFocus: false,
  });
  const listFormCategories = data?.data;

  const storeFormTemplate = async (data: any) => {
    if(cloneForm) {
      data.cloneId = editData?.formTemplateId;
      data.formCategoryId = data?.formCategory;
      data.formCategoryId = data?.formCategory;
      delete data.formCategory;

      try {
        const res = await cloneFormTemplate.mutateAsync(data);
        toast.success('Form Template cloned successfully');
        reset();
        setCloneForm(false);
        setOpenAddFormTemplateForm(false);
      } catch(err: any){
        console.error('err',err);
      }
    }
    else {
      data.id = editData?.formTemplateId;
      data.formCategoryId = data?.formCategory;
      data.formCategoryId = data?.formCategory;
      delete data.formCategory;

      if(data.formCategoryId == '-1' && data.newCategory) {
        const res = await addFormCategory.mutateAsync({name: data.newCategory});
        if (res?.status == 200 || res?.status == 201) {
          data.formCategoryId = res?.data.id;
          delete data.newCategory;
        }
      }

      try {
        const res = await editFormTemplate.mutateAsync(data);
        toast.success('Form Template updated successfully');
        reset();
        setCloneForm(false)
        setOpenAddFormTemplateForm(false);
      } catch(err: any){
        console.error('err',err);
      }
    }
  };

  const onSubmitHandler = async (data: any) => {
    try {
      if(String(getValues('formCategory')).trim() == '-1' &&
        String(getValues('newCategory')).trim() == '') {
          setError('newCategory', {
            type: 'manual',
            message: 'Please enter a category or select from an existing category!',
          });
      }
      else {
        if (
          String(getValues('name')).trim() !== '' &&
          String(getValues('description')).trim() !== ''
        ) {
          data.name = String(getValues('name')).trim();
          data.description = String(getValues('description')).trim();

          if(editData) {
            if (String(getValues('name')).trim() !== editData?.name) {
              const response = await checkTemplateNameMutation.mutateAsync(
                getValues('name').trim()
              );

              if (response.data.id && response.data.id != editData?.id) {
                setError('name', {
                  type: 'manual',
                  message: 'Name already exist!',
                });
              } else {
                storeFormTemplate(data);
              }
            } else {
              storeFormTemplate(data);
            }
          } else {
            const response = await checkTemplateNameMutation.mutateAsync(
              getValues('name').trim()
            );

            if (response.data.id) {
              setError('name', {
                type: 'manual',
                message: 'Name already exists',
              });
            } else {
              data.formCategoryId = data?.formCategory;
              delete data.formCategory;

              if(data.formCategoryId == '-1' && data.newCategory) {
                const res = await addFormCategory.mutateAsync({name: data.newCategory});
                if (res?.status == 200 || res?.status == 201) {
                  data.formCategoryId = res?.data.id;
                  delete data.newCategory;
                }
              }

              try {
                const res = await addFormTemplateData.mutateAsync(data);
                toast.success('Form Template added successfully');
                reset();
                setCloneForm(false);
                setOpenAddFormTemplateForm(false);
              } catch(err: any) {
                console.error('err',err);
              }
            }
          }
        }
      }
    } catch (err: any) {
      console.error(err.message);
    }
  };

  useEffect(() => {
    if(editData) {
      clearErrors();
      setValue('name', editData?.name);
      setValue('description', editData?.description);
      setValue('formCategory', editData?.formCategoryId);
    } else {
      reset();
    }
  }, [editData, listFormCategories, reset]);

  return (
    <div>
      <div className="">
        <div className="flex justify-between mx-4">
          <div className="block">
            <h6 className="font-semibold text-[24px] text-white">
              {(editData && !cloneForm) && 'Edit Form Template'}
              {(editData && cloneForm) && 'Clone Form Template'}
              {!editData && 'Add Form Template'}
            </h6>
            <p className="font-normal my-1 text-[16px] tracking-normal leading-5 text-[#cccccc] ">
              {!editData
                ? 'Enter all of the required information to add a new form template to the platform'
                : ``}
            </p>
          </div>
          <div
            className="mt-1 cursor-pointer"
            onClick={() => {
              setCloneForm(false);
              setOpenAddFormTemplateForm(false);
            }}
          >
            <CloseIcon />
          </div>
        </div>
        <div className="">
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <div className="rounded pt-3">
              <div className="m-2">
                <div className="grid grid-cols-1">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Name
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('name')}
                      type="text"
                      name="name"
                      id="name"
                      className="bg-gray-200 p-1.5 w-full rounded pl-2 text-[14px] text-black"
                      placeholder="Enter Name"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.name?.message}
                    </p>
                  </div>
                  <div></div>
                </div>

                <div className="grid grid-cols-1 py-2">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Description
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('description')}
                      type="text"
                      name="description"
                      id="description"
                      className="bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black"
                      placeholder="Enter Description"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.description?.message}
                    </p>
                  </div>
                  <div className=""></div>
                </div>

                <div className="grid grid-cols-1">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Form Category
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <select
                      {...register('formCategory')}
                      name="formCategory"
                      id="formCategory"
                      onChange={(e) => {
                        clearErrors('formCategory');
                        setCategoryValue(e.target.value);
                      }}
                      className={'block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-[14px] text-black'}
                    >
                      <option className="hidden" value={''}>
                        Select Form Category
                      </option>
                      {listFormCategories?.map((ele: any) => (
                        <option key={ele?.id} value={ele?.id} className="text-black">
                          {ele.name.charAt(0).toUpperCase() + ele.name.slice(1)}
                        </option>
                      ))}
                        <option value={-1} className="text-black">
                          Add New Category
                        </option>
                    </select>
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.formCategory?.message}
                    </p>
                  </div>
                </div>
                {categoryValue == '-1' &&
                  <div className="grid grid-cols-1 py-2">
                    <div className="mx-2">
                      <span className="text-white text-[12px]">
                        New Category
                        <span className="text-red-600 font-medium ml-[1px]">
                          *
                        </span>
                      </span>
                      <input
                        {...register('newCategory')}
                        type="text"
                        name="newCategory"
                        id="newCategory"
                        className="bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black"
                        placeholder="Enter Category"
                      />
                      <p className="text-start text-xs text-red-500 font-semibold pt-1">
                        {errors.newCategory?.message}
                      </p>
                    </div>
                    <div className=""></div>
                  </div>
                }
              </div>
              <div className="grid grid-cols-2 mt-5">
                <div className="mx-3">
                  <button
                    id="cancel_button"
                    title="Click to cancel"
                    onClick={() => {
                      setCloneForm(false);
                      setOpenAddFormTemplateForm(false);
                    }}
                    className={`text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center h-9 w-full items-center  me-2 mb-2 ${
                      editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {'Cancel'}
                  </button>
                </div>
                <div className="mx-3">
                  <button
                    id="create_form_template"
                    title={
                      !editData
                        ? 'Click to add new form template details'
                        : 'Click to save form template details'
                    }
                    type="submit"
                    className={`text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-8 py-2 text-center h-9 w-full items-center me-2 mb-2 ${
                      editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {(editData && !cloneForm) && 'Save'}
                    {(editData && cloneForm) && 'Clone'}
                    {!editData && 'Add'}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
