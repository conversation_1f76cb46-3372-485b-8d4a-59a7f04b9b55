import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { DateSelectionService } from './date_selection.service';
import { CreateDateSelectionDto } from './dto/create-date_selection.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';

@Controller('portal/v1/date-selection')
@ApiTags('dateSelection')
export class DateSelectionController {
  constructor(private readonly dateSelectionService: DateSelectionService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  create(@Body() createDateSelectionDto: CreateDateSelectionDto) {
    return this.dateSelectionService.create(createDateSelectionDto);
  }

  @Get(':id/:type')
  @UseGuards(JwtAuthGuard)
  findOne(@Param('id') id: string, @Param('type') type: string) {
    return this.dateSelectionService.findOne(+id, type);
  }
}
