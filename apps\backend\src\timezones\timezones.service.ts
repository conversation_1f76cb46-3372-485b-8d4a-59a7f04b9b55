import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Timezones } from './entities/timezones.entity';
import { Repository } from 'typeorm';

@Injectable()
export class TimezonesService {
  constructor(
    @InjectRepository(Timezones)
    private timezoneRepository: Repository<Timezones>
  ) {}

  async findAll() {
    return this.timezoneRepository.find({
      where: {
        isActive: true,
      },
    });
  }

  async searchTimezones(partialText: string): Promise<any[]> {
    const allTimezones = await this.findAll();
    return allTimezones.filter((timezone) =>
      timezone.name.toLowerCase().includes(partialText.toLowerCase())
    );
  }

}
