import { useEffect, useState } from 'react';
import {
  EyeIcon,
  EyeOffIcon,
  LockIcon,
  Logo,
  UserIcon,
} from '../../../assets/icons/icons';
import { useLogin } from '../../../services/mutations/usermutations';
import { useNavigate } from 'react-router';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { ToastContainer, toast } from 'react-toastify';
import { Link, Navigate } from 'react-router-dom';
import VersionTxt from '/version.txt';
import { IwtEnv } from '../../../api/apiClient';
import decodeJWT from '../../../utils/jwtdecoder';
import mixpanel from 'mixpanel-browser';
declare var iwtEnv: IwtEnv;
const schema = yup.object({
  username: yup.string().max(50).required('Please enter username'),
  password: yup.string().max(50).required('Please enter password'),
});

export default function Login(): JSX.Element {
  const [showPassword, setShowPassword] = useState(false);
  const [version, setVersion] = useState('');
  const loginMutation = useLogin();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    reset,
  } = useForm({ resolver: yupResolver(schema) });

  const onEyeClick = () => {
    setShowPassword(!showPassword);
  };
  const navigate = useNavigate();

  function loginMixpanelEvent() {
    const decoded = decodeJWT();
    mixpanel.identify(decoded?.userId);
    mixpanel.people.set({
      $First_Name: decoded?.firstname,
      $Last_Name: decoded?.lastname,
      $MshaId: decoded?.mshaId,
      $Mine_Name: decoded?.minename,
    });
    mixpanel.track('Logged In', {
      UserId: decoded?.userId,
    });
  }

  const onSubmitHandler = async (data: any) => {
    try {
      const response = await loginMutation.mutateAsync(data);
      if (response?.status !== 404) {
        if (response?.status !== 401) {
          if (response?.status === 403) {
            if (response?.data?.message == 'No Features') {
              setError('password', {
                type: 'manual',
                message:
                  'You do not have access to portal. Please contact your administrator.',
              });
            } else {
              setError('password', {
                type: 'manual',
                message: 'Login failed. Please contact your administrator.',
              });
            }
          } else if (decodeJWT()?.role !== 'superuser') {
            loginMixpanelEvent();
            navigate(`/app/${response?.data?.navgationModule}`);
          } else if (decodeJWT()?.role === 'superuser') {
            loginMixpanelEvent();
            navigate('/app/Mines');
          }
        } else {
          setError('password', {
            type: 'manual',
            message: 'Password failed. Please try again.',
          });
        }
      } else {
        setError('username', {
          type: 'manual',
          message: 'User not found. Please try again.',
        });
      }
    } catch (err: any) {
      if (err?.response?.data?.statusCode == 401) {
        setError('password', {
          type: 'manual',
          message: 'Password failed. Please try again.',
        });
      } else if (err?.response?.data?.statusCode == 404) {
        setError('username', {
          type: 'manual',
          message: 'User not found. Please try again.',
        });
      } else if (err?.response?.data?.statusCode === 403) {
        if (err?.response?.data?.message == 'No Features') {
          setError('password', {
            type: 'manual',
            message:
              'You do not have access to portal. Please contact your administrator.',
          });
        } else {
          setError('password', {
            type: 'manual',
            message: 'Login failed. Please contact your administrator.',
          });
        }
      } else {
        toast.error(err.message);
      }
    }
  };

  useEffect(() => {
    fetch(VersionTxt)
      .then((response) => response.text())
      .then((data) => setVersion(data))
      .catch((error) => setVersion(''));
  }, []);

  return (
    <section className="h-screen">
      <div className="h-full">
        {/* <!-- Login container with background--> */}
        <div className="g-6 flex h-full flex-wrap items-center justify-center lg:justify-between m-auto">
          {/* <!--Sign in section--> */}
          <div className="mb-0 md:mb-12 lg:mb-20  md:w-8/12 lg:w-full xl:w-full ">
            <div className="shrink-1 mb-12 grow-0 basis-auto md:mb-7 md:w-9/12 md:shrink-0 lg:w-6/12 xl:w-6/12 m-auto">
              <div className="">
                <Logo className="m-auto  rounded-full" />
                <h2 className="text-center text-white text-2xl  font-semibold mt-8">
                  IWT Analytics Platform
                </h2>
                {/* <div
                  className={`${
                    window?.innerHeight < 650 ? 'h-[14vh]' : 'h-[9vh]'
                  }`}
                >
                  <Logo className="m-auto  rounded-full" />
                  <img
                    src={mainLogo}
                    alt="logo"
                    className="m-auto mb-5 mainLogo"
                  />
                </div> */}
                {/* <iframe src="https://lottie.host/embed/d87323ad-7f91-4071-a929-55c12366e229/C0vZvX27Mu.json"></iframe> */}
              </div>
            </div>
            <form
              className="max-w-sm mx-auto md:w-4/5 lg:w-3/4 xl:w-1/4 mt-12"
              onSubmit={handleSubmit(onSubmitHandler)}
            >
              <div className="mb-2">
                <div className="relative  rounded ">
                  <span>
                    <UserIcon className="absolute top-0 left-1 h-6 mr-1 my-1.5 text-white cursor-pointer"></UserIcon>
                  </span>
                  <input
                    {...register('username')}
                    type="text"
                    autoFocus
                    tabIndex={1}
                    id="username"
                    className={`border text-white text-sm pl-8 pr-[34px] bg-transparent focus:border-blue-500 block w-full p-2  placeholder-white outline-none autoComOff`}
                    placeholder="Username"
                  />
                </div>
                <div
                  id="usernameError"
                  className="text-start text-xs text-red-500 semibold pt-1"
                >
                  {errors.username?.message}
                </div>
              </div>
              <div className="mb-8">
                <div className="relative  rounded b">
                  <span>
                    <LockIcon className="absolute top-0 left-1 h-6 mr-1 my-1.5  text-white cursor-pointer"></LockIcon>
                  </span>
                  <input
                    {...register('password')}
                    type={!showPassword ? 'password' : 'text'}
                    id="password"
                    tabIndex={2}
                    className={`border   text-white text-sm pl-8 outline-none bg-transparent focus:border-blue-500 block w-full p-2  placeholder-white autoComOff`}
                    placeholder="Password"
                  />
                  {!showPassword ? (
                    <span onClick={onEyeClick}>
                      <EyeIcon className="absolute top-0  right-0 h-6 mr-1 my-1.5 text-white cursor-pointer"></EyeIcon>
                    </span>
                  ) : (
                    <span onClick={onEyeClick}>
                      <EyeOffIcon className="absolute top-0 right-0 h-6 mr-1 my-1.5 text-white cursor-pointer"></EyeOffIcon>
                    </span>
                  )}
                </div>
                <div
                  id="passwordError"
                  className="text-start text-xs text-red-500 semibold pt-1"
                >
                  {errors.password?.message}
                </div>
              </div>

              {/* <!-- Login button --> */}
              <div className="text-center md:text-right h-full  sm:items-center  grid md:grid-cols-2 sm:grid-cols-1">
                <button
                  id="signin"
                  type="submit"
                  tabIndex={3}
                  className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/90 font-medium rounded-lg text-sm px-10 py-2 text-center  items-center  me-2 mb-2"
                >
                  Sign In
                </button>
                {/* <!--Forgot password link--> */}

                <div>
                  <Link
                    id="forgotPassword"
                    className="underline cursor-pointer w-fit text-[#FFB132]  mt-[3px] md:mt-[-6px] forgotPas"
                    tabIndex={4}
                    to={'/app/forgotpassword'}
                  >
                    Forgot password?
                  </Link>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div className="text-[14px] relative top-[-30px]">
          <p className="text-gray-400">
            {iwtEnv?.showVersionflag
              ? `© 2024 IWT Services Inc. 
             ${version?.trim() === '' ? '' : `Version ${version}`}`
              : ''}
          </p>
        </div>
      </div>
      <ToastContainer
        autoClose={2000}
        hideProgressBar={true}
        newestOnTop={false}
        closeOnClick
        // rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </section>
  );
}
