import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtModule } from '@nestjs/jwt';
import { LocalStrategy } from './strategies/local.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';
import { jwtConstants } from './constant';
import { UsersModule } from '../users/users.module';
import { SendgridService } from '../common/sendgrid/sendgrid.service';
import { EmailService } from '../common/email/email.service';
import { ConfigService } from '@nestjs/config';
import { RefreshJwtStrategy } from './strategies/refresh-jwt.strategy';
import { MinesModule } from '../mines/mines.module';

@Module({
  imports: [
    UsersModule,
    AuthModule,
    MinesModule,
    JwtModule.register({
      secret: jwtConstants.accessTokenSecret,
      signOptions: { expiresIn: '365d' },
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    LocalStrategy,
    JwtStrategy,
    SendgridService,
    EmailService,
    ConfigService,
    RefreshJwtStrategy,
  ],
})
export class AuthModule {}
