import {
  IsNotEmpty,
  IsDef<PERSON>,
  <PERSON>N<PERSON><PERSON>,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateFormCategoryDto {
  @IsString({ message: 'Name must be a string' })
  @Length(1, 100, {
    message: 'Name length must be between 1 and 100 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  mineId: number;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
