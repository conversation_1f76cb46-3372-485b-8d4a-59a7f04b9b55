import {
  addRoleFeature,
  deleteRoleFeature,
} from '../../api/rolefeatures/rolefeaturesapis';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function useAddRoleFeature() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) => addRoleFeature(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-rolefeature'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        queryKey: ['rolefeatures'],
        exact: true,
      });
    },
  });
}

export function useDeleteRoleFeature() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteRoleFeature(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['delete-rolefeature'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        queryKey: ['rolefeatures'],
        exact: true,
      });
    },
  });
}
