import { toast } from 'react-toastify';
import { getUserById } from '../../../api/users/userapis';
import decodeJWT from '../../../utils/jwtdecoder';
import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { getFeatures } from '../../../api/users/userapis';

export default function Dashboard() {
  const { data, isLoading } = useQuery({
    queryKey: ['user_by_id'],
    queryFn: () => getUserById(Number(decodeJWT()?.userId)),
    refetchOnWindowFocus: false,
  });
  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  return '';
  // if (
  //   features?.data.some((feature: any) => feature.FeatureName == 'Dashboard') ||
  //   decodeJWT()?.role == 'superuser'
  // ) {
  //   return (
  //     <>
  //       <div className="sticky w-full top-0 z-30 px-6 box  bg-top">
  //         <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe]  pb-4 pt-4">
  //           <div className="">
  //             <h6 className="p-2 font-bold text-white text-[32px] text-left"></h6>
  //           </div>
  //           <div className="">
  //             <h6 className="p-2 font-bold text-white text-[32px] text-right">
  //               Dashboard
  //             </h6>
  //           </div>
  //         </div>
  //       </div>
  //     </>
  //   );
  // } else {
  //   return '';
  // }
}
