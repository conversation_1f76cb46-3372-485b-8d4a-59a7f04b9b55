import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToOne,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Category } from '../../categories/entities/category.entity';
import { RoleFeatureAccess } from '../../role_feature_access/entities/role-feature-access.entity';

@Entity('features')
export class Feature {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ unique: true, nullable: false })
  name: string;

  @Column({ nullable: false })
  code: string;

  @Column({ name: 'category_id', nullable: false })
  categoryId: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @Column({ name: 'sort_order', nullable: false })
  sortOrder: number;

  @ManyToOne(() => Category)
  @JoinColumn({ name: 'category_id' })
  category: Category;

  @OneToMany(
    () => RoleFeatureAccess,
    (roleFeatureAccess) => roleFeatureAccess.feature
  )
  roleFeatureAccess: RoleFeatureAccess[];
}
