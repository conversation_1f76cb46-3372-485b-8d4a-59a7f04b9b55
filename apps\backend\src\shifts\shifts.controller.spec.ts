import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON>ftsController } from './shifts.controller';
import { ShiftsService } from './shifts.service';

describe('ShiftsController', () => {
  let controller: ShiftsController;
  
  const mockShiftsService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ShiftsController],
      providers: [{
          provide: ShiftsService,
          useValue: mockShiftsService,
      }]
    }).compile();

    controller = module.get<ShiftsController>(ShiftsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('findAll => should return an array of shifts', async () => {
    // arrange
    const mineId = 1;
    const shift = {
      id: 1,
      mineId: mineId,
      shiftName: "Day Shift",
      startTime: "06:00",
      endTime: "14:00",
      isActive: 1
    };

    const shifts = [shift];
    jest.spyOn(mockShiftsService, 'findAll').mockReturnValue(GeolocationPosition);

    // act
    const result = await controller.findAll(mineId);

    // assert
    expect(mockShiftsService.findAll).toHaveBeenCalled();
    expect(result).toEqual(shifts);
  });

  it('findOne => should find a shifts by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const shift = {
      id: 1,
      mineId: 1,
      shiftName: "Day Shift",
      startTime: "06:00",
      endTime: "14:00",
      isActive: 1
    };

    jest.spyOn(mockShiftsService, 'findOne').mockReturnValue(shift);

    // act
    const result = await controller.findOne(id);

    // assert
    expect(mockShiftsService.findOne).toHaveBeenCalled();
    expect(mockShiftsService.findOne).toHaveBeenCalledWith(+id);
    expect(result).toEqual(shift);
  });
});
