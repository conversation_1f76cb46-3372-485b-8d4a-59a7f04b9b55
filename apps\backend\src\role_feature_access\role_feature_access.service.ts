import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RoleFeatureAccess } from './entities/role-feature-access.entity';
import { Repository } from 'typeorm';
import { CreateRoleFeatureAccessDto } from './dto/create-role-feature-access.dto';
import { AuditService } from '../audit/audit.service';
import { RolesService } from '../roles/roles.service';
import { MinesService } from '../mines/mines.service';

@Injectable()
export class RoleFeatureAccessService {
  constructor(
    private auditService: AuditService,
    private roleService: RolesService,
    private mineService: MinesService,
    @InjectRepository(RoleFeatureAccess)
    private roleFeatureAccessRepo: Repository<RoleFeatureAccess>
  ) {}

  async create(
    createRoleFeatureAccessDto: CreateRoleFeatureAccessDto,
    user?: any
  ) {
    const roleFeatureAccess = this.roleFeatureAccessRepo.create(
      createRoleFeatureAccessDto
    );
    const roleFeature = await this.roleFeatureAccessRepo.save(
      roleFeatureAccess
    );
    if (roleFeature && user) {
      const role = await this.roleService.findOne(
        createRoleFeatureAccessDto.roleId
      );
      const mine = await this.mineService.findMineById(user.mineid);
      this.auditService.createAuditLog(
        user,
        mine.name,
        'Feature added for role',
        role.name,
        'Role'
      );
    }
    return roleFeature;
  }

  async delete(id: number, user: any) {
    const roleFeature = await this.findOne(id);

    if (user) {
      const mine = await this.mineService.findMineById(user.mineid);
      this.auditService.createAuditLog(
        user,
        mine.name,
        'Feature removed for role',
        roleFeature[0].roleName,
        'Role'
      );
    }

    return this.roleFeatureAccessRepo.delete(id);
  }
  async findAll(companyId: number) {
    const queryBuilder = this.roleFeatureAccessRepo
      .createQueryBuilder('role_feature_access')
      .select([
        'role_feature_access.id as roleFeatureId',
        'role_id',
        'feature_id',
      ])
      .innerJoin('roles', 'role', 'role.id = role_feature_access.role_id')
      .innerJoin(
        'features',
        'feature',
        'feature.id = role_feature_access.feature_id'
      )
      .where('role.company_id = :companyId', { companyId: companyId });

    const companyRoleFeature = await queryBuilder.getRawMany();

    return companyRoleFeature;
  }

  async findOne(id: number) {
    const featureRole = await this.roleFeatureAccessRepo
      .createQueryBuilder('role_feature_access')
      .select([
        'role_feature_access.id as roleFeatureAccessId',
        'role.id as roleId',
        'role.name as roleName',
      ])
      .innerJoin('roles', 'role', 'role.id = role_feature_access.role_id')
      .where('role_feature_access.id = :roleFeatureId', { roleFeatureId: id })
      .getRawMany();
    return featureRole;
  }
}
