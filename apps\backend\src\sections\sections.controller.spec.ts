import { Test, TestingModule } from '@nestjs/testing';
import { SectionsController } from './sections.controller';
import { SectionsService } from './sections.service';

describe('SectionsController', () => {
  let controller: SectionsController;
  
  const mockSectionsService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SectionsController],
      providers: [{
          provide: SectionsService,
          useValue: mockSectionsService,
      }]
    }).compile();

    controller = module.get<SectionsController>(SectionsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('findAll => should return an array of sections', async () => {
    // arrange
    const mineId = 1;
    const section = {
      id: 1,
      mineId: mineId,
      sectionName: "Section A",
      isActive: 1
    };

    const sections = [section];
    jest.spyOn(mockSectionsService, 'findAll').mockReturnValue(GeolocationPosition);

    // act
    const result = await controller.findAll(mineId);

    // assert
    expect(mockSectionsService.findAll).toHaveBeenCalled();
    expect(result).toEqual(sections);
  });

  it('findOne => should find a sections by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const section = {
      id: 1,
      mineId: 1,
      sectionName: "Section A",
      isActive: 1
    };

    jest.spyOn(mockSectionsService, 'findOne').mockReturnValue(section);

    // act
    const result = await controller.findOne(id);

    // assert
    expect(mockSectionsService.findOne).toHaveBeenCalled();
    expect(mockSectionsService.findOne).toHaveBeenCalledWith(+id);
    expect(result).toEqual(section);
  });
});
