import { ApiClient } from '../apiClient';

export interface FormData {
  id?: number;
  name?: string;
  content?: string;
  submission?: number;
  isSubmitted?: boolean;
}

export const addForm = async (data: FormData) =>
  await ApiClient.post(`/api/portal/v1/forms`, data);

export const getForms = async () =>
  await ApiClient.get(`api/portal/v1/forms`);

export const getInProgressForms = async () =>
  await ApiClient.get(`api/portal/v1/forms/inprogress`);

export const getInProgressFormsByUser = async () =>
  await ApiClient.get(`api/portal/v1/forms/inprogressbyuser`);

export const getSubmittedForms = async (date?: string|null) => {
  let url = date ? `api/portal/v1/forms/submitted/${date}` : `api/portal/v1/forms/submitted`;
  return await ApiClient.get(url);
}

export const getSubmittedFormsByUser = async (date?: string|null) => {
  let url = date ? `api/portal/v1/forms/submittedbyuser/${date}` : `api/portal/v1/forms/submittedbyuser`;
  return await ApiClient.get(url);
}

export const checkFormByName = async (name: string) => {
  const encodedName = encodeURIComponent(name);
  return await ApiClient.get(`/api/portal/v1/forms/name/${encodedName}`);
}

export const getForm = async (id: number) =>
  await ApiClient.get(`api/portal/v1/forms/${id}`);

export const editForm = async (id: number, data: FormData) => {
  return await ApiClient.patch(`/api/portal/v1/forms/${id}`, data);
}

export const submitForm = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/forms/submit/${id}`);
}

export const revokeSubmission = async (id: number) =>
  await ApiClient.patch(`/api/portal/v1/forms/revoke/${id}`);

export const deleteForm = async (id: number) =>
  await ApiClient.patch(`/api/portal/v1/forms/isDelete/${id}`);

export const getFile = async (location: string) =>
  await ApiClient.get(`public/uploads/${location}`);