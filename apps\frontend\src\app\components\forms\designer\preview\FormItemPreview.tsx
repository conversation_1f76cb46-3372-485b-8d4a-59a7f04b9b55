import { IwtEnv } from '../../../../../api/apiClient';
import { useEffect, useState } from 'react';
import { CloneIcon, DoubleArrow, DragIcon, SingleArrow, TrashIcon } from '../../../../../assets/icons/icons';
import { Id, Item, SelectedItem } from "../../types";
import { useQuery } from '@tanstack/react-query';
import { getActiveShifts } from '../../../../../api/shifts/shiftapis';
import { getSections } from '../../../../../api/sections/sectionapis';
import Signature from '../../../common/Signature';
declare var iwtEnv: IwtEnv;

interface Props {
	itemGroup: string,
	showPreview: Boolean;
	item: Item;
	selectedItem: SelectedItem;
	setSelectedItem: (item: SelectedItem) => void;
	moveItem: (groupId: Id, fromIndex: number, direction: string) => void;
	cloneItem: (item: Item) => void;
	deleteItem: (item: Item) => void;
	questionIndex: number;
}

interface Style {
	backgroundColor?: string,
	color?: string,
	margin?: string,
	marginTop?: string,
	marginBottom?: string,
	marginLeft?: string,
	marginRight?: string,
	padding?: string,
	paddingTop?: string,
	paddingBottom?: string,
	paddingLeft?: string,
	paddingRight?: string,
	width?: string,
	height?: string,
	fontSize?: string,
}

interface InputStyle {
	height: string,
	width: string,
	minWidth: string,
	minHeight: string,
}

function FormItemPreview({ itemGroup, showPreview, item, selectedItem, setSelectedItem, moveItem, cloneItem, deleteItem, questionIndex }: Props) {
	const properties = item.properties;
	const key = properties?.key ?? null;
	const placeholder = properties?.placeholder ?? null;
	const text = properties?.text ?? null;
	const typeValue = properties?.type ?? 'Input';
	const inputTypeOption = properties?.inputoptiontype ?? 'text';
	const dateTypeOption = properties?.dateoptiontype ?? 'date';
	const labelpositiongroup = properties?.labelgroup?.labelposition ?? null;
	const labelposition = labelpositiongroup ?? 'Header';
	const label = properties?.labelgroup?.label ?? properties.label ?? null;
	const bgcolor = properties?.bgcolorgroup?.bgcolor ?? null;
	const textcolor = properties?.colorgroup?.color ?? null;
	const fUnitValueField = properties?.fontsizegroup?.fontsizeunitvaluefield ?? null;
	const fUnitTypeField = properties?.fontsizegroup?.fontsizeunittypefield ?? null;
	const mUnitValueField = properties?.margingroup?.unitvaluefield ?? null;
	const mUnitTypeField = properties?.margingroup?.unittypefield ?? null;
	const mtUnitValueField = properties?.margintop?.unitvaluefield ?? null;
	const mtUnitTypeField = properties?.margintop?.unittypefield ?? null;
	const mbUnitValueField = properties?.marginbottom?.unitvaluefield ?? null;
	const mbUnitTypeField = properties?.marginbottom?.unittypefield ?? null;
	const mlUnitValueField = properties?.marginleft?.unitvaluefield ?? null;
	const mlUnitTypeField = properties?.marginleft?.unittypefield ?? null;
	const mrUnitValueField = properties?.marginright?.unitvaluefield ?? null;
	const mrUnitTypeField = properties?.marginright?.unittypefield ?? null;
	const pUnitValueField = properties?.paddinggroup?.unitvaluefield ?? null;
	const pUnitTypeField = properties?.paddinggroup?.unittypefield ?? null;
	const ptUnitValueField = properties?.paddingtop?.unitvaluefield ?? null;
	const ptUnitTypeField = properties?.paddingtop?.unittypefield ?? null;
	const pbUnitValueField = properties?.paddingbottom?.unitvaluefield ?? null;
	const pbUnitTypeField = properties?.paddingbottom?.unittypefield ?? null;
	const plUnitValueField = properties?.paddingleft?.unitvaluefield ?? null;
	const plUnitTypeField = properties?.paddingleft?.unittypefield ?? null;
	const prUnitValueField = properties?.paddingright?.unitvaluefield ?? null;
	const prUnitTypeField = properties?.paddingright?.unittypefield ?? null;
	const wSizeValueField = properties?.widthgroup?.sizeunitvaluefield ?? null;
	const wSizeUnitTypeField = properties?.widthgroup?.widthunittypefield ?? null;
	const hSizeValueField = properties?.heightgroup?.sizeunitvaluefield ?? null;
	const hSizeUnitTypeField = properties?.heightgroup?.heightunittypefield ?? null;
	const positionValue = properties?.positiongroup ?? 'left';
	const dropdowndatasourcetype = properties?.dropdowndatasourcetype ?? null;
	const imagegroup = properties?.imagegroup ?? null;
	const imagePreview = imagegroup?.source ?? imagegroup?.preview ?? null;
	const imageSource = imagegroup?.source ?? null;
	
	let dropDownData = [];
	if(dropdowndatasourcetype == 'Custom' && properties?.data) {
		dropDownData = properties?.data;
	}
	const [dropDownListData, setDropDownListData] = useState(dropDownData ?? []);
	
	const {
		data: getShiftRes,
		status: apiStatusforShift,
	} = useQuery({
		queryKey: ['active-shifts'],
		queryFn: getActiveShifts,
		refetchOnWindowFocus: false,
		enabled: (typeValue === 'DropDown' && dropdowndatasourcetype === 'Shifts')
	});
	const {
		data: getSectionRes,
		status: apiStatusforSection,
	} = useQuery({
		queryKey: ['all-sections'],
		queryFn: getSections,
		refetchOnWindowFocus: false,
		enabled: (typeValue === 'DropDown' && dropdowndatasourcetype === 'Sections')
	});

	useEffect(() => {
		if(getShiftRes?.data.length > 0 && dropdowndatasourcetype === 'Shifts') {
			setDropDownListData(getShiftRes?.data.map((shift:any) => {
				return {itemId: shift.id, answer:shift.shiftName}
			}));
		}
		if(getSectionRes?.data.length > 0 && dropdowndatasourcetype === 'Sections') {
			setDropDownListData(getSectionRes?.data.map((section:any) => {
				return {itemId: section.id, answer:section.sectionName}
			}));
		}
	}, [getShiftRes?.data, getSectionRes?.data]);
	
	let justifyValue = 'center';
	if(positionValue == 'left') { justifyValue = 'start' }
	if(positionValue == 'right') { justifyValue = 'end' }
	
	const style:Style = {};
	let offset = 28;
	if(!(questionIndex > 0 && labelposition == 'Header') && labelposition !== 'None' && typeValue != 'Header' && typeValue != 'Label') {
		offset += 32
	}
	const inputStyle:InputStyle = {
		height: `calc(100% - ${offset}px)`,
		width: '100%',
		minWidth: '38px',
		minHeight: '38px',
	};
	const radioStyle:InputStyle = {
		height: `calc(100% - ${offset}px)`,
		width: '100%',
		minWidth: '26px',
		minHeight: '26px',
	};
	const signatureStyle:InputStyle = {
		height: `calc(100% - ${offset}px)`,
		width: '100%',
		minWidth: '100px',
		minHeight: '100px',
	};
	
	let margin, marginTop, marginBottom, marginLeft, marginRight;
	if(mUnitTypeField) {
		if(mUnitTypeField !== 'individual') {
			margin = `${mUnitValueField ?? ''}${mUnitTypeField ?? ''}`;
		}
		else {
			marginTop = `${mtUnitValueField ?? ''}${mtUnitTypeField ?? ''}`;
			marginBottom = `${mbUnitValueField ?? ''}${mbUnitTypeField ?? ''}`;
			marginLeft = `${mlUnitValueField ?? ''}${mlUnitTypeField ?? ''}`;
			marginRight = `${mrUnitValueField ?? ''}${mrUnitTypeField ?? ''}`;
		}
	}

	let padding, paddingTop, paddingBottom, paddingLeft, paddingRight;
	if(pUnitTypeField) {
		if(pUnitTypeField !== 'individual') {
			padding = `${pUnitValueField ?? ''}${pUnitTypeField ?? ''}`;
		}
		else {
			paddingTop = `${ptUnitValueField ?? ''}${ptUnitTypeField ?? ''}`;
			paddingBottom = `${pbUnitValueField ?? ''}${pbUnitTypeField ?? ''}`;
			paddingLeft = `${plUnitValueField ?? ''}${plUnitTypeField ?? ''}`;
			paddingRight = `${prUnitValueField ?? ''}${prUnitTypeField ?? ''}`;
		}
	}
	
	let width, widthClass;
	if(wSizeUnitTypeField) {
		let type = wSizeUnitTypeField ?? 'w-fit';
		let value = wSizeValueField ?? 0;
		if(type == 'px' || type == '%') {
			width = `${value}${type}`;
		}
		else {
			widthClass = type;
		}
	}
	
	let height, heightClass;
	if(hSizeUnitTypeField) {
		let type = hSizeUnitTypeField ?? 'h-fit';
		let value = hSizeValueField ?? 0;
		if(type == 'px' || type == '%') {
			height = `${value}${type}`;
		}
		else {
			heightClass = type;
		}
	}
	
	let fontSize, fontSizeClass;
	if(fUnitTypeField) {
		let type = fUnitTypeField ?? 'base';
		let value = fUnitValueField ?? 1;
		if(type == 'px' || type == 'rem') {
			fontSize = `${value}${type}`;
		}
		else {
			fontSizeClass = type;
		}
	}
	
	const backgroundColor = `${bgcolor ?? ''}`;
	const color = `${textcolor ?? ''}`;

	if(fontSize && fontSize.length > 0) {style.fontSize = fontSize;}
	if(backgroundColor && backgroundColor.length > 0) {style.backgroundColor = backgroundColor;}
	if(color && color.length > 0) {style.color = color;}
	if(margin && margin.length > 0) {style.margin = margin;}
	if(marginTop && marginTop.length > 0) {style.marginTop = marginTop;}
	if(marginBottom && marginBottom.length > 0) {style.marginBottom = marginBottom;}
	if(marginLeft && marginLeft.length > 0) {style.marginLeft = marginLeft;}
	if(marginRight && marginRight.length > 0) {style.marginRight = marginRight;}
	if(padding && padding.length > 0) {style.padding = padding;}
	if(paddingTop && paddingTop.length > 0) {style.paddingTop = paddingTop;}
	if(paddingBottom && paddingBottom.length > 0) {style.paddingBottom = paddingBottom;}
	if(paddingLeft && paddingLeft.length > 0) {style.paddingLeft = paddingLeft;}
	if(paddingRight && paddingRight.length > 0) {style.paddingRight = paddingRight;}
	if(width && width.length > 0) {style.width = width;}
	if(height && height.length > 0) {style.height = height;}

	function handleMoveItemButtonClick (e: React.MouseEvent<HTMLButtonElement>, groupId: Id, fromIndex: number, direction: string) {
		e.stopPropagation();
		moveItem(groupId, fromIndex, direction);
	}

	function handleDeleteItemButtonClick(e: React.MouseEvent<HTMLButtonElement>, item: Item) {
		e.stopPropagation();
		deleteItem(item);
	}

	function handleCloneItemButtonClick(e: React.MouseEvent<HTMLButtonElement>, item: Item) {
		e.stopPropagation();
		cloneItem(item);
	}

	function handleFormItemSelect(e: React.MouseEvent<HTMLDivElement>, id: Id) {
		e.stopPropagation();
		const selectedItem = { id: id, item: item };
		setSelectedItem(selectedItem);
	}

	return (
		<div
			onClick={(e) => handleFormItemSelect(e, item?.id)}
			style={style}
			data-refkey={key ?? ''}
			className={`
				p-2 flex self-start flex-wrap
				${widthClass ?? ''} ${heightClass ?? ''}
				${selectedItem?.id == item?.id && !showPreview && questionIndex <= 0 ? 'selected' : '' }
				text-${fontSizeClass ?? ''}
				justify-${justifyValue ?? 'center'}
				text-${positionValue ?? 'center'}
				justify-${positionValue ?? 'center'}
				${showPreview || questionIndex > 0 ? 'form-item-preview' : 'form-item cursor-pointer hover:ring'}
			`}
		>
			{!showPreview && questionIndex <= 0 &&
				<div className={`item-label w-full flex flex-wrap justify-between h-fit`}>
					<div className={`flex justify-start`}>
						<button
							title="Click to move item to start"
							onClick={(e) => handleMoveItemButtonClick(e, item.groupId, item.index, 'start')}
							className="opacity-40 hover:opacity-100 py-1 px-0">
							<DoubleArrow className=" w-4 h-4" />
						</button>
						<button
							title="Click to move item left"
							onClick={(e) => handleMoveItemButtonClick(e, item.groupId, item.index, 'left')}
							className="opacity-40 hover:opacity-100 py-1 px-0">
							<SingleArrow className=" w-4 h-4" />
						</button>
						<button
							title="Click to move item right"
							onClick={(e) => handleMoveItemButtonClick(e, item.groupId, item.index, 'right')}
							className="opacity-40 hover:opacity-100 py-1 px-0">
							<SingleArrow className="rotate-180 w-4 h-4" />
						</button>
						<button
							title="Click to move group item to end"
							onClick={(e) => handleMoveItemButtonClick(e, item.groupId, item.index, 'end')}
							className="opacity-40 hover:opacity-100 py-1 px-0">
							<DoubleArrow className="rotate-180 w-4 h-4" />
						</button>
					</div>
					<div className={`flex justify-end`} >
						<button
							title="Click to clone item"
							onClick={(e) => handleCloneItemButtonClick(e, item)}
							className="opacity-40 hover:opacity-100 py-1 px-0.5">
							<CloneIcon />
						</button>
						<button
							title="Click to remove item"
							onClick={(e) => handleDeleteItemButtonClick(e, item)}
							className="opacity-40 hover:opacity-100 py-1 px-0.5">
							<TrashIcon />
						</button>
					</div>
				</div>
			}

			{!(questionIndex > 0 && labelposition == 'Header') && labelposition !== 'None' && typeValue != 'Header' && typeValue != 'Label' && (
				<label className={`max-w-full`}>{label}</label>
			)}
			{typeValue == 'Checkbox' && (
				<input style={radioStyle} name={itemGroup} className={`form-check-input form-element`} type='checkbox'/>
			)}
			{typeValue == 'Color' && (
				<div className='color-element-container flex flex-col items-center gap-2'>
					<input style={inputStyle} className={`color-element form-element`} type='color' />
				</div>
			)}
			{typeValue == 'Date' && (
				<input style={inputStyle} className={`bg-gray-200 p-1.5 rounded text-black form-element`} type={dateTypeOption} />
			)}
			{typeValue == 'DropDown' && (
				<select defaultValue={-1} style={inputStyle} className={`form-element p-[7px] rounded bg-gray-200 focus:border-blue-500 text-black`}>
					<option disabled value={-1} className="text-gray-300 bg-gray-500">Select an option</option>
					{dropDownListData.map((o:any) => <option value={o.itemId} key={o.itemId}>{o.answer}</option>)}
				</select>
			)}
			{typeValue == 'Header' && (
				<h1>{text ?? ''}</h1>
			)}
			{typeValue == 'Image' && (
				<img src={imageSource ? `${iwtEnv.apiUrl}/${imageSource}` : imagePreview ?? ''} />
			)}
			{typeValue == 'Input' && (
				<input
					style={inputStyle}
					className={`bg-gray-200 p-1.5 w-full h- rounded text-black form-element`}
					type={inputTypeOption} placeholder={placeholder ?? null} />
			)}
			{typeValue == 'Label' && (
				<label>{text ?? ''}</label>
			)}
			{typeValue == 'Radio' && (
				<input style={radioStyle} name={itemGroup} className={`form-check-input form-element`} type='radio'/>
			)}
			{typeValue == 'Signature' && (
				<Signature
					readOnly={false}
					currentValue={''}
					saveSignatureField={()=>{}}
					style={signatureStyle}
				/>
			)}
			{typeValue == 'Textarea' && (
				<textarea style={inputStyle} className={`bg-gray-200 p-1.5 w-full rounded text-black min-h-[100px]`} placeholder={placeholder ?? null}></textarea>
			)}
		</div>
	)
}

export default FormItemPreview;