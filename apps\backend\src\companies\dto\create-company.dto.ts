import {
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
export class CreateCompanyDto {
  @Length(1, 100, {
    message: 'Name length must be between 1 and 100 characters',
  })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @Length(1, 50, { message: 'Code length must be between 1 and 50 characters' })
  @IsString({ message: 'Code must be a string' })
  @IsNotEmpty()
  @ApiProperty()
  code: string;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
