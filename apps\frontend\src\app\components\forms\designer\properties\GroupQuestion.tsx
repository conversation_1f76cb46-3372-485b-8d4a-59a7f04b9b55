import { useEffect, useRef, useState } from 'react';
import { Id } from '../../types';
import { TrashIcon, PlusIcon } from '../../../../../assets/icons/icons';

interface Props {
	id: Id;
	label: string;
	value: string;
	currentIndex: number;
	focusIndex: number;
	setFocusQuestionByIndex: (index: number) => void;
	updateQuestion: (id:Id, value:string) => void;
	deleteQuestion: (id:Id) => void;
	addQuestion: (newValue: string) => void;
}

const GroupQuestion = ({
	id,
	label,
	value,
	currentIndex,
	focusIndex,
	setFocusQuestionByIndex,
	updateQuestion,
	deleteQuestion,
	addQuestion
}: Props) => {
	const [currentValue, setCurrentValue] = useState(value);
	const questionInputRef = useRef<HTMLInputElement>(null);
	
	useEffect(() => {
		setCurrentValue(value);
	}, [value]);
	
	useEffect(() => {
		if(focusIndex == currentIndex) {
			questionInputRef.current?.focus();
		}
	}, [currentIndex, focusIndex]);
	
	function valueChanged(e:any) {
		setCurrentValue(e.target.value);
	}

	function handleUpdateQuestion(e:any, focusChangeAction: string) {
		let newValue = e.target.value;
		if(newValue !== value) {
			if(id == 'temp-id') {
				setCurrentValue('');
				(focusChangeAction === 'up' || focusChangeAction === 'none') && addQuestion(newValue);
			}
			else {
				updateQuestion(id, newValue);
			}
		}
		focusChangeAction === 'up' && setFocusQuestionByIndex(currentIndex+1);
		focusChangeAction === 'down' && setFocusQuestionByIndex(currentIndex-1);
	}
	
	return (
		<>
			<span className="text-white text-[16px]">{label}:</span>
			<input
				ref={questionInputRef}
				name={label} type="text" id={`${id}`}
				className="bg-gray-200 p-1.5 rounded pl-2 text-[14px] text-black col-span-6"
				placeholder={`Enter question`}
				value={currentValue ?? ''}
				onKeyDown={(e) => {
					e.key === 'Escape' && setCurrentValue(value);
					e.key === 'Enter' && handleUpdateQuestion(e, 'up');
					if(e.key === 'Tab') {
						e.preventDefault();
						let focusChangeAction = 'up';
						if(e.shiftKey) {
							focusChangeAction = 'down';
						}
						handleUpdateQuestion(e, focusChangeAction);
					}
				}}
				onChange={(e) => valueChanged(e)}
				onBlur={(e) => handleUpdateQuestion(e, 'none')}
				onFocus={() => setFocusQuestionByIndex(currentIndex)}
			/>
			{id !== 'temp-id' &&
				<button
					tabIndex={-1}
					onClick={() => deleteQuestion(id)}
					className="cursor-pointer">
					<TrashIcon className="text-[14px] h-5 w-5" />
				</button>
			}
			{id === 'temp-id' &&
				<button
					tabIndex={-1}
					onClick={() => addQuestion('')}
					className="cursor-pointer">
					<PlusIcon className="text-white text-[14px] h-5 w-5" />
				</button>
			}
		</>
	);
};

export default GroupQuestion;