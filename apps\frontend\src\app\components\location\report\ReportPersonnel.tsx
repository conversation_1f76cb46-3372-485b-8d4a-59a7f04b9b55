import { useEffect, useState, useCallback } from 'react';
import { PrintIcon } from '../../../../assets/icons/icons';
import { Link, useOutletContext, useParams } from 'react-router-dom';
import Table, { ColumnDef } from '../../common/Table';
import EnterLeaveUG from './EnterLeaveUG';
import TotalTimeUG from './TotalTimeUG';
import ReportPersonnelWithoutMiner from './ReportPersonnelWithoutMiner';
import { doPrint } from '../../../../utils/PDFPrint';
import { useQuery } from '@tanstack/react-query';
import { getPersonalReportData } from '../../../../api/location/locationdashboardapi';
import dayjs from 'dayjs';
import decodeJWT from '../../../../utils/jwtdecoder';
import { getMineAllMiners } from '../../../../api/users/watchlistapis';
import { getDateRangerForUser } from '../../../../api/users/dateSelectionapis';
import { shortcuts } from '../../../../utils/constant';
import { Tooltip } from '@material-tailwind/react';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import Loader from '../../common/Loader';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../../PageName';
import CountdownTimer from './dummy';
const columns: ColumnDef[] = [
  {
    key: 'date',
    label: 'Date',
    type: 'text',
    render: (row: any) => {
      const formattedDate = dayjs(row?.date).format('MM-DD-YYYY');
      return <span>{formattedDate}</span>;
    },
  },
  {
    key: 'dayOfWeek',
    label: 'Day Of Week',
    type: 'text',
  },
  {
    key: 'enterUG',
    label: 'Enter UG',
    type: 'text',
    render: (row: any) => (
      <span
        className={`${
          row?.enterUGCC == 'G'
            ? 'text-green-500'
            : row?.enterUGCC == 'R'
            ? 'text-red-500'
            : 'text-white'
        }`}
      >
        {row?.enterUG ? row?.enterUG : '-'}
      </span>
    ),
  },
  {
    key: 'leaveUG',
    label: 'Leave UG',
    type: 'text',
    render: (row: any) => (
      <span
        className={`${
          row?.leaveUGCC == 'G'
            ? 'text-green-500'
            : row?.leaveUGCC == 'R'
            ? 'text-red-500'
            : 'text-white'
        }`}
      >
        {row?.leaveUG ? row?.leaveUG : '-'}
      </span>
    ),
  },
  {
    key: 'totalTimeUG',
    label: 'Total Time UG',
    type: 'text',
    render: (row: any) => (
      <span
        className={`${
          row?.totalTimeUGCC == 'G'
            ? 'text-green-500'
            : row?.totalTimeUGCC == 'R'
            ? 'text-red-500'
            : 'text-white'
        }`}
      >
        {row?.totalTimeUG ? row?.totalTimeUG : '-'}
      </span>
    ),
  },
];

const ReportPersonnel = () => {
  const [searchText, setLastUpdatedDate, filterData] = useOutletContext() as [
    string,
    any,
    any
  ];

  const params = useParams();

  const url = getPageNamesFromUrl(params['*']);

  const [timeZone, setTimeZone] = useState('');

  useEffect(() => {
    const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    setTimeZone(browserTimeZone);
  }, []);

  const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  dayjs.extend(utc);
  dayjs.extend(timezone);

  const today = dayjs();
  const startOfWeek = today.subtract(6, 'day');
  const endOfWeek = today;
  const [dateRangeSelected, setDateRangeSelected] = useState({
    startDate: startOfWeek.format('YYYY-MM-DD'),
    endDate: endOfWeek.format('YYYY-MM-DD'),
  });

  const { data: minerData, isLoading } = useQuery({
    queryKey: ['miners'],
    queryFn: getMineAllMiners,
    refetchOnWindowFocus: false,
  });

  const startDate = dateRangeSelected?.startDate
    ? String(dayjs(dateRangeSelected?.startDate).format('YYYY-MM-DD'))
    : '';
  const endDate = dateRangeSelected?.endDate
    ? String(dayjs(dateRangeSelected?.endDate).format('YYYY-MM-DD'))
    : '';

  const reportPersonnelDataQuery = useQuery({
    queryKey: [
      'personnelData',
      dateRangeSelected.startDate,
      dateRangeSelected.endDate,
      params?.minerId,
    ],
    refetchOnWindowFocus: false,
    queryFn: () =>
      getPersonalReportData(
        Number(decodeJWT()?.mineid),
        Number(params?.minerId),
        startDate,
        endDate
      ),
    enabled: !!params?.minerId,
  });

  const { data: reportPersonnelData } = reportPersonnelDataQuery;

  useEffect(() => {
    if (filterData) {
      setDateRangeSelected(filterData);
    }
  }, [dateRangeSelected?.startDate, dateRangeSelected?.endDate, filterData]);

  const data = reportPersonnelData?.data;
  const ugTimeData = [
    // {
    //   name: 'Average Enter UG',
    //   data: {
    //     time: data?.avgEnterUG?.split(' ')[0],
    //     value: data?.avgEnterUG?.split(' ')[1].toLowerCase(),
    //   },
    // },
    // {
    //   name: 'Average Leave UG',
    //   data: {
    //     time: data?.avgLeaveUG?.split(' ')[0],
    //     value: data?.avgLeaveUG?.split(' ')[1].toLowerCase(),
    //   },
    // },
    // {
    //   name: 'Average Time UG',
    //   data: {
    //     time: data?.avgTimeUG?.split(' ')[0],
    //     value: data?.avgTimeUG?.split(' ')[1].toLowerCase(),
    //   },
    // },
  ];

  const minerId = Number(params?.minerId);

  const [selectedTab, setSelectedTab] = useState('enterleaveug');
  const selected =
    'border border-[#ffb132] bg-[#ffb132] rounded-[6px] text-black';
  const common = 'text-[12px] mr-2 cursor-pointer px-4 py-1';

  return (
    <div>
      {reportPersonnelDataQuery?.isLoading ? (
        <div>
          <div>
            <div className="flex justify-center items-center h-full pt-[200px] white">
              {<Loader />}
            </div>
            <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
              Loading....
            </div>
          </div>
        </div>
      ) : (
        <div className="xl:px-8 2xl:px-[3.4rem]">
          <div>
            <div className=" ">
              {/* <div>
                <Link
                  to={'#'}
                  className={
                    selectedTab === 'enterleaveug'
                      ? `${common} ${selected}`
                      : `${common} text-white`
                  }
                  onClick={() => setSelectedTab('enterleaveug')}
                >
                  Enter/Leave UG
                </Link>
                <Link
                  to={'#'}
                  className={
                    selectedTab === 'totaltimeug'
                      ? `${common} ${selected}`
                      : `${common} text-white`
                  }
                  onClick={() => setSelectedTab('totaltimeug')}
                >
                  Total Time UG
                </Link>
              </div> */}
              <div className="">
                <Tooltip content={'Print'} arrow={false}>
                  <div
                    className={`border border-[#4AA8FE] w-[24px] float-right mr-[9px] rounded-md px-[3px] py-[3px] ${
                      params?.minerId ? 'cursor-pointer ' : 'cursor-not-allowed'
                    }`}
                    onClick={() => {
                      if (
                        params?.minerId &&
                        reportPersonnelData?.data?.reportData?.length != 0
                      ) {
                        doPrint('printableArea');
                      }
                    }}
                  >
                    <PrintIcon
                      className={`${
                        params?.minerId &&
                        reportPersonnelData?.data?.reportData?.length != 0
                          ? 'text-white'
                          : 'text-gray-200'
                      }`}
                    />
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
          <div id="printableArea" className=" ">
            <div className="minerNameHide">
              <h6 className="py-2 text-center font-bold text-white text-[32px]">
                {params?.minerId
                  ? minerData?.data?.find(
                      (ele: any) => ele?.MinerId == Number(params?.minerId)
                    )?.minerName ?? ''
                  : ''}
              </h6>
            </div>
            <div className="flex justify-around mt-5 w-[94%] mx-auto">
              {ugTimeData.map((ele) => (
                <div
                  key={ele.name}
                  className="border border-[#4AA8FE] rounded-md text-[#ffb132] text-[10px] w-1/3 h-[70px] py-[10px] px-[16px] mx-4"
                >
                  <p>{ele.name}</p>
                  <span className="font-[600] text-[32px] text-white">
                    {!params?.minerId ? '' : ele.data.time ?? '-'}
                  </span>
                  <span className="font-[400] text-[12px] text-white">
                    {!params?.minerId ? '' : ` ${ele.data.value ?? ''}`}
                  </span>
                </div>
              ))}
            </div>
            <div className="w-[92%] m-auto mt-5 h-[500px]">
              {!minerId && <ReportPersonnelWithoutMiner />}
              {minerId && selectedTab === 'enterleaveug' ? (
                <EnterLeaveUG
                  reportData={reportPersonnelData?.data?.reportData ?? []}
                  dateRange={{
                    startDate: dateRangeSelected?.startDate,
                    endDate: dateRangeSelected?.endDate,
                  }}
                  data={reportPersonnelData?.data}
                />
              ) : (
                <></>
              )}
              {minerId && selectedTab === 'totaltimeug' ? (
                <TotalTimeUG
                  reportData={reportPersonnelData?.data?.reportData ?? []}
                  dateRange={{
                    startDate: dateRangeSelected?.startDate,
                    endDate: dateRangeSelected?.endDate,
                  }}
                  data={reportPersonnelData?.data}
                />
              ) : (
                <></>
              )}
              <div className="border-t-[1px] border-[#4AA8FE] my-5 ml-auto "></div>
              {minerId ? (
                <div className="pb-10 tableBGPersonnel p-5 rounded-md">
                  {data?.reportData && data?.reportData?.length !== 0 ? (
                    <>
                      <Table
                        data={data?.reportData ?? []}
                        columns={columns}
                        searchText={''}
                        sortable={false}
                        searchOnColumn={''}
                        separateLine={true}
                        scrollable={true}
                        dataRenderLimitMdScreen={8}
                        dataRenderLimitLgScreen={15}
                        tableHeightClassLg="tableheightForlg"
                        tableHeightClassMd="tableheightFormd"
                      />
                    </>
                  ) : (
                    <h2 className="text-white text-[20px] text-center">
                      Unfortunately, we don't have miner reports for the
                      selected date. Please choose a different date.
                    </h2>
                  )}
                </div>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportPersonnel;
