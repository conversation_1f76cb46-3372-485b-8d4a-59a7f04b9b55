export default {
  "properties": {},
  "content": {
    "groups": [
      {
        "id": "group-0",
        "name": "group-0",
        "index": 0,
        "items": [
          {
            "id": "group-0-item-0",
            "index": 0,
            "groupId": "group-0",
            "content": "",
            "properties": {
              "type": "Header",
              "text": "Section Production Report",
              "fontsizegroup": {
                "fontsizeunitvaluefield": "40",
                "fontsizeunittypefield": "px",
                "value": "px"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "w-full",
                "value": "w-full"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "74B7E81A-A478-4476-8BFF-763DF88D6879"
            },
            "key": "74B7E81A-A478-4476-8BFF-763DF88D6879"
          }
        ],
        "properties": {
          "questionwidthgroup": {
            "sizeunitvaluefield": 20,
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "grouplabelposition": "block",
          "questionpositiongroup": "left",
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          }
        },
        "questions": []
      },
      {
        "id": "group-1",
        "name": "group-1",
        "index": 1,
        "items": [
          {
            "id": "group-1-item-0",
            "index": 0,
            "groupId": "group-1",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "date",
              "labelgroup": {
                "label": "Date",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "form_date",
              "requiredfield": true,
              "requiredgroup": {
                "requiredfield": true,
                "prelimfield": true
              }
            },
            "key": "form_date"
          },
          {
            "id": "group-1-item-1",
            "index": 1,
            "groupId": "group-1",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "text",
              "labelgroup": {
                "label": "Foreman",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "foreman"
            },
            "key": "foreman"
          },
          {
            "id": "group-1-item-2",
            "index": 2,
            "groupId": "group-1",
            "content": "",
            "properties": {
              "type": "DropDown",
              "dropdowndatasourcetype": "Sections",
              "labelgroup": {
                "label": "Section",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "form_section",
              "requiredfield": true,
              "requiredgroup": {
                "requiredfield": true,
                "prelimfield": true
              }
            },
            "key": "form_section"
          },
          {
            "id": "group-1-item-3",
            "index": 3,
            "groupId": "group-1",
            "content": "",
            "properties": {
              "type": "DropDown",
              "dropdowndatasourcetype": "Shifts",
              "labelgroup": {
                "label": "Shift",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "form_shift",
              "requiredfield": true,
              "requiredgroup": {
                "requiredfield": true,
                "prelimfield": true
              }
            },
            "key": "form_shift"
          }
        ],
        "properties": {
          "questionwidthgroup": {
            "sizeunitvaluefield": 20,
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "grouplabelposition": "block",
          "questionpositiongroup": "left",
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          }
        },
        "questions": []
      },
      {
        "id": "group-2",
        "name": "group-2",
        "index": 2,
        "items": [
          {
            "id": "group-2-item-0",
            "index": 0,
            "groupId": "group-2",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "labelgroup": {
                "label": "Time In",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "time_in"
            },
            "key": "time_in"
          },
          {
            "id": "group-2-item-1",
            "index": 1,
            "groupId": "group-2",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "labelgroup": {
                "label": "Arrive Time",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "arrive_time"
            },
            "key": "arrive_time"
          },
          {
            "id": "group-2-item-2",
            "index": 2,
            "groupId": "group-2",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "labelgroup": {
                "label": "On Coal Time",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "on_coal_time"
            },
            "key": "on_coal_time"
          },
          {
            "id": "group-2-item-3",
            "index": 3,
            "groupId": "group-2",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "labelgroup": {
                "label": "Quit Time",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "quit_time"
            },
            "key": "quit_time"
          }
        ],
        "properties": {
          "questionwidthgroup": {
            "sizeunitvaluefield": 20,
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "grouplabelposition": "block",
          "questionpositiongroup": "left",
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          }
        },
        "questions": []
      },
      {
        "id": "group-3",
        "name": "group-3",
        "index": 3,
        "items": [
          {
            "id": "group-3-item-0",
            "index": 0,
            "groupId": "group-3",
            "content": "",
            "properties": {
              "type": "Checkbox",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "Incident(s)",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": "100",
                "heightunittypefield": "h-fit",
                "value": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "incidents"
            },
            "key": "incidents"
          },
          {
            "id": "group-3-item-1",
            "index": 1,
            "groupId": "group-3",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "# of Crew",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "num_crew"
            },
            "key": "num_crew"
          },
          {
            "id": "group-3-item-2",
            "index": 2,
            "groupId": "group-3",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "# of Crew Off",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "num_crew_off"
            },
            "key": "num_crew_off"
          },
          {
            "id": "group-3-item-3",
            "index": 3,
            "groupId": "group-3",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "End of Track X-Cut",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "end_track_x_cut"
            },
            "key": "end_track_x_cut"
          }
        ],
        "properties": {
          "questionheaderdisplay": "Header",
          "questionpositiongroup": "left",
          "questionwidthgroup": {
            "sizeunitvaluefield": "30",
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "rowgroup": {
            "justifytypefield": "justify-start",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          }
        },
        "questions": []
      },
      {
        "id": "group-4",
        "name": "group-4",
        "index": 4,
        "items": [
          {
            "id": "group-4-item-0",
            "index": 0,
            "groupId": "group-4",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "Total Feet Mined",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "total_feet_mined"
            },
            "key": "total_feet_mined"
          },
          {
            "id": "group-4-item-1",
            "index": 1,
            "groupId": "group-4",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "Total Feet Bolted",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "total_feet_bolted"
            },
            "key": "total_feet_bolted"
          },
          {
            "id": "group-4-item-2",
            "index": 2,
            "groupId": "group-4",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "Total # of Cars",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "total_num_cars"
            },
            "key": "total_num_cars"
          },
          {
            "id": "group-4-item-3",
            "index": 3,
            "groupId": "group-4",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "CM Bits Used",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "left",
              "widthgroup": {
                "sizeunitvaluefield": "25",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "cm_bits_used"
            },
            "key": "cm_bits_used"
          }
        ],
        "properties": {
          "questionheaderdisplay": "Header",
          "questionpositiongroup": "left",
          "questionwidthgroup": {
            "sizeunitvaluefield": 20,
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          }
        },
        "questions": []
      },
      {
        "id": "group-5",
        "name": "group-5",
        "index": 5,
        "items": [
          {
            "id": "group-5-item-0",
            "index": 0,
            "groupId": "group-5",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "text",
              "headergroup": {
                "headerposition": "Header",
                "header": "Place"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "cut_place"
            },
            "key": "cut_place"
          },
          {
            "id": "group-5-item-1",
            "index": 1,
            "groupId": "group-5",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Header",
                "header": "Height of Cut"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "cut_height"
            },
            "key": "cut_height"
          },
          {
            "id": "group-5-item-2",
            "index": 2,
            "groupId": "group-5",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "headergroup": {
                "headerposition": "Header",
                "header": "Start Time"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "cut_start_time"
            },
            "key": "cut_start_time"
          },
          {
            "id": "group-5-item-3",
            "index": 3,
            "groupId": "group-5",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "headergroup": {
                "headerposition": "Header",
                "header": "End Time"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "cut_end_time"
            },
            "key": "cut_end_time"
          },
          {
            "id": "group-5-item-4",
            "index": 4,
            "groupId": "group-5",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Header",
                "header": "Start Depth"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "cut_start_depth"
            },
            "key": "cut_start_depth"
          },
          {
            "id": "group-5-item-5",
            "index": 5,
            "groupId": "group-5",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Header",
                "header": "End Depth"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "cut_end_depth"
            },
            "key": "cut_end_depth"
          },
          {
            "id": "group-5-item-6",
            "index": 6,
            "groupId": "group-5",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Header",
                "header": "Total Feet"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "cut_total_feet"
            },
            "key": "cut_total_feet"
          },
          {
            "id": "group-5-item-7",
            "index": 7,
            "groupId": "group-5",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Header",
                "header": "# of Cars"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "cut_num_cars"
            },
            "key": "cut_num_cars"
          }
        ],
        "properties": {
          "questionheaderdisplay": "Header",
          "questionpositiongroup": "center",
          "questionwidthgroup": {
            "sizeunitvaluefield": "10",
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "questionheader": "Cut #"
        },
        "questions": [
          {
            "id": "question-group-5-0",
            "index": 0,
            "groupId": "group-5",
            "name": "1"
          },
          {
            "id": "question-group-5-1",
            "index": 1,
            "groupId": "group-5",
            "name": "2"
          },
          {
            "id": "question-group-5-2",
            "index": 2,
            "groupId": "group-5",
            "name": "3"
          },
          {
            "id": "question-group-5-3",
            "index": 3,
            "groupId": "group-5",
            "name": "4"
          },
          {
            "id": "question-group-5-4",
            "index": 4,
            "groupId": "group-5",
            "name": "5"
          },
          {
            "id": "question-group-5-5",
            "index": 5,
            "groupId": "group-5",
            "name": "6"
          },
          {
            "id": "question-group-5-6",
            "index": 6,
            "groupId": "group-5",
            "name": "7"
          },
          {
            "id": "question-group-5-7",
            "index": 7,
            "groupId": "group-5",
            "name": "8"
          }
        ]
      },
      {
        "id": "group-6",
        "name": "group-6",
        "index": 6,
        "items": [
          {
            "id": "group-6-item-0",
            "index": 0,
            "groupId": "group-6",
            "content": "",
            "properties": {
              "type": "DropDown",
              "dropdowndatasourcetype": "Custom",
              "headergroup": {
                "headerposition": "Header",
                "header": "Type"
              },
              "labelgroup": {
                "label": "DropDown",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "down_type",
              "data": [
                {
                  "itemId": "1",
                  "answer": "Inspection"
                },
                {
                  "itemId": "2",
                  "answer": "Travel"
                },
                {
                  "itemId": "3",
                  "answer": "Planned"
                },
                {
                  "itemId": "4",
                  "answer": "Unplanned"
                }
              ]
            },
            "key": "down_type"
          },
          {
            "id": "group-6-item-1",
            "index": 1,
            "groupId": "group-6",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Header",
                "header": "Duration of Delay (min)"
              },
              "labelgroup": {
                "label": "",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "down_duration"
            },
            "key": "down_duration"
          },
          {
            "id": "group-6-item-2",
            "index": 2,
            "groupId": "group-6",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "headergroup": {
                "headerposition": "Header",
                "header": "Time Down"
              },
              "labelgroup": {
                "label": "",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "down_start_time"
            },
            "key": "down_start_time"
          },
          {
            "id": "group-6-item-3",
            "index": 3,
            "groupId": "group-6",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "number",
              "headergroup": {
                "headerposition": "Header",
                "header": "Actual Production Lost Time (min)"
              },
              "labelgroup": {
                "label": "",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "down_production_lost_time"
            },
            "key": "down_production_lost_time"
          },
          {
            "id": "group-6-item-4",
            "index": 4,
            "groupId": "group-6",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "headergroup": {
                "headerposition": "Header",
                "header": "Time Repaired"
              },
              "labelgroup": {
                "label": "",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "down_end_time"
            },
            "key": "down_end_time"
          },
          {
            "id": "group-6-item-5",
            "index": 5,
            "groupId": "group-6",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "text",
              "headergroup": {
                "headerposition": "Header",
                "header": "Equipment ID"
              },
              "labelgroup": {
                "label": "",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "down_equipment_id"
            },
            "key": "down_equipment_id"
          },
          {
            "id": "group-6-item-6",
            "index": 6,
            "groupId": "group-6",
            "content": "",
            "properties": {
              "type": "Textarea",
              "headergroup": {
                "headerposition": "Header",
                "header": "Reason for Downtime/Comments"
              },
              "labelgroup": {
                "label": "",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": "40",
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": "120",
                "heightunittypefield": "px",
                "value": "px"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "down_reason"
            },
            "key": "down_reason"
          }
        ],
        "properties": {
          "questionheaderdisplay": "Header",
          "questionpositiongroup": "center",
          "questionwidthgroup": {
            "sizeunitvaluefield": "10",
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "questionheader": "Down #"
        },
        "questions": [
          {
            "id": "question-group-6-0",
            "index": 0,
            "groupId": "group-6",
            "name": "1"
          },
          {
            "id": "question-group-6-1",
            "index": 1,
            "groupId": "group-6",
            "name": "2"
          },
          {
            "id": "question-group-6-2",
            "index": 2,
            "groupId": "group-6",
            "name": "3"
          },
          {
            "id": "question-group-6-3",
            "index": 3,
            "groupId": "group-6",
            "name": "4"
          },
          {
            "id": "question-group-6-4",
            "index": 4,
            "groupId": "group-6",
            "name": "5"
          },
          {
            "id": "question-group-6-5",
            "index": 5,
            "groupId": "group-6",
            "name": "6"
          }
        ]
      },
      {
        "id": "group-7",
        "name": "group-7",
        "index": 7,
        "items": [
          {
            "id": "group-7-item-0",
            "index": 0,
            "groupId": "group-7",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "text",
              "headergroup": {
                "headerposition": "Header",
                "header": "Move From"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "section_move_from"
            },
            "key": "section_move_from"
          },
          {
            "id": "group-7-item-1",
            "index": 1,
            "groupId": "group-7",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "text",
              "headergroup": {
                "headerposition": "Header",
                "header": "Move To"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "section_move_to"
            },
            "key": "section_move_to"
          },
          {
            "id": "group-7-item-2",
            "index": 2,
            "groupId": "group-7",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "headergroup": {
                "headerposition": "Header",
                "header": "Start Time"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "section_move_start_time"
            },
            "key": "section_move_start_time"
          },
          {
            "id": "group-7-item-3",
            "index": 3,
            "groupId": "group-7",
            "content": "",
            "properties": {
              "type": "Date",
              "dateoptiontype": "time",
              "headergroup": {
                "headerposition": "Header",
                "header": "End Time"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "section_move_end_time"
            },
            "key": "section_move_end_time"
          },
          {
            "id": "group-7-item-4",
            "index": 4,
            "groupId": "group-7",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "text",
              "headergroup": {
                "headerposition": "Header",
                "header": "Bit Change Time (min)"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "section_move_bit_change_duration"
            },
            "key": "section_move_bit_change_duration"
          },
          {
            "id": "group-7-item-5",
            "index": 5,
            "groupId": "group-7",
            "content": "",
            "properties": {
              "type": "Input",
              "inputoptiontype": "text",
              "headergroup": {
                "headerposition": "Header",
                "header": "Total Time"
              },
              "labelgroup": {
                "label": "Input",
                "labelposition": "None"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "%"
              },
              "heightgroup": {
                "sizeunitvaluefield": 20,
                "heightunittypefield": "h-fit"
              },
              "margingroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": "1",
                "unittypefield": "px"
              },
              "key": "section_move_total_duration"
            },
            "key": "section_move_total_duration"
          }
        ],
        "properties": {
          "questionheaderdisplay": "Header",
          "questionpositiongroup": "center",
          "questionwidthgroup": {
            "sizeunitvaluefield": "10",
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": "0",
            "unittypefield": "px"
          },
          "questionheader": "Section"
        },
        "questions": [
          {
            "id": "question-group-7-0",
            "index": 0,
            "groupId": "group-7",
            "name": "1"
          },
          {
            "id": "question-group-7-1",
            "index": 1,
            "groupId": "group-7",
            "name": "2"
          },
          {
            "id": "question-group-7-2",
            "index": 2,
            "groupId": "group-7",
            "name": "3"
          },
          {
            "id": "question-group-7-3",
            "index": 3,
            "groupId": "group-7",
            "name": "4"
          },
          {
            "id": "question-group-7-4",
            "index": 4,
            "groupId": "group-7",
            "name": "5"
          }
        ]
      },
      {
        "id": "group-8",
        "name": "group-8",
        "index": 8,
        "items": [
          {
            "id": "group-8-item-0",
            "index": 0,
            "groupId": "group-8",
            "content": "",
            "properties": {
              "type": "Textarea",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "Physical Condition of Section",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "w-full",
                "value": "w-full"
              },
              "heightgroup": {
                "sizeunitvaluefield": "200",
                "heightunittypefield": "px",
                "value": "px"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "section_condition",
              "placeholder": "Insert Text"
            },
            "key": "section_condition"
          }
        ],
        "properties": {
          "questionheaderdisplay": "Header",
          "questionpositiongroup": "left",
          "questionwidthgroup": {
            "sizeunitvaluefield": 20,
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          }
        },
        "questions": []
      },
      {
        "id": "group-9",
        "name": "group-9",
        "index": 9,
        "items": [
          {
            "id": "group-9-item-0",
            "index": 0,
            "groupId": "group-9",
            "content": "",
            "properties": {
              "type": "Textarea",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "Supplies Needed",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "w-full",
                "value": "w-full"
              },
              "heightgroup": {
                "sizeunitvaluefield": "200",
                "heightunittypefield": "px",
                "value": "px"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "supplies_needed",
              "placeholder": "Insert Text"
            },
            "key": "supplies_needed"
          }
        ],
        "properties": {
          "questionheaderdisplay": "Header",
          "questionpositiongroup": "left",
          "questionwidthgroup": {
            "sizeunitvaluefield": 20,
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          }
        },
        "questions": []
      },
      {
        "id": "group-10",
        "name": "group-10",
        "index": 10,
        "items": [
          {
            "id": "group-10-item-0",
            "index": 0,
            "groupId": "group-10",
            "content": "",
            "properties": {
              "type": "Textarea",
              "headergroup": {
                "headerposition": "Label"
              },
              "labelgroup": {
                "label": "Utility Work & Notes",
                "labelposition": "Header"
              },
              "fontsizegroup": {
                "fontsizeunitvaluefield": 14,
                "fontsizeunittypefield": "base"
              },
              "positiongroup": "center",
              "widthgroup": {
                "sizeunitvaluefield": 20,
                "widthunittypefield": "w-full",
                "value": "w-full"
              },
              "heightgroup": {
                "sizeunitvaluefield": "200",
                "heightunittypefield": "px",
                "value": "px"
              },
              "margingroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "margintop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "marginright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddinggroup": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingtop": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingbottom": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingleft": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "paddingright": {
                "unitvaluefield": 4,
                "unittypefield": "px"
              },
              "key": "utility_work_notes",
              "placeholder": "Insert Text"
            },
            "key": "utility_work_notes"
          }
        ],
        "properties": {
          "questionheaderdisplay": "Header",
          "questionpositiongroup": "left",
          "questionwidthgroup": {
            "sizeunitvaluefield": 20,
            "widthunittypefield": "%"
          },
          "questionfontsizegroup": {
            "fontsizeunitvaluefield": 14,
            "fontsizeunittypefield": "base"
          },
          "rowgroup": {
            "justifytypefield": "justify-normal",
            "flextypefield": "flex-nowrap"
          },
          "margingroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "margintop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "marginright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddinggroup": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingtop": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingbottom": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingleft": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          },
          "paddingright": {
            "unitvaluefield": 4,
            "unittypefield": "px"
          }
        },
        "questions": []
      }
    ]
  },
  "style": {}
}