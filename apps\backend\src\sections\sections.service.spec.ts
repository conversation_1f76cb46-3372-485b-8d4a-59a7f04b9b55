import { Test, TestingModule } from '@nestjs/testing';
import { SectionsService } from './sections.service';
import typeorm = require('typeorm');
import { getRepositoryToken } from '@nestjs/typeorm';
import { Section } from './entities/section.entity';

describe('SectionsService', () => {
  let service: SectionsService;
  let sectionRepository: typeorm.Repository<Section>;
  const SECTION_REPOSITORY_TOKEN = getRepositoryToken(Section);

  const mockSectionRepository = {
    find: jest.fn(),
    findOneBy: jest.fn(),
  };

  const expectedSections = [
    {
      id: 1,
      mineId: 1,
      sectionName: "Section A",
      isActive: 1
    },
    {
      id: 2,
      mineId: 1,
      sectionName: "Section B",
      isActive: 1
    },
    {
      id: 2,
      mineId: 1,
      sectionName: "Section C",
      isActive: 1
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SectionsService,
        {
          provide: SECTION_REPOSITORY_TOKEN,
          useValue: mockSectionRepository
        },
      ],
    }).compile();

    service = module.get<SectionsService>(SectionsService);
    sectionRepository = module.get<typeorm.Repository<Section>>(
      SECTION_REPOSITORY_TOKEN
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('fetchData', () => {
    it('should return "section array"', async () => {
      const mineId = 1;
      const result = await service.findAll(mineId);
      expect(result).toEqual(expectedSections);
    });
  });

  it('findAll => should return an array of goals', async () => {
    // arrange
    const mineId = 1;
    const section = {
      id: 1,
      mineId: mineId,
      sectionName: "Section A",
      isActive: 1
    };
    const sections = [section];
    jest.spyOn(mockSectionRepository, 'find').mockReturnValue(sections);

    // act
    const result = await service.findAll(mineId);

    // assert
    expect(mockSectionRepository.find).toHaveBeenCalled();
    expect(result).toEqual(sections);
  });

  it('findOne => should find a goal by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const section = {
      id: 1,
      mineId: 1,
      sectionName: "Section A",
      isActive: 1
    };

    jest.spyOn(mockSectionRepository, 'findOneBy').mockReturnValue(section);

    // act
    const result = await service.findOne(id);

    // assert
    expect(mockSectionRepository.findOneBy).toHaveBeenCalled();
    expect(mockSectionRepository.findOneBy).toHaveBeenCalledWith({id});
    expect(result).toEqual(section);
  });
});
