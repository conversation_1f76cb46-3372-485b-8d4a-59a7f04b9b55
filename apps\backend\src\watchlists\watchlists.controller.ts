import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { WatchlistsService } from './watchlists.service';
import { CreateWatchlistItemDto } from './dto/create-watchlist.dto';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';

@Controller('portal/v1/watchlistitems')
@ApiTags('watchlist')
export class WatchlistsController {
  constructor(private watchlistService: WatchlistsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  CreateWatchlistItem(
    @Body() watchlistData: CreateWatchlistItemDto,
    @Req() req
  ) {
    return this.watchlistService.createWatchlistItem(watchlistData, req.user);
  }

  @Delete('/:id')
  @UseGuards(JwtAuthGuard)
  DeleteWatchlistItem(@Param() id: number, @Req() req) {
    return this.watchlistService.deleteWatchlistItem(id, req.user);
  }

  @Post('clear-watchlist')
  @UseGuards(JwtAuthGuard)
  ClearWatchlist(@Req() req) {
    return this.watchlistService.clearWatchlist(req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  GetUserWatchlist(@Req() req) {
    return this.watchlistService.getUserWatchlist(req.user);
  }
  @Post('delete-items')
  @UseGuards(JwtAuthGuard)
  DeleteWatchlistItems(@Body() watchlistIds: String[], @Req() req) {
    return this.watchlistService.deleteWatchlistItems(watchlistIds, req.user);
  }
}
