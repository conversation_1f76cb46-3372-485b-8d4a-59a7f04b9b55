describe('Edit Option Test', () => {
  beforeEach(() => {
    // Setup code or visit the page if needed
    cy.visit('http://localhost:4200/app');
    cy.fixture('editData').then((editData) => {
      const data = editData.users[0];
      cy.get('#username').type(data.username);
      cy.wait(1000);
      cy.get('#password').type(data.password);
      cy.wait(1000);
      cy.get('#signin').click();
      cy.wait(2000);
      cy.url().should('include', '/Dashboard');
      cy.wait(1000);
      cy.get('#setting_button').click();
      cy.wait(2000);
      cy.url().should('include', '/user');
    });
  });

  it('should click on the edit icon in a table row', () => {
    cy.fixture('editData').then((editData) => {
      const data = editData.users[1];
      cy.get('.edit-icon').first().click();
      cy.wait(2000);
      // cy.get('#cancel_button').click();
      // cy.wait(2000);
      // cy.get('.edit-icon').first().click();
      // cy.wait(5000);
      cy.get('#firstName').clear().type(data.firstName);
      cy.get('#lastName').clear().type(data.lastName);
      cy.get('#username').clear().type(data.userName);
      cy.get('#create_user').click();
      cy.wait(1000);
      cy.get('#username').clear().type(data.newUserName);
      cy.get('#role').select(1);
      cy.get('#create_user').click();
    });
  });
});
