import {
	GroupProperties, InputProperties, CheckboxProperties,
	ColorProperties, DateProperties, DDLProperties,
	HeaderProperties, ImageProperties, LabelProperties,
	RadioProperties, SignatureProperties, TextareaProperties, SelectedItem,
} from './types';

export const expandItemProperties = (currentItem: SelectedItem) => {
	const { template, group, item } = currentItem;
	let properties;
	let expandedProperties;

	if(group) {
		properties = group.properties;
		expandedProperties = getItemProperties('Group');
	}
	if(item) {
		properties = item.properties;
		expandedProperties = getItemProperties(properties.type ?? 'Input');
	}

	for(const key in expandedProperties) {
		if(properties.hasOwnProperty(key)) {
			const parentProp = properties[key];
			if(expandedProperties[key].propGroup) {
				const propGroup = expandedProperties[key].propGroup;
				for(const key2 in propGroup) {
					const prop = propGroup[key2];
					if(parentProp.hasOwnProperty(key2) && prop.propType != 'button') {
						prop.value = parentProp[key2];
					}
					if(prop.promote) {
						expandedProperties[key].value = parentProp[key2];
					}
				}
			}
			else if(expandedProperties[key].buttonGroup) {
				expandedProperties[key].value = parentProp;
			}
			else {
				expandedProperties[key].value = properties[key];
			}
			if(key === 'dropdowndatasourcetype' && properties.hasOwnProperty('data')) {
				expandedProperties[key].data = properties.data;
			}
		}
	}
	return expandedProperties;
};

export const getItemProperties = (type:string) => {
	let properties;
	if(type === 'Group') { properties = JSON.parse(JSON.stringify(GroupProperties)); }
	if(type === 'Checkbox') { properties = JSON.parse(JSON.stringify(CheckboxProperties)); }
	if(type === 'Color') { properties = JSON.parse(JSON.stringify(ColorProperties)); }
	if(type === 'Date') { properties = JSON.parse(JSON.stringify(DateProperties)); }
	if(type === 'DropDown') { properties = JSON.parse(JSON.stringify(DDLProperties)); }
	if(type === 'Header') { properties = JSON.parse(JSON.stringify(HeaderProperties)); }
	if(type === 'Image') { properties = JSON.parse(JSON.stringify(ImageProperties)); }
	if(type === 'Input') { properties = JSON.parse(JSON.stringify(InputProperties)); }
	if(type === 'Label') { properties = JSON.parse(JSON.stringify(LabelProperties)); }
	if(type === 'Radio') { properties = JSON.parse(JSON.stringify(RadioProperties)); }
	if(type === 'Signature') { properties = JSON.parse(JSON.stringify(SignatureProperties)); }
	if(type === 'Textarea') { properties = JSON.parse(JSON.stringify(TextareaProperties)); }
	if(properties.type) { properties.type.value = type; }
	if(properties.text) { properties.text.value = type; }
	if(properties.labelgroup && properties.labelgroup.propGroup && properties.labelgroup.propGroup.label) {
		properties.labelgroup.propGroup.label.value = type;
	}
	return properties;
}

export const combineProperties = (target:any, source:any) => {
	const combined = {...target};

	for(const key in source) {
		if(target.hasOwnProperty(key) && key != 'type') {
			combined[key] = source[key];
		}
	}
	return combined;
}

export const getDefaults = (type:string) => {
	const extendedProperties = getItemProperties(type);
	let properties:any = {};

	for(const key in extendedProperties) {
		let parentGroup = extendedProperties[key];
		let parentDefault = parentGroup.value ?? parentGroup.defaultValue ?? null;
		let propGroup = parentGroup.propGroup ?? null;
		let buttonGroup = parentGroup.buttonGroup ?? null;
		if(propGroup) {
			for(const key2 in propGroup) {
				const prop = propGroup[key2];
				let defaultValue;
				if(key2 === 'label' || key2 === 'text') {
					defaultValue = type;
				}
				if(prop.value || prop.defaultValue || parentDefault || defaultValue) {
					if(!properties[parentGroup.name]) {
						properties[parentGroup.name] = {};
					}
					properties[parentGroup.name][key2] = propGroup[key2].value ?? propGroup[key2].defaultValue ?? parentDefault ?? defaultValue;
				}
			}
		}
		else if(buttonGroup) {
			properties[parentGroup.name] = buttonGroup.value ?? buttonGroup.defaultValue ?? parentDefault;
		}
		else if(extendedProperties[key].value || extendedProperties[key].defaultValue) {
			properties[key] = extendedProperties[key].value ?? extendedProperties[key].defaultValue;
		}
	}
	if(type !== 'Group') {
		let guid = crypto.randomUUID().toUpperCase();
		properties.key = guid;
	}
	return properties;
}