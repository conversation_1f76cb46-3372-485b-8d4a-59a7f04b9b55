import React from 'react';

export default function Loader() {
  return (
    <div
      className="inline-block h-10 w-10 animate-spin rounded-full border-4 border-solid border-white border-r-[#80c2fe] align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
      role="status"
    >
      <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
        Loading...
      </span>
    </div>
  );

  //   return (
  //     <div className="flex justify-center items-center">
  //       <div className="animate-spin rounded-full border-b-4 border-blue-600 w-8 h-8"></div>
  //     </div>
  //   );
}
