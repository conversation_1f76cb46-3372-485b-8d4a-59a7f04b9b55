import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { FormTemplate } from '../../form_templates/entities/form_template.entity';

@Entity('form_categories')
export class FormCategory {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'mine_id' })
  mineId: number;

  @Column({ nullable: false })
  name: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @Column({ name: 'is_delete', default: false, nullable: false })
  isDelete: boolean;

  @OneToMany(() => FormTemplate, formTemplate => formTemplate.formCategory)
  formTemplates: FormTemplate[]
}
