import { Body, Controller, Get, Param, Post, Patch, UseGuards, Req, Request } from '@nestjs/common';
import { FormCategoriesService } from './form_categories.service';
import { CreateFormCategoryDto } from './dto/create-form-category.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { Roles } from '../auth/gurads/roles.decorator';
import { RolesGuard } from '../auth/gurads/roles.guard';

@Controller('portal/v1/formCategories')
@ApiTags('formCategories')
export class FormCategoriesController {
  constructor(private readonly formCategoryService: FormCategoriesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() formCategoryBody: CreateFormCategoryDto, @Req() req) {
    return await this.formCategoryService.create(formCategoryBody, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Request() req) {
    let mineId = req.user.mineid;
    return this.formCategoryService.findAll(mineId);
  }

  @Get('usedByTemplates')
  @UseGuards(JwtAuthGuard)
  findUsedByTemplates(@Request() req) {
    let mineId = req.user.mineid;
    return this.formCategoryService.findUsedByTemplates(mineId);
  }

  @Get('usedBySavedForms')
  @UseGuards(JwtAuthGuard)
  findUsedBySavedForms(@Request() req) {
    let mineId = req.user.mineid;
    return this.formCategoryService.findUsedBySavedForms(mineId);
  }

  @Get('usedByCompletedForms')
  @UseGuards(JwtAuthGuard)
  findUsedByCompletedForms(@Request() req) {
    let mineId = req.user.mineid;
    return this.formCategoryService.findUsedByCompletedForms(mineId);
  }

  @Get('/:id')
  @UseGuards(JwtAuthGuard)
  findOne(@Param('id') id: number) {
    return this.formCategoryService.findOne(id);
  }

  @Patch('/isDelete/:id')
  @Roles(['admin', 'superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async deletedFormCategory(@Param('id') id: number, @Req() req) {
    let deletedFormCategory = await this.formCategoryService.deleteFormCategory(id, req.user);
    return deletedFormCategory;
  }
}
