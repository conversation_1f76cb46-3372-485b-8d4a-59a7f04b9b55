import { ApiClient } from '../apiClient';

export const getAllRoleFeatures = async () => {
  return await ApiClient.get(`/api/portal/v1/rolefeature`);
};

export const addRoleFeature = async (data: any) => {
  return await ApiClient.post(`/api/portal/v1/rolefeature`, data);
};

export const deleteRoleFeature = async (id: number) => {
  return await ApiClient.delete(`/api/portal/v1/rolefeature/${id}`);
};
