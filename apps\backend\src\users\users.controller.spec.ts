import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { User } from './entities/user.entity';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

describe('UsersController', () => {
  let usersController: UsersController;
  const mockUserService = {
    findAll: jest.fn(),
    findUserById: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
    updateUserStatus: jest.fn(),
    deleteUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUserService,
        },
      ],
    }).compile();

    usersController = module.get<UsersController>(UsersController);
  });

  it('should be defined', () => {
    expect(usersController).toBeDefined();
  });

  it('findUserById => should find a user by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const user = {
      id: 1,
      username: 'IwtUser',
      companyId: 1,
      password: '$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq',
      firstName: 'Iwt',
      lastName: 'User',
      contactNumber: null,
      jobTitle: null,
      email: '<EMAIL>',
      isActive: true,
      isDelete: false,
      createdAt: '2024-02-20T10:32:50.386Z',
      createdBy: null,
      updatedAt: '2024-02-20T10:32:50.386Z',
      updatedBy: null,
    };

    jest.spyOn(mockUserService, 'findUserById').mockReturnValue(user);

    // act
    const result = await usersController.findById(id);

    // assert
    expect(mockUserService.findUserById).toHaveBeenCalled();
    expect(mockUserService.findUserById).toHaveBeenCalledWith(+id);
    expect(result).toEqual(user);
  });

  it('changeStatus => should find a user by a given id and update its status', async () => {
    // arrange
    const id = 1;

    const user = {
      id: 1,
      username: 'IwtUser',
      companyId: 1,
      password: '$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq',
      firstName: 'Iwt',
      lastName: 'User',
      contactNumber: null,
      jobTitle: null,
      email: '<EMAIL>',
      isActive: true,
      isDelete: false,
      createdAt: '2024-02-20T10:32:50.386Z',
      createdBy: null,
      updatedAt: '2024-02-27T07:34:45.250Z',
      updatedBy: null,
    };

    jest.spyOn(mockUserService, 'updateUserStatus').mockReturnValue(user);

    // act
    const result = await usersController.updateUserStatus(id);

    // assert
    expect(mockUserService.updateUserStatus).toHaveBeenCalled();
    expect(mockUserService.updateUserStatus).toHaveBeenCalledWith(+id);
    expect(result).toMatchObject(user);
  });

  it('deleteUser => should find a user by a given id and delete', async () => {
    // arrange
    const id = 1;

    const user = {
      id: 1,
      username: 'IwtUser',
      companyId: 1,
      password: '$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq',
      firstName: 'Iwt',
      lastName: 'User',
      contactNumber: null,
      jobTitle: null,
      email: '<EMAIL>',
      isActive: true,
      isDelete: false,
      createdAt: '2024-02-20T10:32:50.386Z',
      createdBy: null,
      updatedAt: '2024-02-27T07:34:45.250Z',
      updatedBy: null,
    };

    jest.spyOn(mockUserService, 'deleteUser').mockReturnValue(user);

    // act
    const result = await usersController.deleteUser(id);

    // assert
    expect(mockUserService.deleteUser).toHaveBeenCalled();
    expect(mockUserService.deleteUser).toHaveBeenCalledWith(+id);
    expect(result).toMatchObject(user);
  });

  it('findAll => should return an array of users', async () => {
    // arrange
    let companyId = 1;
    const req = { user: { companyid: companyId } };
    const user = {
      id: 1,
      username: 'IwtUser',
      companyId: 1,
      password: '$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq',
      firstName: 'Iwt',
      lastName: 'User',
      contactNumber: null,
      jobTitle: null,
      email: '<EMAIL>',
      isActive: true,
      isDelete: false,
      createdAt: '2024-02-20T10:32:50.386Z',
      createdBy: null,
      updatedAt: '2024-02-27T07:34:45.250Z',
      updatedBy: null,
    };
    const users = [user];
    jest.spyOn(mockUserService, 'findAll').mockReturnValue(users);
    // act
    const result = await usersController.findAll(req);
    // assert
    expect(mockUserService.findAll).toHaveBeenCalled();
    expect(result).toEqual(users);
  });

  it('createUser => should create a new user by the given data', async () => {
    // arrange
    const createUserDto: CreateUserDto = {
      username: 'IwtUser',
      companyId: 1,
      password: '$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq',
      firstName: 'Iwt',
      lastName: 'User',
      email: '<EMAIL>',
      roleId: 1,
      mineId: 1,
    } as CreateUserDto;

    let companyId = 1;
    const req = { user: { companyid: companyId } };

    const newUser = {
      id: 1,
      username: 'IwtUser',
      companyId: 1,
      password: '$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq',
      firstName: 'Iwt',
      lastName: 'User',
      contactNumber: null,
      jobTitle: null,
      email: '<EMAIL>',
      isActive: true,
      isDelete: false,
      createdAt: '2024-02-20T10:32:50.386Z',
      createdBy: null,
      updatedAt: '2024-02-27T07:34:45.250Z',
      updatedBy: null,
    };

    jest.spyOn(mockUserService, 'createUser').mockReturnValue(newUser);

    // act
    const result = await usersController.createUser(createUserDto, req);

    // assert
    expect(mockUserService.createUser).toHaveBeenCalled();
    expect(mockUserService.createUser).toHaveBeenCalledWith(createUserDto, {
      user: { companyid: companyId },
    });
    expect(result).toMatchObject(newUser);
  });

  it('updateUser => should find a user by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateUserDto = {
      username: 'superUser',
      companyId: 1,
      password: '$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq',
      firstName: 'Iwt',
      lastName: 'User',
      email: '<EMAIL>',
      roleId: 1,
      mineId: 1,
      isActive: true,
      isDelete: false,
    } as UpdateUserDto;
    const user = {
      id: 1,
      username: 'IwtUser',
      companyId: 1,
      password: '$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq',
      firstName: 'Iwt',
      lastName: 'User',
      contactNumber: null,
      jobTitle: null,
      email: '<EMAIL>',
      isActive: true,
      isDelete: false,
      createdAt: '2024-02-20T10:32:50.386Z',
      createdBy: null,
      updatedAt: '2024-02-27T07:34:45.250Z',
      updatedBy: null,
    };
    jest.spyOn(mockUserService, 'updateUser').mockReturnValue(updateUserDto);
    // act
    const result = await usersController.updateUser(id, updateUserDto);
    // assert
    expect(mockUserService.updateUser).toHaveBeenCalled();
    expect(mockUserService.updateUser).toHaveBeenCalledWith(id, updateUserDto);
    expect(result).toMatchObject(user);
  });
});
