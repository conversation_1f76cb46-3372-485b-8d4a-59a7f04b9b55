import { useEffect, useRef, useState } from 'react';
import { LeftAlign, CenterAlign, RightAlign, ClearIcon } from '../../../../../assets/icons/icons';
import { toast } from 'react-toastify';
import { useUploadFile } from '../../../../../services/mutations/formtemplatemutations';

interface PropertyProps {
	id: string;
	updatePropValue: (name:string, newValue:any, parent?:string, promote?:boolean, type?: string) => void;
	clearGroupValue: (parent:string) => void;
	value: any;
	propType?: string;
	label?: string;
	hint?: string;
	parent?: string;
	name?: string;
	options?: any;
	promote?: boolean;
	icon?: any;
	action?: string;
	classes?: string;
}

const Property = ({
	updatePropValue,
	clearGroupValue,
	propType,
	parent,
	name,
	label,
	hint,
	value,
	options,
	promote,
	icon,
	action,
	classes,
}: PropertyProps) => {
	const [currentValue, setCurrentValue] = useState(value ?? null);
	const [selectedFile, setSelectedFile] = useState<any>(null);
	
	const selectFileInput = useRef<any>(null);

	const uploadFile = useUploadFile();
	
	useEffect(() => {
		setCurrentValue(value);
	}, [value]);
	
	function valueChanged(e:any) {
		setCurrentValue(e.target.value);
	}

	function updateProperty(e:any) {
		let name = e.target.name;
		let newValue = e.target.value;
		if(e.target.type === 'checkbox') {
			newValue = e.target.checked;
		}
		if(newValue !== value) {
			updatePropValue(name, newValue, parent, promote);
		}
	}

	function handleButtonClick(name:string, value:string, type:string, parent:string, action?:string) {
		if(action === 'cleargroup') {
			clearGroupValue(parent);
			return;
		}
		if(name !== value) {
			let newValue = name;
			updatePropValue(name, newValue, parent, promote, type);
		}
	}

	async function handleUploadFileButtonClick(e:any) {
		if(selectedFile) {
			if(selectedFile.size > 5000000) {
				toast.error('File size exceeds 5MB.');
				return;
			}
			if(!selectedFile.type.startsWith('image/')) {
				toast.error('Please select an image file.');
				return;
			}
			const formData = new FormData();
			formData.set('file', selectedFile);
			try {
				const res:any = await uploadFile.mutateAsync(formData);
				if(res?.status == 200 || res?.status == 201) {
					console.log(res?.data);
					let name = `${e.target.name}`;
					let newValue = res?.data?.location ?? '';
					updatePropValue(name, newValue, parent, promote, 'source');
				}
			} catch(err: any) {
				toast.success('There was an issue uploading the image.');
				console.log(err);
			}
			const reader = new FileReader();
			reader.readAsDataURL(selectedFile);

			reader.onloadend = () => {
				let name = `${e.target.name}`;
				let newValue = reader.result;
				updatePropValue(name, newValue, parent, promote, 'preview');
			};
		}
	}
	
	return (
		<>
			{propType !== 'cb' && label ? <span title={hint} className="text-white text-[14px]">{label}</span> : ''}
			{(propType === 'text') && (
				<input
					name={name}
					type="text"
					id={`prop-${name}`}
					className="bg-gray-200 p-1.5 w-full rounded pl-2 text-[14px] text-black"
					placeholder={`Enter ${name}`}
					value={currentValue ?? ''}
					onKeyDown={(e) => {
						if(e.key === 'Escape') {
							setCurrentValue(value);
						}
						e.key === 'Enter' && updateProperty(e);
					}}
					onChange={(e) => valueChanged(e)}
					onBlur={(e) => updateProperty(e)} />
			)}
			{(propType === 'number') && (
				<input name={name} type="number" id={`prop-${name}`}
					className="bg-gray-200 p-1.5 w-full rounded pl-2 text-[14px] text-black"
					value={currentValue ?? ''}
					onKeyDown={(e) => {
						if(e.key === 'Escape') {
							setCurrentValue(value);
						}
						e.key === 'Enter' && updateProperty(e);
					}}
					onChange={(e) => valueChanged(e)}
					onBlur={(e) => updateProperty(e)} />
			)}
			{(propType === 'ddl') && (
				<select name={name} id={`prop-${name}`}
					className="block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-[14px] text-black"
					value={currentValue ?? ''}
					onChange={(e) => updateProperty(e)} >
					{options?.map((i:any) => <option key={i.id ?? i} value={i.id ?? i}>{i.label ?? i}</option>)}
				</select>
			)}
			{(propType === 'cb') && (
				<div className="flex justify-center items-center mt-1">
					<div title={hint} className="text-white text-[14px] mr-2">{label}</div>
					<div className="relative w-[32px] h-[16px] rounded-full">
						<label className={'cursor-pointer'}>
							<input name={name} type="checkbox" className="sr-only peer"
								tabIndex={Number('-1')}
								checked={currentValue ?? false}
								onChange={(e) => updateProperty(e)}
							/>
							<div className="w-full h-full bg-gray-200 bg-opacity-25 border-[0.5px] border-gray-200 outline-none peer-focus:outline-none
								rounded-full peer peer-checked:after:translate-x-[162%] rtl:peer-checked:after:-translate-x-[162%]
								peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white
								after:rounded-full after:h-[10px] after:w-[10px] after:my-[1px] after:mx-[2px] after:transition-all peer-checked:bg-[#96FB60]
								peer-checked:bg-opacity-25 peer-checked:border-[1px] peer-checked:border-[#96FB60]"></div>
						</label>
					</div>
				</div>
			)}
			{(propType === 'color') && (
				<div className='color-property-container flex flex-col items-center gap-2'>
					<input name={name} type="color" id={`prop-${name}`}
						className="bg-gray-200 w-full rounded text-[14px] text-black color-property"
						value={currentValue ?? '#ffffff'}
						onKeyDown={(e) => {
							if(e.key === 'Escape') {
								setCurrentValue(value);
							}
							e.key === 'Enter' && updateProperty(e);
						}}
						onFocus={(e) => updateProperty(e)}
						onChange={(e) => valueChanged(e)}
						onBlur={(e) => updateProperty(e)} />
				</div>
			)}
			{(propType === 'button') && (
				<div id={`prop-${name}`}
					className={`${value === name ? 'button-active' : ''} ${classes ?? ''} button-property cursor-pointer`}
					onClick={() => handleButtonClick(name ?? '', value, propType, parent ?? '', action)}
				>
					{icon === 'LeftAlign' && <LeftAlign />}
					{icon === 'CenterAlign' && <CenterAlign />}
					{icon === 'RightAlign' && <RightAlign />}
					{icon === 'ClearIcon' && <ClearIcon />}
				</div>
			)}
			{(propType === 'image') && (
				<>
					<div className="flex flex-row justify-center mt-4">
						<input
							ref={selectFileInput}
							name={name}
							type="file"
							id={`prop-${name}`}
							className="hidden"
							onChange={(e: any) => setSelectedFile(e.target.files[0])} />
						<button
							id="select_file"
							title="Click to select image file"
							className={`
								text-white bg-[#29547c]/25 hover:bg-[#29547c]/50 border-[#4AA8FE]
								border-[1px] hover:border-[#4AA8FE]/75 font-medium rounded-lg text-sm
								p-2 text-center items-center basis-1/2 mr-1
							`}
							onClick={() => selectFileInput.current?.click()}
						>
							Select Image
						</button>
						<button
							id="upload_file"
							title="Click to upload image"
							name={name}
							className={`
								text-white font-medium rounded-lg text-sm bg-[#4AA8FE]
								p-2 text-center items-center basis-1/2 ml-1
								${selectedFile ? ' hover:bg-[#4AA8FE]/75' : 'cursor-default opacity-50 disabled'}
							`}
							onClick={async (e:any) => handleUploadFileButtonClick(e)}
						>
							Apply
						</button>
					</div>
					<div className="mb-4 w-full text-left mt-1 truncate">Image: {selectedFile ? selectedFile.name : ''}</div>
				</>
			)}
		</>
	);
};

export default Property;