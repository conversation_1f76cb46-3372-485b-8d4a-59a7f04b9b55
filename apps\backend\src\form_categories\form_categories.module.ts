import { Modu<PERSON> } from '@nestjs/common';
import { FormCategoriesController } from './form_categories.controller';
import { FormCategoriesService } from './form_categories.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FormCategory } from './entities/form_category.entity';
import { RolesModule } from '../roles/roles.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    RolesModule,
    UsersModule,
    TypeOrmModule.forFeature([FormCategory])
  ],
  controllers: [FormCategoriesController],
  providers: [FormCategoriesService],
  exports: [FormCategoriesService],
})
export class FormCategoriesModule {}
