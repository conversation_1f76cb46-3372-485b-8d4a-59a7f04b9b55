import { DragIcon } from '../../../../../assets/icons/icons';

interface Props {
	label: string;
	icon?: any;
}

const ToolItem = ({icon, label }: Props) => {
	return (
		<div
			className="my-3 form-designer-tool flex justify-between">
			<div className="flex-1">
				<label>{label ? label : ''}</label>
			</div>
			<div className="flex-1">
				{icon ? icon : ''}
			</div>
			<div className="">
				<DragIcon />
			</div>
		</div>
	);
};

export default ToolItem;
