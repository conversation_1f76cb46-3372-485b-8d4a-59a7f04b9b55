import React, { useState } from 'react';
import { IoMdArrowDropup } from 'react-icons/io';
import { IoMdArrowDropdown } from 'react-icons/io';
import { useRef } from 'react';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import mixpanel from 'mixpanel-browser';
import decodeJWT from '../../../utils/jwtdecoder';
import { getPageNamesFromUrl } from '../PageName';

const SortingDropdown = (data: any) => {
  const [isopen, setIsOpen] = useState(false);
  const [sortingOption, setSortingOption] = useState('first_arrival');
  let list = ['first_arrival', 'first_departure'];
  const dropdownRef = useRef(null);
  const params = useParams();
  const decode = decodeJWT();
  const url = getPageNamesFromUrl(params['*']);

  const handleSortingChange = (selectedOption: string) => {
    setSortingOption(selectedOption);
    setIsOpen(false);
    data?.setArrive(selectedOption);
    sessionStorage.setItem('sortingOption', selectedOption);
    mixpanel.track('Section Sort', {
      Sorting_Order: selectedOption,
      MshaId: decode?.mshaId,
      Page_Name: url,
    });
  };

  const savedSortingOption: any = sessionStorage.getItem('sortingOption');

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isopen) {
      window.addEventListener('click', handleClickOutside);
    }

    return () => {
      window.removeEventListener('click', handleClickOutside);
    };
  }, [isopen]);

  useEffect(() => {
    setSortingOption(savedSortingOption || 'first_arrival');
    data?.setArrive(savedSortingOption);
  }, [savedSortingOption, params['*']]);

  return (
    <div className="flex items-center text-right  ">
      <div className="text-white text-[12px] font-normal mr-4 ">Sort</div>

      <div className="relative flex item-center " ref={dropdownRef}>
        <button
          id="sortingDropdown"
          className="bg-[#253543] w-[201px] flex items-center font-normal text-xs border-[#4AA8FE] border-1  text-white pt-2 pb-2 pl-4 rounded-sm"
          onClick={() => setIsOpen((prev) => !prev)}
        >
          {sortingOption === 'first_arrival'
            ? 'First Arrival'
            : 'First Departure'}
          <div
            className={`${
              sortingOption === 'first_arrival' ? 'pl-20' : 'pl-14'
            }`}
          >
            <IoMdArrowDropdown className="h-4 w-4" />
          </div>
        </button>
        {isopen && (
          <div className="bg-[#253543]   absolute top-8 flex flex-col w-[201px] rounded-sm">
            {list
              .filter((item) => item !== sortingOption) // Filter out the selected option
              .map((item, i) => (
                <div
                  className="flex w-[201px] justify-between hover:bg-blue-400 cursor-pointer text-xs p-1 pl-4 text-white rounded-sm"
                  key={i}
                  onClick={() => handleSortingChange(item)}
                >
                  <h3 id="selectOption">
                    {item === 'first_arrival'
                      ? 'First Arrival'
                      : 'First Departure'}
                  </h3>
                </div>
              ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SortingDropdown;
