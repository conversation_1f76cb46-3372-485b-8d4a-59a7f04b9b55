import { useResetPassword } from '../../../services/mutations/usermutations';
import { Logo, UserIcon } from '../../../assets/icons/icons';
import { useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import { useNavigate } from 'react-router';
import { Link } from 'react-router-dom';
import { emailRegEx } from '../../../utils/constant';
import mainLogo from '../../../assets/Property 1=Effect.png';
import mixpanel from 'mixpanel-browser';
import decodeJWT from '../../../utils/jwtdecoder';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [showSuccessMsg, setShowSuccessMsg] = useState(false);
  const resetPassword = useResetPassword();
  const navigate = useNavigate();
  const [usernameWarningText, setUsernameWarningText] = useState('');
  const decoded = decodeJWT();
  const onFormSubmit = async (e: any) => {
    e.preventDefault();
    if (email === '') {
      setUsernameWarningText('Please enter your email address.');
    } else if (!emailRegEx.test(email)) {
      setUsernameWarningText('Please enter valid email address.');
    } else {
      try {
        const res = await resetPassword.mutateAsync({ email: email });
        if (res?.status == 200 || res?.status == 201) {
          setShowSuccessMsg(true);
          setTimeout(() => {
            navigate('/');
          mixpanel.track('Password Reset', {
            MshaId: decoded?.mshaId,
          })
          }, 5000);
        }
      } catch (err: any) {
        console.error(err?.message);
        if (err?.response?.data?.statusCode == 404) {
          setUsernameWarningText('Please enter registered email address.');
        } else if (err?.response?.data?.statusCode == 403) {
          setUsernameWarningText(
            'Reset password failed, Please contact your administrator.'
          );
        } else {
          toast?.error(err.message);
        }
      }
    }
  };
  const onChangeEmail = (event: any) => {
    setUsernameWarningText('');
    setEmail(event.target.value);
  };
  return (
    <section className="h-screen">
      <div className="h-full">
        <div className="g-6 flex h-full flex-wrap items-center justify-center lg:justify-between m-auto">
          <div className="m-auto w-[80%] md:w-full">
            <div className="shrink-1 mb-12 grow-0 basis-auto md:mb-7 md:w-9/12 md:shrink-0 lg:w-6/12 xl:w-6/12 m-auto">
              <div className="">
                <Logo className="m-auto  rounded-full" />
                <h2 className="text-center text-white text-2xl  font-semibold mt-8">
                  IWT Analytics Platform
                </h2>
                {/* <div
                  className={`${
                    window?.innerHeight < 650 ? 'h-[14vh]' : 'h-[9vh]'
                  }`}
                >
                  <Logo className="m-auto  rounded-full" />
                  <img
                    src={mainLogo}
                    alt="logo"
                    className="m-auto mb-5 mainLogo"
                  />
                </div> */}
                {/* <iframe src="https://lottie.host/embed/d87323ad-7f91-4071-a929-55c12366e229/C0vZvX27Mu.json"></iframe> */}
              </div>
            </div>

            <div>
              {' '}
              {showSuccessMsg ? (
                <div className="me-3 mb-5">
                  <h6 className="text-[16px] text-white m-auto text-center w-1/3 ">
                    Password reset request successful, <br />
                    Check your email for a temporary password. <br />
                    You will be redirected to the login page shortly
                  </h6>
                </div>
              ) : (
                <form
                  className="max-w-sm mx-auto md:w-4/5 lg:w-3/4 xl:w-1/4 "
                  onSubmit={onFormSubmit}
                >
                  <div className="me-3 mb-5">
                    <h6 className="text-[16px] text-white ">
                      Submit your registered email address and we’ll send you a
                      temporary password.
                    </h6>

                    {/* <h6 className="mt-6 text-[16px] text-white">
                      <span className="text-[#FFB132]">Don’t worry:</span>{' '}  
                      you’ll be able to change after signing in.
                    </h6> */}
                  </div>
                  <div className="my-6">
                    <div className="relative  rounded ">
                      <span>
                        <UserIcon className="absolute top-0 left-1 h-6 mr-1 my-1.5 text-white cursor-pointer"></UserIcon>
                      </span>
                      <input
                        tabIndex={1}
                        type="text"
                        autoFocus
                        id="emailToReset"
                        onChange={onChangeEmail}
                        className={`border text-white text-sm pl-8  bg-transparent focus:border-blue-500 block w-full p-2  placeholder-white outline-none autoComOff`}
                        placeholder="Enter email address"
                      />
                    </div>
                    <div className="text-start text-xs text-red-500 font-semibold pt-1">
                      {usernameWarningText}
                    </div>
                  </div>

                  {/* <!-- submit button --> */}
                  <div className="text-center md:text-right h-full  sm:items-center  grid md:grid-cols-2 sm:grid-cols-1">
                    <button
                      id="resetPass"
                      tabIndex={2}
                      type="submit"
                      className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/90 font-medium rounded-lg text-sm px-4 py-2 text-center  items-center  me-2 mb-2"
                    >
                      Reset Password
                    </button>

                    <div>
                      <span className="md:mt-[-6px] mt-[3px]">
                        <span className="text-white text-sm font-normal text-center md:text-right w-fit">
                          Back to{' '}
                        </span>
                        <Link
                          tabIndex={3}
                          to={`/app`}
                          className="cursor-pointer w-fit text-[#FFB132]"
                        >
                          <span
                            id="backToSignInPage"
                            className="text-[#FFB132] underline underline-offset-1 font-semibold w-fit"
                          >
                            Sign In
                          </span>
                        </Link>
                      </span>
                    </div>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
      <ToastContainer
        autoClose={2000}
        hideProgressBar={true}
        newestOnTop={false}
        closeOnClick
        // rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </section>
  );
}
