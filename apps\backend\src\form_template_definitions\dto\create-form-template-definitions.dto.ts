import {
  IsDef<PERSON>,
  <PERSON><PERSON><PERSON>Empty,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateFormTemplateDefinitionDto {
  @IsString({ message: 'Definition must be a string' })
  @IsNotEmpty()
  @ApiProperty()
  definition: string;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  mineId: number;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  formTemplateId: number;
  
  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  major: number;
  
  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  minor: number;
  
  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  revision: number;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
