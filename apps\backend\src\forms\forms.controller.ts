import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Req,
} from '@nestjs/common';
import { Form } from './entities/forms.entity';
import { FormsService } from './forms.service';
import { CreateFormDto } from './dto/create-forms.dto';
import { UpdateFormDto } from './dto/update-forms.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { Roles } from '../auth/gurads/roles.decorator';
import { RolesGuard } from '../auth/gurads/roles.guard';

@Controller('portal/v1/forms')
@ApiTags('forms')
export class FormsController {
  constructor(private readonly formsService: FormsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() createFormDto: CreateFormDto, @Req() req) {
    return this.formsService.create(createFormDto, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Request() req) {
    let mineId = req.user.mineid;
    return this.formsService.findAll(mineId);
  }

  @Get('inprogress')
  @UseGuards(JwtAuthGuard)
  async findInProgressForms(@Request() req) {
    let mineId = req.user.mineid;
    return this.formsService.findInProgressForms(mineId);
  }

  @Get('inprogressbyuser')
  @UseGuards(JwtAuthGuard)
  async findInProgressFormsByUser(@Request() req) {
    let userId = req.user.userId;
    return this.formsService.findInProgressFormsByUser(userId);
  }

  @Get('submitted')
  @UseGuards(JwtAuthGuard)
  async findSubmittedForms(@Request() req) {
    let mineId = req.user.mineid;
    return this.formsService.findSubmittedForms(mineId);
  }

  @Get('submittedbyuser')
  @UseGuards(JwtAuthGuard)
  async findSubmittedFormsByUser(@Request() req) {
    let userId = req.user.userId;
    return this.formsService.findSubmittedFormsByUser(userId);
  }

  @Get('submitted/:date')
  @UseGuards(JwtAuthGuard)
  async findSubmittedFormsByDate(@Request() req, @Param('date') date: string) {
    let mineId = req.user.mineid;
    return this.formsService.findSubmittedForms(mineId, date);
  }

  @Get('submittedbyuser/:date')
  @UseGuards(JwtAuthGuard)
  async findSubmittedFormsByUserByDate(@Request() req, @Param('date') date: string) {
    let userId = req.user.userId;
    return this.formsService.findSubmittedFormsByUser(userId, date);
  }

  @Get('name/:name')
  @UseGuards(JwtAuthGuard)
  async findByName(@Param('name') name: string, @Req() req): Promise<Form> {
    let mineId = req.user.mineid;
    const decodedName = decodeURIComponent(name);
    return await this.formsService.findByName(mineId, decodedName);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: number) {
    return this.formsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  async update(@Param('id') id: number, @Body() updateFormDto: UpdateFormDto, @Req() req) {
    return this.formsService.update(id, updateFormDto, req.user);
  }

  @Patch('submit/:id')
  @UseGuards(JwtAuthGuard)
  async submit(@Param('id') id: number, @Req() req) {
    return this.formsService.submit(id, req.user);
  }

  @Patch('revoke/:id')
  @UseGuards(JwtAuthGuard)
  async revoke(@Param('id') id: number, @Req() req) {
    return this.formsService.revoke(id, req.user);
  }

  @Patch('/isProcessed/:id')
  @Roles(['admin', 'superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async processForm(@Param('id') id: number, @Req() req) {
    let processedForm = await this.formsService.processForm(id, req.user);
    return processedForm;
  }

  @Patch('/isDelete/:id')
  @Roles(['admin', 'superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async deleteForm(@Param('id') id: number, @Req() req) {
    let deletedForm = await this.formsService.deleteForm(id, req.user);
    return deletedForm;
  }

  @Patch('submission/:id')
  @UseGuards(JwtAuthGuard)
  async updateSubmissionContent(@Param('id') id: number) {
    return this.formsService.updateSubmissionContent(id);
  }

  @Get('toprocess/:mineId')
  @UseGuards(JwtAuthGuard)
  async getFormsToProcess(@Param('mineId') mineId: number) {
    return this.formsService.getFormsToProcess(mineId);
  }
}
