import Plot from 'react-plotly.js';

export default function FillAreaGraph() {
  const layout = {
    barmode: 'stack',
    xaxis: {
      title: 'Day',
      type: 'category',
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      tickangle: 45,
      width: 1.5,
      // tickmode: 'array',
    },
    yaxis: {
      title: 'Feet Mined',
      type: 'linear',
      range: [350, 480],
      tickvals: [
        350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 460, 470,
      ],
      ticktext: [
        350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 460, 470,
      ],
      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 50%)',
      // zeroline: true,
      // zerolinecolor: 'rgba(74, 168, 256, 50%)',
    },
    showlegend: false,
    margin: { t: 20, l: 60, b: 30, r: 30 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
  };

  const data = [
    {
      x: [
        'AUG15',
        'AUG17',
        'AUG19',
        'AUG21',
        'AUG23',
        'AUG25',
        'AUG27',
        'AUG29',
        'AUG31',
        'SEP2',
        'SEP4',
        'SEP6',
        'SEP8',
        'SEP10',
        'SEP12',
      ],
      y: [
        412, 453, 448, 397, 437, 455, 415, 452, 410, 445, 430, 390, 435, 425,
        380,
      ],
      type: 'scatter',
      marker: {
        // color: 'rgba(255, 194, 94, 1)',
      },
      fill: 'tozeroy',
      fillcolor: 'rgba(56, 134, 206, 0.44)/20',
      mode: 'lines',
      // line: { color: 'rgba(255, 194, 94, 1)' },
    },

    {
      x: [
        'AUG15',
        'AUG17',
        'AUG19',
        'AUG21',
        'AUG23',
        'AUG25',
        'AUG27',
        'AUG29',
        'AUG31',
        'SEP2',
        'SEP4',
        'SEP6',
        'SEP8',
        'SEP10',
        'SEP12',
      ],
      y: [
        430, 430, 430, 430, 430, 430, 430, 430, 430, 430, 430, 430, 430, 430,
        430,
      ],
      type: 'scatter',
      marker: {
        color: 'rgba(255, 194, 94, 1)',
      },
      fillcolor: 'rgba(255, 194, 94, 0.5)',
      mode: 'lines',
      line: {
        color: 'white',
        dash: 'dot',
      },
    },
  ];

  return (
    <div className="w-full h-64" style={{ overflow: 'hidden' }}>
      <Plot
        className="w-full h-64 bg-transparent"
        data={data}
        layout={layout}
        config={{ displayModeBar: false, responsive: true }}
      />
    </div>
  );
}
