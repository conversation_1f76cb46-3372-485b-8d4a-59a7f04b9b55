import { Test, TestingModule } from '@nestjs/testing';
import { ShiftsService } from './shifts.service';
import typeorm = require('typeorm');
import { getRepositoryToken } from '@nestjs/typeorm';
import { Shift } from './entities/shift.entity';

describe('ShiftsService', () => {
  let service: ShiftsService;
  let shiftRepository: typeorm.Repository<Shift>;
  const SHIFT_REPOSITORY_TOKEN = getRepositoryToken(Shift);

  const mockShiftRepository = {
    find: jest.fn(),
    findOneBy: jest.fn(),
  };

  const expectedShifts = [
    {
      id: 1,
      mineId: 1,
      shiftName: "Day Shift",
      startTime: "06:00",
      endTime: "14:00",
      isActive: 1
    },
    {
      id: 2,
      mineId: 1,
      shiftName: "Evening Shift",
      startTime: "14:00",
      endTime: "22:00",
      isActive: 1
    },
    {
      id: 2,
      mineId: 1,
      shiftName: "Night Shift",
      startTime: "22:00",
      endTime: "06:00",
      isActive: 1
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ShiftsService,
        {
          provide: SHIFT_REPOSITORY_TOKEN,
          useValue: mockShiftRepository
        },
      ],
    }).compile();

    service = module.get<ShiftsService>(ShiftsService);
    shiftRepository = module.get<typeorm.Repository<Shift>>(
      SHIFT_REPOSITORY_TOKEN
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('fetchData', () => {
    it('should return "shift array"', async () => {
      const mineId = 1;
      const result = await service.findAll(mineId);
      expect(result).toEqual(expectedShifts);
    });
  });

  it('findAll => should return an array of goals', async () => {
    // arrange
    const mineId = 1;
    const shift = {
      id: 1,
      mineId: mineId,
      shiftName: "Day Shift",
      startTime: "06:00",
      endTime: "14:00",
      isActive: 1
    };
    const shifts = [shift];
    jest.spyOn(mockShiftRepository, 'find').mockReturnValue(shifts);

    // act
    const result = await service.findAll(mineId);

    // assert
    expect(mockShiftRepository.find).toHaveBeenCalled();
    expect(result).toEqual(shifts);
  });

  it('findOne => should find a goal by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const shift = {
      id: 1,
      mineId: 1,
      shiftName: "Day Shift",
      startTime: "06:00",
      endTime: "14:00",
      isActive: 1
    };

    jest.spyOn(mockShiftRepository, 'findOneBy').mockReturnValue(shift);

    // act
    const result = await service.findOne(id);

    // assert
    expect(mockShiftRepository.findOneBy).toHaveBeenCalled();
    expect(mockShiftRepository.findOneBy).toHaveBeenCalledWith({id});
    expect(result).toEqual(shift);
  });
});
