import { FormInput, Group, Item, Question } from '../types';
import FormItem from "./FormItem";
import FormItemHeader from "./FormItemHeader";

interface Props {
	group: Group;
	items: Item[];
	questions: Question[];
	saveInputValue: (input: FormInput) => void;
	readOnly: boolean;
}

interface Style {
	transform?: any,
	transition?: any,
	backgroundColor?: string,
	color?: string,
	margin?: string,
	marginTop?: string,
	marginBottom?: string,
	marginLeft?: string,
	marginRight?: string,
	padding?: string,
	paddingTop?: string,
	paddingBottom?: string,
	paddingLeft?: string,
	paddingRight?: string,
	width?: string,
	fontSize?: string,
}

const FormGroup = ({group, items, questions, saveInputValue, readOnly}: Props) => {
	const properties = group.properties;

	const header = properties?.questionheader ?? '';
	const headerdisplay = properties?.questionheaderdisplay ?? null;
	const positiongroup = properties?.questionpositiongroup ?? null;
	const fUnitValueField = properties?.questionfontsizegroup?.fontsizeunitvaluefield ?? null;
	const fUnitTypeField = properties?.questionfontsizegroup?.fontsizeunittypefield ?? null;

	const textcolor = properties?.colorgroup?.color ?? null;
	const bgcolor = properties?.bgcolorgroup?.bgcolor ?? null;

	const wSizeValueField = properties?.questionwidthgroup?.sizeunitvaluefield ?? null;
	const wSizeUnitTypeField = properties?.questionwidthgroup?.widthunittypefield ?? null;
	const mUnitValueField = properties?.margingroup?.unitvaluefield ?? null;
	const mUnitTypeField = properties?.margingroup?.unittypefield ?? null;
	const mtUnitValueField = properties?.margintop?.unitvaluefield ?? null;
	const mtUnitTypeField = properties?.margintop?.unittypefield ?? null;
	const mbUnitValueField = properties?.marginbottom?.unitvaluefield ?? null;
	const mbUnitTypeField = properties?.marginbottom?.unittypefield ?? null;
	const mlUnitValueField = properties?.marginleft?.unitvaluefield ?? null;
	const mlUnitTypeField = properties?.marginleft?.unittypefield ?? null;
	const mrUnitValueField = properties?.marginright?.unitvaluefield ?? null;
	const mrUnitTypeField = properties?.marginright?.unittypefield ?? null;
	const pUnitValueField = properties?.paddinggroup?.unitvaluefield ?? null;
	const pUnitTypeField = properties?.paddinggroup?.unittypefield ?? null;
	const ptUnitValueField = properties?.paddingtop?.unitvaluefield ?? null;
	const ptUnitTypeField = properties?.paddingtop?.unittypefield ?? null;
	const pbUnitValueField = properties?.paddingbottom?.unitvaluefield ?? null;
	const pbUnitTypeField = properties?.paddingbottom?.unittypefield ?? null;
	const plUnitValueField = properties?.paddingleft?.unitvaluefield ?? null;
	const plUnitTypeField = properties?.paddingleft?.unittypefield ?? null;
	const prUnitValueField = properties?.paddingright?.unitvaluefield ?? null;
	const prUnitTypeField = properties?.paddingright?.unittypefield ?? null;
	const justifytypefield = properties?.rowgroup?.justifytypefield ?? null;
	const flextypefield = properties?.rowgroup?.flextypefield ?? null;
	
	const containerStyle:Style = {};
	const style:Style = {};
	const questionStyle:Style = {};
	let margin, marginTop, marginBottom, marginLeft, marginRight;
	if(mUnitTypeField !== 'individual') {
		margin = `${mUnitValueField ?? ''}${mUnitTypeField ?? ''}`;
	}
	else {
		marginTop = `${mtUnitValueField ?? ''}${mtUnitTypeField ?? ''}`;
		marginBottom = `${mbUnitValueField ?? ''}${mbUnitTypeField ?? ''}`;
		marginLeft = `${mlUnitValueField ?? ''}${mlUnitTypeField ?? ''}`;
		marginRight = `${mrUnitValueField ?? ''}${mrUnitTypeField ?? ''}`;
	}

	let padding, paddingTop, paddingBottom, paddingLeft, paddingRight;
	if(pUnitTypeField !== 'individual') {
		padding = `${pUnitValueField ?? ''}${pUnitTypeField ?? ''}`;
	}
	else {
		paddingTop = `${ptUnitValueField ?? ''}${ptUnitTypeField ?? ''}`;
		paddingBottom = `${pbUnitValueField ?? ''}${pbUnitTypeField ?? ''}`;
		paddingLeft = `${plUnitValueField ?? ''}${plUnitTypeField ?? ''}`;
		paddingRight = `${prUnitValueField ?? ''}${prUnitTypeField ?? ''}`;
	}
	
	let width = '';
	let widthClass = '';
	if(wSizeUnitTypeField) {
		let type = wSizeUnitTypeField ?? 'w-fit';
		let value = wSizeValueField ?? 0;
		if(type == 'px' || type == '%') {
			width = `${value}${type}`;
		}
		else {
			widthClass = type;
		}
	}
	
	let fontSize = '';
	let fontSizeClass = '';
	if(fUnitTypeField) {
		let type = fUnitTypeField ?? 'base';
		let value = fUnitValueField ?? 1;
		if(type == 'px' || type == 'rem') {
			fontSize = `${value}${type}`;
		}
		else {
			fontSizeClass = type;
		}
	}

	let justifyClass = '';
	if(justifytypefield) {
		justifyClass = justifytypefield ?? 'justify-normal';
	}

	let flexClass = '';
	if(flextypefield) {
		flexClass = flextypefield ?? 'flex-nowrap';
	}
	
	const backgroundColor = `${bgcolor ?? ''}`;
	const color = `${textcolor ?? ''}`;

	if(fontSize && fontSize.length > 0) {style.fontSize = fontSize;}
	if(backgroundColor && backgroundColor.length > 0) {containerStyle.backgroundColor = backgroundColor;}
	if(color && color.length > 0) {style.color = color;}
	if(margin && margin.length > 0) {style.margin = margin;}
	if(marginTop && marginTop.length > 0) {style.marginTop = marginTop;}
	if(marginBottom && marginBottom.length > 0) {style.marginBottom = marginBottom;}
	if(marginLeft && marginLeft.length > 0) {style.marginLeft = marginLeft;}
	if(marginRight && marginRight.length > 0) {style.marginRight = marginRight;}
	if(padding && padding.length > 0) {style.padding = padding;}
	if(paddingTop && paddingTop.length > 0) {style.paddingTop = paddingTop;}
	if(paddingBottom && paddingBottom.length > 0) {style.paddingBottom = paddingBottom;}
	if(paddingLeft && paddingLeft.length > 0) {style.paddingLeft = paddingLeft;}
	if(paddingRight && paddingRight.length > 0) {style.paddingRight = paddingRight;}
	if(width && width.length > 0) {questionStyle.width = width;}

	return (
		<div
			style={containerStyle}
			className={`
				relative my-2 w-full flex flex-col overflow-x-auto
			`}
			key={group.name}
		>
			{headerdisplay && headerdisplay === 'Header' &&
				<div
					style={style} className={`
						flex flex-grow overflow-x-hidden overflow-y-auto items-top
						${justifyClass}
						${flexClass}
					`}>
					{header && questions.length > 0 &&
						<div
							style={questionStyle}
							className={`
								p-2 flex items-top flex-wrap h-fit
								text-${fontSizeClass ?? ''}
								text-${positiongroup ?? 'left'}
								${justifyClass}
								${widthClass ?? ''}
							`}
						>
							<label className={`w-full block`}>{header}</label>	
						</div>
					}
					{items.map(item => (
						<FormItemHeader
							key={`${item.id}-label`}
							item={item}
						/>
					))}
				</div>
			}
			{questions.length > 0 && questions.map((question,index) => {
				return (
					<div
						style={style}
						key={question.id}
						className={`
							flex flex-grow overflow-x-hidden overflow-y-auto items-start
							${justifyClass}
							${flexClass}
						`}>
						<div
							style={questionStyle}
							className={`
								text-${fontSizeClass ?? ''}
								text-${positiongroup ?? 'left'}
								${widthClass ?? ''}
								px-2 py-4 form-question
							`}
						>{question.name}</div>
						{items.map(item => (
							<FormItem
								key={`${question.id}-${item.id}`}
								itemGroup={`${group.id}-${question.id}`}
								item={item}
								questionId={question.id}
								saveInputValue={saveInputValue}
								readOnly={readOnly}
								questionIndex={index}
							/>
						))}
					</div>
				)
			})}
			{questions.length <= 0 &&
				<div
					style={style} className={`
						flex flex-grow overflow-x-hidden overflow-y-auto
						${justifyClass}
						${flexClass}
					`}>
					{items.map(item => (
						<FormItem
							key={item.id}
							itemGroup={`${group.id}`}
							item={item}
							saveInputValue={saveInputValue}
							readOnly={readOnly}
							questionIndex={0}
						/>
					))}
				</div>
			}
		</div>
	);
};

export default FormGroup;