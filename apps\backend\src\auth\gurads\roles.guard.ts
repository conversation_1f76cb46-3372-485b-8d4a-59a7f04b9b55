import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { Roles } from './roles.decorator';
import { CreateUserDto } from '../../users/dto/create-user.dto';
import { UsersService } from '../../users/users.service';
import { Features } from './feature.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private userService: UsersService
  ) {}

  async matchRoles(allowedRoles: any, allowedFeatures: any, user: any) {
    const featureData = await this.userService.getUsersWithRole(user.userId);
    const roleAllowed = featureData?.find((x) => {
      return allowedRoles?.some((element) => {
        return x.RoleName === element;
      });
    });

    const featureAllowed = featureData?.find((x) => {
      return allowedFeatures?.some((element) => {
        return x.FeatureName == element[0]?.toUpperCase() + element?.slice(1);
      });
    });

    if (
      (allowedRoles != undefined && !roleAllowed) ||
      (allowedFeatures != undefined && !featureAllowed)
    ) {
      return false;
    }
    return true;
  }

  canActivate(
    context: ExecutionContext
  ): boolean | Promise<boolean> | Observable<boolean> {
    const roles = this.reflector.get(Roles, context.getHandler());
    const features = this.reflector.get(Features, context.getHandler());

    if (!roles && !features) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    return this.matchRoles(roles, features, user);
  }
}
