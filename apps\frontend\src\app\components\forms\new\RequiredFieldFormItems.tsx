import { IwtEnv } from '../../../../api/apiClient';
import { useEffect, useState } from 'react';
import { FormInput, Id, Item } from "../types";
import { useQuery } from '@tanstack/react-query';
import { getActiveShifts } from '../../../../api/shifts/shiftapis';
import { getSections } from '../../../../api/sections/sectionapis';

interface Props {
	item: Item;
	saveInputValue: (input: FormInput) => void;
}

function RequiredFieldFormItems({ item, saveInputValue }: Props) {
	const properties = item.properties;
	const placeholder = properties?.placeholder ?? null;
	const typeValue = properties?.type ?? 'Input';
	const inputTypeOption = properties?.inputoptiontype ?? 'text';
	const dateTypeOption = properties?.dateoptiontype ?? 'date';
	const label = properties?.labelgroup?.label ?? properties.label ?? null;
	const dropdowndatasourcetype = properties?.dropdowndatasourcetype ?? null;
	
	let dropDownData = [];
	if(dropdowndatasourcetype == 'Custom' && properties?.data) {
		dropDownData = properties?.data;
	}
	
	const [formInputValue, setFormInputValue] = useState<any>();
	const [currentFormInputValue, setCurrentFormInputValue] = useState<any>();
	const [dropDownListData, setDropDownListData] = useState(dropDownData ?? []);
	
	const {
		data: getShiftRes,
	} = useQuery({
		queryKey: ['active-shifts'],
		queryFn: getActiveShifts,
		refetchOnWindowFocus: false,
		enabled: (typeValue === 'DropDown' && dropdowndatasourcetype === 'Shifts')
	});
	const {
		data: getSectionRes,
	} = useQuery({
		queryKey: ['all-sections'],
		queryFn: getSections,
		refetchOnWindowFocus: false,
		enabled: (typeValue === 'DropDown' && dropdowndatasourcetype === 'Sections')
	});
  
	useEffect(() => {
		if(getShiftRes?.data.length > 0 && dropdowndatasourcetype === 'Shifts') {
			setDropDownListData(getShiftRes?.data.map((shift:any) => {
				return {itemId: shift.id, answer:shift.shiftName}
			}));
		}
		if(getSectionRes?.data.length > 0 && dropdowndatasourcetype === 'Sections') {
			setDropDownListData(getSectionRes?.data.map((section:any) => {
				return {itemId: section.id, answer:section.sectionName}
			}));
		}
	}, [getShiftRes?.data, getSectionRes?.data]);
	
	function inputValueChanged(e:any) {
		let newValue = e.target.value;
		setCurrentFormInputValue(newValue);
	}

	function handleInputChange(e:any) {
		let newValue = e.target.value;
		let name = e.target.name;

		if(typeValue == 'Radio') {
			const radioGroup = document.getElementsByName(name);
			
			radioGroup.forEach((radio:any) => {
				if(radio.checked) {
					newValue = radio.value;
				}
			});
		}
		if(typeValue == 'Checkbox') {
			newValue = null;
			if(e.target.checked) {
				newValue = e.target.value;
			}
		}
		if(typeValue == 'DropDown') {
			newValue = {
				itemId: e.target.value,
				answer: e.target.options[e.target.selectedIndex].text
			};
		}
		if(newValue !== formInputValue) {
			const inputToSave: FormInput = {
				item,
				value: newValue,
			};
			saveInputValue(inputToSave);
		}
	};
	
	return (
		<div className="grid grid-cols-1">
			<div className="mx-2">
			<span className="text-white text-[12px]">
				{label}
				<span className="text-red-600 font-medium ml-[1px]">
				*
				</span>
			</span>
			{typeValue == 'Date' && (
				<input
					value={currentFormInputValue ?? ''}
					className={`bg-gray-200 p-1.5 w-full rounded text-black form-element`}
					type={dateTypeOption}
					onChange={(e) => inputValueChanged(e)}
					onBlur={(e) => handleInputChange(e)}
					onKeyDown={(e) => {
						e.key === 'Enter' && handleInputChange(e);
						if(e.key === 'Escape') {
							setCurrentFormInputValue(formInputValue);
						}
					}}
				/>
			)}
			{typeValue == 'DropDown' && (
				<select
					defaultValue={-1}
					value={currentFormInputValue ?? undefined}
					className={`block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-black form-element`}
					onChange={(e) => handleInputChange(e)}
				>
					<option disabled value={-1} className="text-gray-300 bg-gray-500">Select an option</option>
					{dropDownListData.map((o:any) => <option value={o.itemId} key={o.itemId} label={o.answer}>{o.answer}</option>)}
				</select>
			)}
			{typeValue == 'Input' && (
				<input
					value={currentFormInputValue ?? ''}
					className={`bg-gray-200 p-1.5 w-full rounded text-black form-element`}
					type={inputTypeOption}
					placeholder={placeholder ?? null}
					onChange={(e) => inputValueChanged(e)}
					onBlur={(e) => handleInputChange(e)}
					onKeyDown={(e) => {
						e.key === 'Enter' && handleInputChange(e);
						if(e.key === 'Escape') {
							setCurrentFormInputValue(formInputValue);
						}
					}}
				/>
			)}
			{typeValue == 'Textarea' && (
				<textarea
					className={`bg-gray-200 p-1.5 w-full rounded text-black min-h-[100px]`}
					placeholder={placeholder ?? null}
					value={currentFormInputValue ?? ''}
					onChange={(e) => inputValueChanged(e)}
					onBlur={(e) => handleInputChange(e)}
					onKeyDown={(e) => {
						e.key === 'Enter' && handleInputChange(e);
						if(e.key === 'Escape') {
							setCurrentFormInputValue(formInputValue);
						}
					}}
				></textarea>
			)}
			</div>
			<div></div>
		</div>
	)
}

export default RequiredFieldFormItems;