import { Down<PERSON>rrow, TriangleDownIcon, TriangleUpIcon, UpArrow,DualArrowTextIcon,DescendingIcon,AscendingIcon } from '../../../assets/icons/icons';
import dayjs from 'dayjs';
import React from 'react';
import { ChangeEvent, useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { escapeRegExp } from '../../../utils/constant';
import { getPageNamesFromUrl } from '../PageName';
import mixpanel from 'mixpanel-browser';

export interface ColumnDef {
  key: string;
  label: string;
  type:
    | 'text'
    | 'number'
    | 'date'
    | 'datetime'
    | 'sr_no'
    | 'checkbox'
    | 'time'
    | 'element'
    | 'action';
  truncate?: boolean;
  onChange?: (event: ChangeEvent<HTMLInputElement>, row: any) => void;
  render?: (row: any) => JSX.Element;
  computedValue?: (row: any) => string;
  separateLine?: boolean;
}

export type FetchRowsCallback = (
  pageNo: number,
  rowsPerPage: number,
  sortColumn?: string | null,
  isAscending?: boolean,
  searchText?: string
) => void;

export function getElem(obj: { [key: string]: any }, key: string) {
  return key.split('.').reduce((curObj, curKey) => curObj?.[curKey], obj);
}

function getCellText(column: ColumnDef, row: any): string {
  return column.computedValue
    ? column.computedValue(row)
    : column.type === 'date'
    ? dayjs(String(getElem(row, column.key))).format('MM-DD-YY')
    : column.type === 'datetime'
    ? dayjs(String(getElem(row, column.key))).format('MM-DD-YY hh:mm A')
    : column.type === 'time'
    ? dayjs(String(getElem(row, column.key))).format('HH:mm')
    : String(getElem(row, column.key));
}

// const rowsPerPage = 30;

function Table(props: {
  data: any[];
  columns: ColumnDef[];
  searchText: string;
  sortable?: boolean;
  pageable?: boolean;
  searchOnColumn?: string;
  strictColumns?: boolean;
  scrollable?: boolean;
  highlightRow?: { keyName: string; value: any };
  highlightColor?: string;
  totalRows?: number;
  pageNo?: number;
  rowsPerPage?: number;
  fetchRowsCallback?: FetchRowsCallback;
  sortColumn?: string;
  isAscending?: boolean;
  uniqueData?: string;
  separateLine?: boolean;
  backgroundColor?: boolean;
  dataRenderLimitMdScreen: number;
  dataRenderLimitLgScreen: number;
  tableHeightClassLg: string;
  tableHeightClassMd: string;
  paddingClass?: string;
  setTableCount?: any;
}) {
  const [rowsPerPage, setRowsPerPage] = useState(100000);
  const [data, setData] = useState(props?.data ?? []);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [isAscending, setIsAscending] = useState(true);
  const [pageNo, setPageNo] = useState(1);
  const [sortSecColumn, setSortSecColumn] = useState(() => {
    const savedSortColumn = localStorage.getItem('sortColumn');
    return savedSortColumn || ''; // Default to empty string if not found
  });
  const scrollRef: any = useRef(null);

  const [isSecAscending, setIsSecAscending] = useState(() => {
    const savedSortOrder = localStorage.getItem('sortOrder');
    return savedSortOrder ? JSON.parse(savedSortOrder) : true; // Default to ascending if not found
  });
  const params = useParams();

  const onArrowClick = (columnKey: string, order: boolean) => {
    if (props.fetchRowsCallback) {
      props.fetchRowsCallback(
        props.pageNo ?? pageNo,
        props.rowsPerPage ?? rowsPerPage,
        columnKey,
        order,
        props.searchText
      );
    } else {
      setSortColumn(columnKey);
      setIsAscending(order);
      // Save sorting preferences to localStorage
      if (params['*']?.includes('Location/live/sections')) {
        localStorage.setItem(
          'sortingPreferences',
          JSON.stringify({ sortColumn: columnKey, isAscending: order })
        );
      }
    }
  };

  useEffect(() => {
    // Load sorting preferences from localStorage
    if (params['*']?.includes('Location/live/sections')) {
      const sortingPreferences = localStorage.getItem('sortingPreferences');
      if (sortingPreferences) {
        const { sortColumn, isAscending } = JSON.parse(sortingPreferences);
        setSortColumn(sortColumn);
        setIsAscending(isAscending);
      }
    }
  }, [params]);

  useEffect(() => {
    localStorage.setItem('sortColumn', sortSecColumn);
    localStorage.setItem('sortOrder', JSON.stringify(isSecAscending));
  }, [sortSecColumn, isSecAscending]);

  useEffect(() => {
    setPageNo(1);
  }, [rowsPerPage]);

  function getElem(obj: { [key: string]: any }, key: string) {
    return key.split('.').reduce((curObj, curKey) => curObj?.[curKey], obj);
  }

  function getLastPartOfString(str: string): string {
    const parts = str.trim().split(/\s+/); // Split by whitespace and trim
    return parts.pop() || str;
  }

  function getCellText(column: ColumnDef, row: any): string {
    const value = column.computedValue
      ? column.computedValue(row)
      : column.type === 'date'
      ? dayjs(String(getElem(row, column.key))).format('MM-DD-YY')
      : column.type === 'datetime'
      ? dayjs(String(getElem(row, column.key))).format('MM-DD-YY hh:mm A')
      : column.type === 'time'
      ? dayjs(String(getElem(row, column.key))).format('HH:mm')
      : String(getElem(row, column.key));

    return value;
  }

  const TIME_FORMAT = 'hh:mma';
  const url = getPageNamesFromUrl(params['*']);

  useEffect(() => {
    let lastScrollDepth = 0;

    const handleTableScroll = () => {
      const container = scrollRef.current;
      if (!container) return;

      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      const scrollPercent = (scrollTop / (scrollHeight - clientHeight)) * 100;

      const scrollDepths = [25, 50, 75, 100];

      for (let depth of scrollDepths) {
        if (
          scrollPercent >= depth &&
          lastScrollDepth < depth &&
          url !== 'Mines' &&
          url !== 'unknownpage'
        ) {
          mixpanel.track('Page Scroll', {
            Page_Name: url,
            Scroll_Depth: `${depth}%`,
          });
        } else if (
          scrollPercent < depth &&
          lastScrollDepth >= depth &&
          url !== 'Mines' &&
          url !== 'unknownpage'
        ) {
          mixpanel.track('Page Scroll', {
            Page_Name: url,
            Scroll_Depth: `${depth}%`,
          });
        }
      }

      lastScrollDepth = scrollPercent;
    };

    const container = scrollRef.current;
    if (container) {
      container.addEventListener('scroll', handleTableScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleTableScroll);
      }
    };
  }, []);

  useEffect(() => {
    let newData = [...(props.data ?? [])];

    if (props.searchText !== '') {
      newData = newData.filter((row) => {
        return props.columns.some((column) => {
          if (column.key == props.searchOnColumn) {
            return (
              getCellText(column, row).search(
                new RegExp(escapeRegExp(props.searchText), 'i')
              ) >= 0
            );
          } else {
            return (
              getCellText(column, row).search(
                new RegExp(escapeRegExp(props.searchText), 'i')
              ) >= 0
            );
          }
        });
      });
    }

    if (sortColumn !== null) {
      const columnDef = props.columns.find(
        (columnDef) => columnDef.key === sortColumn
      );
      const ascMult = isAscending ? 1 : -1;

      newData.sort((obj1: any, obj2: any) => {
        const elem1 =
          columnDef && columnDef.computedValue
            ? columnDef.computedValue(obj1)
            : getElem(obj1, sortColumn);
        const elem2 =
          columnDef && columnDef.computedValue
            ? columnDef.computedValue(obj2)
            : getElem(obj2, sortColumn);

        if (elem1 === '-' || elem1 === null || elem1 === undefined) {
          return elem2 === '-' || elem2 === null || elem2 === undefined
            ? 0
            : isAscending
            ? -1
            : 1;
        }

        if (elem2 === '-' || elem2 === null || elem2 === undefined) {
          return isAscending ? 1 : -1;
        }

        if (columnDef?.type === 'text') {
          const lastPart1 = getLastPartOfString(elem1).toLowerCase();
          const lastPart2 = getLastPartOfString(elem2).toLowerCase();
          if (lastPart1 === lastPart2) return 0;
          return (lastPart1 < lastPart2 ? -1 : 1) * ascMult;
        }

        if (
          columnDef?.type === 'time' &&
          typeof elem1 === 'string' &&
          typeof elem2 === 'string'
        ) {
          const time1 = dayjs(elem1, TIME_FORMAT).valueOf();
          const time2 = dayjs(elem2, TIME_FORMAT).valueOf();

          return (time1 - time2) * ascMult;
        }

        if (elem1 === elem2) return 0;
        return (elem1 < elem2 ? -1 : 1) * ascMult;
      });
    }

    setData(newData);
  }, [
    isAscending,
    Boolean(props.strictColumns) && props?.columns,
    props.data,
    props.searchText,
    sortColumn,
  ]);

  const setPageNoCallback = useCallback(
    (pageNo: number) => {
      if (props.fetchRowsCallback) {
        props.fetchRowsCallback(
          pageNo,
          props.rowsPerPage ?? rowsPerPage,
          props.sortColumn ?? sortColumn,
          props.isAscending ?? isAscending,
          props.searchText
        );
      } else {
        setPageNo(pageNo);
      }
    },
    [
      props.fetchRowsCallback,
      props.rowsPerPage ?? rowsPerPage,
      props.sortColumn ?? sortColumn,
      props.isAscending ?? isAscending,
      props.searchText,
    ]
  );

  const setRowsPerPageCallback = useCallback(
    (rowsPerPage: number) => {
      if (props.fetchRowsCallback) {
        props.fetchRowsCallback(
          1,
          rowsPerPage,
          props.sortColumn ?? sortColumn,
          props.isAscending ?? isAscending,
          props.searchText
        );
      } else {
        setRowsPerPage(rowsPerPage);
      }
    },
    [
      props.fetchRowsCallback,
      props.sortColumn ?? sortColumn,
      props.isAscending ?? isAscending,
      props.searchText,
    ]
  );

  const pagedData =
    props.pageable !== false && !props.totalRows
      ? data.slice((pageNo - 1) * rowsPerPage, pageNo * rowsPerPage)
      : data;
  const numPages = Math.ceil(
    (props.totalRows ?? data?.length) / (props.rowsPerPage ?? rowsPerPage)
  );

  const tableRef: React.RefObject<HTMLTableElement> = React.createRef();

  useEffect(() => {
    const table: any = tableRef.current;

    const handleOverflow = (cell: any) => {
      if (cell.offsetWidth < cell.scrollWidth) {
        cell.setAttribute('title', cell.innerText);
      } else {
        cell.removeAttribute('title');
      }
    };

    if (table) {
      Array.from(table?.querySelectorAll('td'))?.forEach((cell: any) => {
        handleOverflow(cell);
      });
    }
    if (props?.setTableCount) {
      props?.setTableCount(data?.length);
    }
  }, [data]);

  return (
    <div>
      <div>
        <div className="inline-block w-full">
          <div
            className={`rounded  ${
              props?.backgroundColor ? 'bg-[#25354354]' : ''
            }`}
          >
            <div className={`${props?.paddingClass}`}>
              <table className={`min-w-full`} ref={tableRef}>
                <thead
                  className={`sticky top-0 bg-[#21303C] z-10 ${
                    props?.scrollable ? 'table table-fixed w-full' : ''
                  }`}
                >
                  <tr
                    className={`${
                      props?.scrollable
                        ? 'table table-fixed w-full px-3'
                        : 'px-3'
                    }`}
                  >
                    {props.columns.map((column, index) => (
                      <th
                        onClick={() => {
                          if (
                            props.sortable !== false &&
                            column.label &&
                            column.type !== 'element' &&
                            column.type !== 'sr_no' &&
                            column.type !== 'action'
                          ) {
                            const isCurrentSortColumn = (props.sortColumn ?? sortColumn) === column.key;
                            const ascending = isCurrentSortColumn ? !(props.isAscending ?? isAscending) : true;
                            onArrowClick(column.key, ascending);
                          }
                        }}
                        key={index}
                        className={
                          'px-4 py-1 text-white text-[14px] font-normal capitalize tracking-wider ' +
                           ( props.sortable !== false &&
                            column.key &&
                            column.type !== 'element' &&
                            column.type !== 'sr_no' &&
                            column.type !== 'action' 
                                ? ' cursor-pointer '
                                : '') +
                          (column.label === 'Production Goal (feet)'
                            ? ' flex justify-center'
                            : '') +
                          (column.label === 'Status' ? 'w-[90px] ' : '') +
                          (column.label === 'Name' ? 'w-[275px] ' : '') +
                          (column.label === 'Category' ? 'w-[150px] ' : '') +
                          (column.label === 'Version' ? 'w-[90px] ' : '') +
                          (column.label === 'Document Name' ? 'w-[400px] ' : '') +
                          (column.label === 'Completed By' ? 'w-[160px] ' : '') +
                          (column.type == 'sr_no' ? 'w-[37px] ' : '') +
                          (params['*']?.includes('AuditLogs') &&
                             column.label == 'Date'
                              ? 'w-full xl xl:w-[137px] 2xl:w-auto'
                              : '') +
                          (params['*']?.includes('AuditLogs') &&
                             column.label == 'Time'
                              ? 'w-full xl xl:w-[137px] 2xl:w-auto'
                              : '')
                        }
                      >

                        {/* <div className="flex flex-row items-center justify-center">
                  <SelectorIcon className="h-4"></SelectorIcon> */}
                        <div
                          className={
                            'flex items-center' +
                            (column.type === 'number'
                              ? ' justify-start'
                              : column.type === 'date'
                              ? ' min-w-6 justify start '
                              : ' ') +
                            (column.type === 'element' ? ' justify-center' : '')
                          }
                          style={{ minHeight: '2.5rem' }}
                        >
                          <span
                            className={'text-left text-[14px] '}
                            dangerouslySetInnerHTML={{ __html: column.label }}
                          ></span>
                          {props.sortable !== false &&
                            column.key &&
                            column.label &&
                            column.type !== 'element' &&
                            column.type !== 'sr_no' &&
                            column.type !== 'action' && 
                              // <div className="flex flex-col ml-2">
                              //   <button
                              //     onClick={() => onArrowClick(column.key, true)}
                              //   >
                              //     <TriangleUpIcon
                              //       className={
                              //         column.key ===
                              //           (props.sortColumn ?? sortColumn) &&
                              //         (props.isAscending ?? isAscending)
                              //           ? 'w-2 h-3 text-[#FFD084]/90'
                              //           : 'w-2 h-3 text-[#FFD084]'
                              //       }
                              //     />
                              //   </button>
                              //   <button
                              //     onClick={() =>
                              //       onArrowClick(column.key, false)
                              //     }
                              //   >
                              //     <TriangleDownIcon
                              //       className={
                              //         column.key ===
                              //           (props.sortColumn ?? sortColumn) &&
                              //         !(props.isAscending ?? isAscending)
                              //           ? 'w-2 h-2 text-[#FFD084]/90'
                              //           : 'w-2 h-2 text-[#FFD084]'
                              //       }
                              //     />
                              //   </button>
                              // </div>
                                column.key === (props.sortColumn ?? sortColumn) && (
                                <div className="flex flex-col ml-2">
                                  {(props.isAscending ?? isAscending) ? (
                                    <TriangleUpIcon className="w-3 h-3 text-[#FFB132]" />
                                  ) : (
                                    <TriangleDownIcon className="w-3 h-3 text-[#FFB132]" />
                                  )}
                                </div>
                            )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody
                  // ref={scrollRef}
                  // className={`${
                  //   props?.scrollable
                  //     ? `table-fixed w-full  block overflow-y-auto overflow-x-hidden ${
                  //         window.innerHeight < 650
                  //           ? ` ${
                  //               pagedData.length <
                  //               props?.dataRenderLimitMdScreen
                  //                 ? 'h-fit'
                  //                 : props?.tableHeightClassMd
                  //             } `
                  //           : pagedData.length < props?.dataRenderLimitLgScreen
                  //           ? 'h-fit'
                  //           : props?.tableHeightClassLg
                  //       }`
                  //     : ''
                  // }`}
                >
                  {pagedData?.map((row, indexOfRow) => (
                    <tr
                      key={indexOfRow}
                      className={
                        `h-10  text-white ${
                          props.separateLine
                            ? 'border-b-[0.5px] border-[#4aa8fe40]'
                            : ''
                        }  ${
                          indexOfRow == 0 &&
                          props.separateLine &&
                          ' border-t-[0.5px] border-[#4aa8fe40]'
                        }  ${
                          props?.scrollable ? 'table table-fixed w-full' : ''
                        }`
                        // (props?.highlightColor &&
                        // getElem(row, props?.highlightRow?.keyName ?? '') ===
                        //   props?.highlightRow?.value
                        //   ? ` bg-${props?.highlightColor}`
                        //   : '')
                        // + (indexOfRow % 2 ? " bg-gray-100" : " bg-white")
                      }
                    >
                      {props?.columns?.map((column, index, array) => (
                        <td
                          className={
                            'py-2 pl-4 text-left text-normal text-[14px] ' +
                            (!column.hasOwnProperty('truncate') || column.truncate === true
                              ? 'truncate ... '
                              : '') +
                            (column.type === 'number'
                              ? ''
                              : // : column.type === "date"
                                //    ? "date-width":
                                '') +
                            (index === array.length - 1 ? ' px-2 ' : '') +
                            (column.label === 'Status' ? 'w-[90px] ' : '') +
                            (column.label === 'Name' ? 'w-[275px] ' : '') +
                            (column.label === 'Category' ? 'w-[150px] ' : '') +
                            (column.label === 'Version' ? 'w-[90px] ' : '') +
                            (column.type == 'sr_no' ? 'w-[37px] ' : '') +
                            (column.label === 'Document Name' ? 'w-[400px] ' : '') +
                            (column.label === 'Completed By' ? 'w-[160px] ' : '') +
                            (params['*']?.includes('AuditLogs') &&
                              (column.label == 'Date'
                                ? 'w-full xl xl:w-[137px] 2xl:w-auto'
                                : '')) +
                            (params['*']?.includes('AuditLogs') &&
                              (column.label == 'Time'
                                ? 'w-full xl xl:w-[137px] 2xl:w-auto'
                                : ''))
                          }
                          key={index}
                          title={`${
                            column.type !== 'element'
                              ? getCellText(column, row)
                              : ''
                          }`}
                        >
                          {column.render ? (
                            column.render(row)
                          ) : column.type === 'sr_no' ? (
                            indexOfRow +
                            1 *
                              ((props.pageNo ?? pageNo) - 1) *
                              (props.rowsPerPage ?? rowsPerPage) +
                            1
                          ) : column.type === 'checkbox' ? (
                            <input
                              type="checkbox"
                              checked={Boolean(getElem(row, column.key))}
                              onChange={(event) =>
                                column.onChange && column.onChange(event, row)
                              }
                            />
                          ) : (
                            getCellText(column, row)
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div> 
        </div>
      </div>
    </div>
  );
}

export default Table;
