import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '../../common/Table';
import ChildScreenForLiveAtomsphere from '../commonLiveChildern';
import { useParams } from 'react-router-dom';
import { getATMLiveEnvirnoment } from '../../../../../src/api/atmosphere/atmoshpere';
import Loader from '../../common/Loader';
import decodeJWT from '../../../../../src/utils/jwtdecoder';

const LiveEnvironment = () => {
  const params = useParams();
  const path = params['*'];
  const lastSegment = path?.split('/').pop() || '';
  const decoded = decodeJWT();

  const { data: liveEnvironmentData, isLoading, isFetching} = useQuery({
    queryKey: ['live-environment'],
    queryFn: () => getATMLiveEnvirnoment(decoded?.mineid),
    refetchOnWindowFocus: true,
  });
  const baseColumns: ColumnDef[] = [
    { key: 'label', label: 'Label', type: 'text',render: (row) => <span className="underline">{row.label}</span>,  },
    { key: 'id', label: 'ID', type: 'text' },
    {
      key: 'lastUpdate',
      label: 'Last Update',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.lastUpdate}</div>
          {/* <div className="text-[12px] text-white/50 float-right">11/6/2025</div> */}
        </div>
      ),
    },
  ];

  const allModuleKeysSet = new Set<string>();
  liveEnvironmentData?.data?.sections?.forEach((section: any) => {
    section.sensors?.forEach((sensor: any) => {
      Object.keys(sensor.moduleData || {}).forEach((key) => {
        allModuleKeysSet.add(key);
      });
    });
  });

  const moduleKeys = Array.from(allModuleKeysSet);

  // const moduleColumns: ColumnDef[] = [
  //   'Absolute Pressure',
  //   'Temperature',
  //   'Relative Humidity',
  // ].map((key) => ({
  //   key: key.replace(/\s+/g, '').toLowerCase(),
  //   label: key,
  //   type: 'text',
  // }));
  const moduleColumns: ColumnDef[] = [
    'AbsPressure',
    'Temperature',
    'Humidity',
  ].map((key) => {
      return {
        key: key.replace(/\s+/g, '').toLowerCase(),
        label: key,
        type: 'text',
      };
  });


  const columns: ColumnDef[] = [
    ...baseColumns.slice(0, 2),
    ...moduleColumns,
    ...baseColumns.slice(2),
  ];

  const dummySections = liveEnvironmentData?.data?.sections
    ?.map((section: any) => ({
      name: section.sectionName.replace('Section ', ''),
      count: section.sensors.length,
    }))
    .filter((ele: any) => ele.count !== 0);

  const dummyData = {
    sectionBreakup: liveEnvironmentData?.data?.sections
      ?.filter((section: any) => section.sensors.length > 0)
      ?.map((section: any) => ({
        sectionName: section.sectionName.replace('Section ', ''),
        totalNodes: section.sensors.length,
        totalNodesBreakup: section.sensors.map((sensor: any) => {
          const moduleDataFormatted: Record<string, string> = {};

          moduleKeys.forEach((key) => {
            const data = sensor.moduleData?.[key];
            const normalizedKey = key.replace(/\s+/g, '').toLowerCase();
            moduleDataFormatted[normalizedKey] = data
              ? `${data.value}${data.unit}\n`
              : '-';
          });

          return {
            label: sensor.label,
            id: sensor.nid,
            status: sensor.status,
            ...moduleDataFormatted,
            lastUpdate: sensor.lastUpdate,
          };
        }),
      })),
  };

  return (
    <>
      {isLoading || isFetching ? (
        <div className="h-[50vh] flex items-center w-full justify-center">
          <Loader />
        </div>
      ) : (
        <>
          <ChildScreenForLiveAtomsphere
            selectedMenu={lastSegment}
            columns={columns}
            dummyData={dummyData}
            dummySections={dummySections}
          />
        </>
      )}
    </>
  );
};
export default LiveEnvironment;
