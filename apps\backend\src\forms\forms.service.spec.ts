import { Test, TestingModule } from '@nestjs/testing';
import { FormsService } from './forms.service';
import { Form } from './entities/forms.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CreateFormDto } from './dto/create-forms.dto';
import { UpdateFormDto } from './dto/update-forms.dto';

describe('FormsService', () => {
  let service: FormsService;

  const mockFormRepository = {
    save: jest.fn(),
    find: jest.fn(),
    findOneBy: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FormsService,
        {
          provide: getRepositoryToken(Form),
          useValue: mockFormRepository,
        },
      ],
    }).compile();

    service = module.get<FormsService>(FormsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('create => Should create a new form and return its data', async () => {
    // arrange
    const req = { user: { userId: 1, mineId: 1 } };
    const createFormDto = {
      mineId: req.user.mineId,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      name: 'Test Form',
      content: '{}',
      createdBy: req.user.userId
    } as CreateFormDto;

    const form = {
      id: 1,
      mineId: req.user.mineId,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      name: 'Test Form',
      content: '{}',
      createdBy: req.user.userId
    } as Form;

    jest.spyOn(mockFormRepository, 'save').mockReturnValue(form);

    // act
    const result = await service.create(createFormDto);

    // assert
    expect(mockFormRepository.save).toHaveBeenCalled();
    expect(mockFormRepository.save).toHaveBeenCalledWith(createFormDto);

    expect(result).toEqual(form);
  });

  it('findAll => should return an array of forms', async () => {
    // arrange
    const req = { user: { userId: 1, mineId: 1 } };
    const form = {
      id: 1,
      mineId: req.user.mineId,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      name: 'Test Form',
      content: '{}',
      createdBy: req.user.userId
    };
    const forms = [form];
    jest.spyOn(mockFormRepository, 'find').mockReturnValue(forms);

    // act
    const result = await service.findAll(req.user.mineId);

    // assert
    expect(mockFormRepository.find).toHaveBeenCalled();
    expect(result).toEqual(forms);
  });

  it('findOne => should find a form by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const req = { user: { userId: 1, mineId: 1 } };
    const form = {
      id: 1,
      mineId: req.user.mineId,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      name: 'Test Form',
      content: '{}',
      createdBy: req.user.userId
    };

    jest.spyOn(mockFormRepository, 'findOneBy').mockReturnValue(form);

    // act
    const result = await service.findOne(id);

    // assert
    expect(mockFormRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(result).toEqual(form);
  });

  it('update => should find a form by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const req = { user: { userId: 1, mineId: 1 } };
    const updateFormDto = {
      name: 'Test Form',
      content: '{}',
      updatedBy: req.user.userId,
    } as UpdateFormDto;
    const form = {
      id: 1,
      mineId: req.user.mineId,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      name: 'Test Form',
      content: '{}',
      updatedBy: req.user.userId
    };

    const updatedForm = Object.assign(form, updateFormDto);
    jest.spyOn(mockFormRepository, 'save').mockReturnValue(updatedForm);

    // act
    const result = await service.update(id, form);

    // assert
    expect(mockFormRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockFormRepository.save).toHaveBeenCalled();
    expect(mockFormRepository.save).toHaveBeenCalledWith(updatedForm);
    expect(result).toEqual(updatedForm);
  });

  it('remove => should find a form by a given id, remove and then return Number of affected rows', async () => {
    // arrange
    const id = 1;
    const req = { user: { userId: 1, mineId: 1 } };
    const form = {
      id: 1,
      mineId: req.user.mineId,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      name: 'Test Form',
      content: '{}',
      updatedBy: req.user.userId
    };

    jest.spyOn(mockFormRepository, 'remove').mockReturnValue(form);

    // act
    const result = await service.remove(id);

    // assert
    expect(mockFormRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockFormRepository.remove).toHaveBeenCalled();
    expect(mockFormRepository.remove).toHaveBeenCalledWith(id);
    expect(result).toEqual(form);
  });
});
