import { CreateUserDto } from './create-user.dto';
import { PartialType } from '@nestjs/mapped-types';
import { IsNumber, IsNotEmpty } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  isActive?: boolean;

  isDelete?: boolean;

  password?: string;
  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  updatedBy: number;
}
