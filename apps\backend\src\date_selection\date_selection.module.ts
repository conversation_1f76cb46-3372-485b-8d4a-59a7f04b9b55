import { Module } from '@nestjs/common';
import { DateSelectionService } from './date_selection.service';
import { DateSelectionController } from './date_selection.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DateSelection } from './entities/date_selection.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DateSelection])],
  controllers: [DateSelectionController],
  providers: [DateSelectionService],
  exports: [DateSelectionService],
})
export class DateSelectionModule {}
