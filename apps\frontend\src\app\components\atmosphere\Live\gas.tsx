import { useParams } from 'react-router-dom';
import ChildScreenForLiveAtomsphere from '../commonLiveChildern';
import { useQuery } from '@tanstack/react-query';
import decodeJWT from '../../../../../src/utils/jwtdecoder';
import { getLiveGasATMdata } from '../../../../../src/api/atmosphere/atmoshpere';
import Loader from '../../common/Loader';
import { ColumnDef } from '../../common/Table';
import dayjs from 'dayjs';

const LiveGas = () => {
  const params = useParams();
  const path = params['*'];
  const lastSegment = path?.split('/').pop() || '';
  const decoded = decodeJWT();

  const { data: liveGasData, isLoading,isFetching } = useQuery({
    queryKey: ['gas-live'],
    queryFn: () => getLiveGasATMdata(decoded?.mineid),
    refetchOnWindowFocus: true,
  });
  const baseColumns: ColumnDef[] = [
    { key: 'label', label: 'Label', type: 'text',render: (row) => <span className="underline">{row.label}</span>, },
    { key: 'id', label: 'ID', type: 'text' },
    { key: 'status', label: 'Status', type: 'text' },
    { key: 'lastUpdate', label: 'Last Update', type: 'text' },
  ];

  const allGasKeysSet = new Set<string>();
  liveGasData?.data?.sections?.forEach((section: any) => {
    section.sensors?.forEach((sensor: any) => {
      Object.keys(sensor.moduleData).forEach((gasKey) => {
        allGasKeysSet.add(gasKey);
      });
    });
  });

  const gasKeys = Array.from(allGasKeysSet).sort();

  const gasColumns: ColumnDef[] = gasKeys.map((key) => ({
    key: key.toLowerCase(),
    label: key.replace('Gas', 'Gas '),
    type: 'text',
  }));

  const columns: ColumnDef[] = [
    ...baseColumns.slice(0, 2),
    ...gasColumns?.map((ele: any) => {
      return {
        ...ele,
        render: (row: any) => {
          return (
            <div
              className="max-w-[140px]"
              title={row[ele.key].replace(':', ' ')}
            >
              <div className="text-[14px] font-medium truncate whitespace-nowrap overflow-hidden text-ellipsis">
                {row[ele.key]?.split(':')[0]}
              </div>
              <div className="text-[12px] text-white/50 truncate whitespace-nowrap overflow-hidden text-ellipsis">
                {row[ele.key]?.split(':')[1]}
              </div>
            </div>
          );
        },
      };
    }),
    ...baseColumns.slice(2),
  ];

  const dummySections = liveGasData?.data?.sections
    ?.map((section: any) => ({
      name: section.sectionName.replace('Section ', ''),
      count: section.sensors.length,
    }))
    .filter((ele: any) => ele.count != 0);

  const dummyData = {
    sectionBreakup: liveGasData?.data?.sections
      ?.filter((section: any) => section.sensors.length > 0)
      ?.map((section: any) => ({
        sectionName: section.sectionName.replace('Section ', ''),
        totalNodes: section.sensors.length,
        totalNodesBreakup: section.sensors.map((sensor: any) => {
          const gasData: Record<string, string> = {};
          gasKeys.forEach((key) => {
            const gas = sensor.moduleData[key];
            gasData[key.toLowerCase()] = gas
              ? `${gas.value}${gas.unit}:${gas.moduleType}`
              : '-';
          });

          return {
            label: sensor.label,
            id: sensor.nid,
            status: sensor.status,
            lastUpdate: sensor.lastUpdate.time,
            ...gasData,
          };
        }),
      })),
  };
  console.log('dummyData', dummyData);
  return (
    <>
      {isLoading || isFetching ? (
        <div className="h-[50vh] flex items-center w-full justify-center">
          <Loader />
        </div>
      ) : (
        <>
          <ChildScreenForLiveAtomsphere
            selectedMenu={lastSegment}
            columns={columns}
            dummyData={dummyData}
            dummySections={dummySections}
          />
        </>
      )}
    </>
  );
};
export default LiveGas;
