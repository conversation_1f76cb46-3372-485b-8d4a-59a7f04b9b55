import { Injectable } from '@nestjs/common';
import { CreateWatchlistItemDto } from './dto/create-watchlist.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { WatchlistItem } from './entities/watchlist.entity';
import { Repository } from 'typeorm';
import { Miner } from '../miners/entities/miner.entity';
import { AuditService } from '../audit/audit.service';
import { MinesService } from '../mines/mines.service';

@Injectable()
export class WatchlistsService {
  constructor(
    private auditService: AuditService,
    private mineService: MinesService,
    @InjectRepository(WatchlistItem)
    private watchlistItemRepository: Repository<WatchlistItem>
  ) {}
  async createWatchlistItem(
    createWatchlistItem: CreateWatchlistItemDto,
    user: any
  ) {
    createWatchlistItem.createdBy = createWatchlistItem.userId = user.userId;
    const existingWatchlist = await this.watchlistItemRepository
      .createQueryBuilder('WatchlistItem')
      .select(['COUNT(1) AS IsPresent'])
      .where('user_id=:userId AND miner_id = :minerId', {
        userId: createWatchlistItem.userId,
        minerId: createWatchlistItem.minerId,
      })
      .getRawMany();
    let watchlist;
    if (existingWatchlist[0].IsPresent == 0) {
      const newWatchListItem = await this.watchlistItemRepository.create(
        createWatchlistItem
      );
      watchlist = await this.watchlistItemRepository.save(newWatchListItem);

      this.auidtLog(
        user,
        'Miner added to watchlist',
        user.firstname + ' ' + user.lastname,
        user.role
      );
    }
    return watchlist;
  }
  async deleteWatchlistItem(id: number, user: any) {
    this.auidtLog(
      user,
      'Miner removed from watchlist',
      user.firstname + ' ' + user.lastname,
      user.role
    );
    return await this.watchlistItemRepository.delete(id);
  }

  async deleteWatchlistItems(ids: String[], user: any) {
    ids.map((id: string) => {
      this.watchlistItemRepository.delete(id);
    });
    this.auidtLog(
      user,
      'One or more miner removed from watchlist',
      user.firstname + ' ' + user.lastname,
      user.role
    );

    return 'Selected delete successfully';
  }

  async clearWatchlist(user: any) {
    const deleteWatchlist = await this.watchlistItemRepository
      .createQueryBuilder()
      .delete()
      .from(WatchlistItem)
      .where('user_id = :userId', { userId: user.userId })
      .execute();

    this.auidtLog(
      user,
      'User cleared watchlist',
      user.firstname + ' ' + user.lastname,
      user.role
    );
    return deleteWatchlist;
  }

  async getUserWatchlist(user: any) {
    const watchlistData = await this.watchlistItemRepository
      .createQueryBuilder('WatchlistItem')
      .select([
        'WatchlistItem.id',
        'miner.id AS MinerId',
        'miner.personal_name AS MinerName',
      ])
      .innerJoin('miners', 'miner', 'WatchlistItem.minerId = miner.id')
      .where('WatchlistItem.user_id = :userId', { userId: user.userId })
      .getRawMany();

    return watchlistData;
  }

  async auidtLog(
    user: any,
    action: string,
    forUser: string,
    forUserRole: string
  ) {
    const mine = await this.mineService.findMineById(user.mineid);
    this.auditService.createAuditLog(
      user,
      mine.name,
      action,
      forUser,
      forUserRole
    );
  }
}
