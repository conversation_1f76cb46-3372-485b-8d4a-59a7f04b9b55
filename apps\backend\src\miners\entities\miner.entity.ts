import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Mine } from '../../mines/entities/mine.entity';
import { WatchlistItem } from '../../watchlists/entities/watchlist.entity';

@Entity('miners')
export class Miner {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'personnel_id' })
  personnelId: string;

  @Column({ name: 'first_name' })
  firstName: string;

  @Column({ name: 'last_name' })
  lastName: string;

  @Column({ name: 'mine_id', type: 'int' })
  mineId: number;

  @ManyToOne(() => Mine, (mine) => mine.id)
  @JoinColumn({ name: 'mine_id' })
  mine: Mine;

  @Column({ name: 'node_id', type: 'int' })
  nodeId: number;

  @Column({ name: 'occupation_id', type: 'int' })
  occupationId: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @OneToOne(() => WatchlistItem, (watchlistItem) => watchlistItem.miner)
  watchlistItem: WatchlistItem;
}
