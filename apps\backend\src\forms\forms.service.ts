import { Injectable, NotFoundException } from '@nestjs/common';
import { IsNull, Repository } from 'typeorm';
import { Form } from './entities/forms.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateFormDto } from './dto/create-forms.dto';
import { UpdateFormDto } from './dto/update-forms.dto';
import moment from 'moment';

@Injectable()
export class FormsService {
  constructor(
    @InjectRepository(Form)
    private formRepository: Repository<Form>
  ) {}

  async create(createFormDto: CreateFormDto, user?: any) {
    let newForm: Form;
    createFormDto = {
      ...createFormDto,
      mineId: user.mineid,
      createdBy: user.userId
    };
    newForm = this.formRepository.create(createFormDto);
    const savedForm = await this.formRepository.save(newForm);
    await this.updateSubmissionContent(savedForm.id);
    return savedForm;
  }

  async findAll(mineId: number) {
    return await this.formRepository.find({
      where: {
        mineId: mineId,
        isDelete: false
      },
    });
  }

  async findInProgressForms(mineId: number) {
    const formData = await this.formRepository.createQueryBuilder('f')
      .select([
        'f.id AS formId',
        'f.name AS document',
        'ft.name AS name',
        'ft.formCategoryId AS formCategoryId',
        'f.is_submitted AS isSubmitted',
        'u1.username AS createdByUser',
        'u2.username AS updatedByUser',
        'f.createdAt AS createdAt',
        'f.updatedAt AS updatedAt',
        'fc.name AS category',
        'f.submission AS submission',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
      ])
      .innerJoin('form_templates', 'ft', 'ft.id = f.formTemplateId')
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.id = f.formTemplateDefinitionId')
      .leftJoin('users', 'u1', 'u1.id = f.createdBy')
      .leftJoin('users', 'u2', 'u2.id = f.updatedBy')
      .where('f.mineId = :mineId', { mineId: mineId })
      .andWhere('f.is_submitted = :isSubmitted', {isSubmitted: false})
      .andWhere('f.is_delete = :isDelete', { isDelete: false })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .addOrderBy('f.updated_at', 'DESC')
      .getRawMany();

      for(let i = 0; i < formData.length; i++) {
        let form = formData[i];
        let formSubmission = JSON.parse(form.submission);
        for(let key in formSubmission) {
          if(key.split('form_').length > 1) {
            let newKey = key.split('form_')[1];
            form[newKey] = formSubmission[key];
            if(formSubmission[key].hasOwnProperty('answer')) {
              form[newKey] = formSubmission[key].answer;
            }
          }
        }
        delete form.submission;
      }

    return formData;
  }

  async findInProgressFormsByUser(userId: number) {
    const formData = await this.formRepository.createQueryBuilder('f')
      .select([
        'f.id AS formId',
        'f.name AS document',
        'ft.name AS name',
        'ft.formCategoryId AS formCategoryId',
        'f.is_submitted AS isSubmitted',
        'u1.username AS createdByUser',
        'u2.username AS updatedByUser',
        'f.createdAt AS createdAt',
        'f.updatedAt AS updatedAt',
        'fc.name AS category',
        'f.submission AS submission',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
      ])
      .innerJoin('form_templates', 'ft', 'ft.id = f.formTemplateId')
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.id = f.formTemplateDefinitionId')
      .leftJoin('users', 'u1', 'u1.id = f.createdBy')
      .leftJoin('users', 'u2', 'u2.id = f.updatedBy')
      .where('f.createdBy = :userId', { userId: userId })
      .andWhere('f.is_submitted = :isSubmitted', {isSubmitted: false})
      .andWhere('f.is_delete = :isDelete', { isDelete: false })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .addOrderBy('f.updated_at', 'DESC')
      .getRawMany();

      for(let i = 0; i < formData.length; i++) {
        let form = formData[i];
        let formSubmission = JSON.parse(form.submission);
        for(let key in formSubmission) {
          if(key.split('form_').length > 1) {
            let newKey = key.split('form_')[1];
            form[newKey] = formSubmission[key];
            if(formSubmission[key].hasOwnProperty('answer')) {
              form[newKey] = formSubmission[key].answer;
            }
          }
        }
        delete form.submission;
      }

    return formData;
  }

  async findSubmittedForms(mineId: number, date?: string) {
    let startDate = new Date(date ?? 0);
    let endDate = date ? new Date(startDate) : new Date();
    startDate.setHours(0,0,0,0);
    endDate.setHours(0,0,0,0);
    endDate.setDate(endDate.getDate() + 1);
    console.log(startDate.toISOString());
    console.log(endDate.toISOString());
    
    const formData = await this.formRepository.createQueryBuilder('f')
      .select([
        'f.id AS formId',
        'f.formTemplateId AS formTemplateId',
        'f.name AS document',
        'ft.name AS name',
        'ft.formCategoryId AS formCategoryId',
        'f.is_submitted AS isSubmitted',
        'u1.username AS createdByUser',
        'u2.username AS updatedByUser',
        'u3.username AS submittedByUser',
        'f.createdAt AS createdAt',
        'f.updatedAt AS updatedAt',
        'f.submittedAt AS submittedAt',
        'f.submission AS submission',
        'fc.name AS category',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
      ])
      .innerJoin('form_templates', 'ft', 'ft.id = f.formTemplateId')
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.id = f.formTemplateDefinitionId')
      .leftJoin('users', 'u1', 'u1.id = f.createdBy')
      .leftJoin('users', 'u2', 'u2.id = f.updatedBy')
      .leftJoin('users', 'u3', 'u3.id = f.submittedBy')
      .where('f.mineId = :mineId', { mineId: mineId })
      .andWhere('f.is_submitted = :isSubmitted', {isSubmitted: true})
      .andWhere('f.is_delete = :isDelete', { isDelete: false })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere(`f.submittedAt BETWEEN '${startDate.toISOString()}' AND '${endDate.toISOString()}'`)
      .addOrderBy('f.updated_at', 'DESC')
      .getRawMany();

    for(let i = 0; i < formData.length; i++) {
      let form = formData[i];
      let formSubmission = JSON.parse(form.submission);
      for(let key in formSubmission) {
        if(key.split('form_').length > 1) {
          let newKey = key.split('form_')[1];
          form[newKey] = formSubmission[key];
          if(formSubmission[key].hasOwnProperty('answer')) {
            form[newKey] = formSubmission[key].answer;
          }
        }
      }
      delete form.submission;
    }
    return formData;
  }

  async findSubmittedFormsByUser(userId: number, date?: string) {
    let startDate = new Date(date ?? 0);
    let endDate = date ? new Date(startDate) : new Date();
    startDate.setHours(0,0,0,0);
    endDate.setHours(0,0,0,0);
    endDate.setDate(endDate.getDate() + 1);
    console.log(startDate.toISOString());
    console.log(endDate.toISOString());

    const formData = await this.formRepository.createQueryBuilder('f')
      .select([
        'f.id AS formId',
        'f.formTemplateId AS formTemplateId',
        'f.name AS document',
        'ft.name AS name',
        'ft.formCategoryId AS formCategoryId',
        'f.is_submitted AS isSubmitted',
        'u1.username AS createdByUser',
        'u2.username AS updatedByUser',
        'u3.username AS submittedByUser',
        'f.createdAt AS createdAt',
        'f.updatedAt AS updatedAt',
        'f.submittedAt AS submittedAt',
        'f.submission AS submission',
        'fc.name AS category',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
      ])
      .innerJoin('form_templates', 'ft', 'ft.id = f.formTemplateId')
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.id = f.formTemplateDefinitionId')
      .leftJoin('users', 'u1', 'u1.id = f.createdBy')
      .leftJoin('users', 'u2', 'u2.id = f.updatedBy')
      .leftJoin('users', 'u3', 'u3.id = f.submittedBy')
      .where('f.createdBy = :userId', { userId: userId })
      .andWhere('f.is_submitted = :isSubmitted', {isSubmitted: true})
      .andWhere('f.is_delete = :isDelete', { isDelete: false })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere(`f.submittedAt BETWEEN '${startDate.toISOString()}' AND '${endDate.toISOString()}'`)
      .addOrderBy('f.updated_at', 'DESC')
      .getRawMany();

    for(let i = 0; i < formData.length; i++) {
      let form = formData[i];
      let formSubmission = JSON.parse(form.submission);
      for(let key in formSubmission) {
        if(key.split('form_').length > 1) {
          let newKey = key.split('form_')[1];
          form[newKey] = formSubmission[key];
          if(formSubmission[key].hasOwnProperty('answer')) {
            form[newKey] = formSubmission[key].answer;
          }
        }
      }
      delete form.submission;
    }
    return formData;
  }

  async findByName(mineId: number, name: string) {
    const formData = await this.formRepository.findOneBy({
      mineId: mineId,
      name: name,
      isDelete: false
    });

    return formData;
  }

  async findOne(id: number) {
    return await this.formRepository.findOne({
      where: { id },
      relations: {
        formTemplate: true
      }
    });
  }

  async update(id: number, updateFormDto: UpdateFormDto, user?: any) {
    const formToUpdate = await this.formRepository.findOneBy({ id });
    
    if (!formToUpdate) {
      throw new NotFoundException('Form not Found!');
    }
    
    formToUpdate.updatedBy = user.userId;
    const updatedForm = Object.assign(formToUpdate, updateFormDto);

    const savedUpdatedForm = await this.formRepository.save(updatedForm);
    await this.updateSubmissionContent(id);
    return savedUpdatedForm;
  }

  async submit(id: number, user:any) {
    const form = await this.formRepository.findOneBy({ id });
    
    if (!form) {
      throw new NotFoundException('Form not Found!');
    }
    form.isSubmitted = true;
    form.submittedBy = user.userId;
    form.submittedAt = new Date();
    return this.formRepository.save(form);
  }

  async revoke(id: number, user:any) {
    const form = await this.formRepository.findOneBy({ id });
    
    if (!form) {
      throw new NotFoundException('Form not Found!');
    }
    form.isSubmitted = false;
    form.submittedBy = null;
    form.submittedAt = null;
    return this.formRepository.save(form);
  }

  async processForm(id: number, user?: any) {
    let form = await this.findOne(id);
    if (!form) {
      throw new NotFoundException(`form with given id ${id} is not found`);
    } else {
      if (form.isProcessed === true) {
        form.isProcessed = false;
      } else {
        form.isProcessed = true;
      }
      form.updatedBy = user.userId;
      return this.formRepository.save(form);
    }
  }

  async deleteForm(id: number, user?: any) {
    let form = await this.findOne(id);
    if (!form) {
      throw new NotFoundException(`form with given id ${id} is not found`);
    } else {
      if (form.isDelete === true) {
        form.isDelete = false;
      } else {
        form.isDelete = true;
      }
      form.updatedBy = user.userId;
      return this.formRepository.save(form);
    }
  }

  async updateSubmissionContent(id: number) {
    const formToUpdate = await this.formRepository.findOneBy({ id });
    
    if (!formToUpdate) {
      throw new NotFoundException('Form not Found!');
    }

    try {
      const contentData = JSON.parse(formToUpdate.content);
      const content = contentData.content;
      const groups = content.groups;
      const responses = {};

      for(let g = 0; g < groups.length; g++) {
        let group = groups[g];
        let groupItems = group.items;
        let groupQuestions = group.questions;

        for(let i = 0; i < groupItems.length; i++) {
          let item = groupItems[i];

          let key = item.key ?? item.properties.key ?? item.id ?? 'no-key';
          let answer = item?.answer ?? '';

          if(groupQuestions.length > 0) {
            for(let q = 0; q < groupQuestions.length; q++) {
              key = item.key ?? item.properties.key ?? item.id ?? 'no-key';
              let quest = groupQuestions[q];
              if(key == 'no-key') {
                key = quest.id ?? `no-key-${q}`;
              }
              else {
                key = `${key}-${quest.name ?? quest.id ?? ('no-key-'+q)}`
              }
              answer = item.answers && item.answers[quest.id] ? item.answers[quest.id] : '';
              responses[key] = answer;
            }
          }
          else {
            responses[key] = answer;
          }
        }
      }
      
      formToUpdate.submission = JSON.stringify(responses);
      formToUpdate.isProcessed = false;
      return await this.formRepository.save(formToUpdate);
    }
    catch(err) {
      console.log('There was an error generating form responses.');
      console.log(err);
    }
    return -1;
  }

  async getFormsToProcess(mineId: number) {
    const formData = await this.formRepository.createQueryBuilder('f')
      .select([
        'f.id AS id',
        'm.code AS mshaId',
        'f.createdAt AS createdAt',
        'f.updatedAt AS updatedAt',
        'u.username AS submittedByUser',
        'f.submittedAt AS submittedAt',
        'f.submission AS submission',
      ])
      .innerJoin('mines', 'm', 'f.mineId = m.id')
      .leftJoin('users', 'u', 'u.id = f.submittedBy')
      .where('f.mineId = :mineId', { mineId: mineId })
      .andWhere('f.is_processed = :isProcessed', {isProcessed: false})
      .andWhere('f.is_delete = :isDelete', { isDelete: false })
      .addOrderBy('f.updated_at', 'DESC')
      .getRawMany();

    try {
      for(let i = 0; i < formData.length; i++) {
        let form = formData[i];
        const submissionData = JSON.parse(form.submission);
        const keys = Object.keys(submissionData);
        let cutsArr = [];
        let downEventsArr = [];

        for (let i = 0; i < keys.length; i++) {
          let key = keys[i];
          let cutsSubStrArr = key.split('cut_');
          let eventSubStrArr = key.split('down_');

          if (cutsSubStrArr.length > 1 && cutsSubStrArr[1].split('-').length > 1) {
            let cutCount = parseInt(cutsSubStrArr[1].split('-')[1]);
            let cutProp = cutsSubStrArr[1].split('-')[0];
            while (cutsArr.length < cutCount) {
              cutsArr.push({});
            }

            cutsArr[cutCount - 1]['id'] = cutCount;
            cutsArr[cutCount - 1][cutProp] = submissionData[key];
          }

          if (eventSubStrArr.length > 1 && eventSubStrArr[1].split('-').length > 1) {
            let eventCount = parseInt(eventSubStrArr[1].split('-')[1]);
            let eventProp = eventSubStrArr[1].split('-')[0];
            while (downEventsArr.length < eventCount) {
              downEventsArr.push({});
            }

            downEventsArr[eventCount - 1]['id'] = eventCount;
            downEventsArr[eventCount - 1][eventProp] = submissionData[key];
          }
        }

        for(let i = 0; i < cutsArr.length; i++) {
          let cut = cutsArr[i];
          let validCut = true;

          if(cut["end_depth"] && cut["end_depth"].trim().length > 0 && !isNaN(cut["end_depth"])) {
            if(!(cut["start_depth"] && cut["start_depth"].trim().length > 0 && !isNaN(cut["start_depth"]))) {
              cut["start_depth"] = "0";
            }
            if(!(cut["total_feet"] && cut["total_feet"].trim().length > 0 && !isNaN(cut["total_feet"]))) {
              cut["total_feet"] = (parseFloat(cut["end_depth"]) - parseFloat(cut["start_depth"])).toString();
            }
          }
          if(!(cut["total_feet"] && cut["total_feet"].trim().length > 0 && !isNaN(cut["total_feet"]))) {
            validCut = false;
          }
          if(!(cut["start_time"] && cut["start_time"].trim().length > 0)) {
            validCut = false;
          }
          if(!(cut["end_time"] && cut["end_time"].trim().length > 0)) {
            validCut = false;
          }
          cut["status"] = validCut ? "complete" : "incomplete";
        }

        let totalFeetMined = 0;
        let totalCuts = 0;
        for(let i = 0; i < cutsArr.length; i++) {
          let cut = cutsArr[i];
          if(cut["total_feet"] && cut["total_feet"].trim().length > 0 && !isNaN(cut["total_feet"])) {
            totalFeetMined += parseFloat(cut["total_feet"]);
            totalCuts++;
          }
        }
        submissionData['total_cuts'] = totalCuts.toString();
        submissionData['total_feet_mined'] = totalFeetMined.toString();
        submissionData['cuts'] = cutsArr;

        for(let i = 0; i < downEventsArr.length; i++) {
          let event = downEventsArr[i];
          event["duration"] = "";
          event["production_lost_time"] = "";
          event["status"] = "incomplete";
          if(event["start_time"] && event["start_time"].trim().length > 0 &&
            event["end_time"] && event["end_time"].trim().length > 0) {
            let startTime = moment(event["start_time"], 'HH:mm');
            let endTime = moment(event["end_time"], 'HH:mm');
            let diff = endTime.diff(startTime, 'minutes');
            if(diff < 0) {
              diff = diff + (24*60);
            }
            event["duration"] = diff.toString();
            event["production_lost_time"] = diff.toString();
            event["status"] = "complete";
          }
          if(event["status"] == "complete" && !(event["type"] && event["type"].answer && event["type"].answer.trim().length > 0)) {
            event["type"] = {"answer":"Unplanned", "itemId":"4"};
          }
        }

        let totalDownTime = 0;
        let totalEvents = 0;
        let lastDownTimeStarted = "";
        for(let i = 0; i < downEventsArr.length; i++) {
          let event = downEventsArr[i];
          if(event["duration"] && event["duration"].trim().length > 0 && !isNaN(event["duration"])) {
            totalDownTime += parseFloat(event["duration"]);
            totalEvents++;
          }
          if(event["start_time"] && event["start_time"].trim().length > 0) {
            lastDownTimeStarted = event["start_time"];
          }
        }
        submissionData['total_down_time'] = totalDownTime.toString();
        submissionData['last_down_start_time'] = lastDownTimeStarted.toString();
        submissionData['total_down_events'] = totalEvents.toString();
        submissionData['down_events'] = downEventsArr;

        form.submissionData = submissionData;
      }

      return formData;
    }
    catch(err) {
      console.log('There was an error generating report.');
      console.log(err);
    }
    return -1;
  }
}
