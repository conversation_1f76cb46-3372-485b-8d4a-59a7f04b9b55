import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import SendGrid from '@sendgrid/mail';

@Injectable()
export class SendgridService {
  constructor(private readonly configService: ConfigService) {
    // request to SendGrid API.
    SendGrid.setApiKey(this.configService.get<string>('SEND_GRID_KEY'));
  }

  async send(mail: SendGrid.MailDataRequired) {
    console.log(`E-Mail sending to ${mail.to}`);

    const transport = await SendGrid.send(mail);
    console.log(transport);

    console.log(`E-Mail sent to ${mail.to}`);
    return transport;
  }
}
