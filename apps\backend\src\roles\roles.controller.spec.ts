import { Test, TestingModule } from '@nestjs/testing';
import { RolesController } from './roles.controller';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { Role } from './entities/role.entity';
import { UpdateRoleDto } from './dto/update-role.dto';

describe('RolesController', () => {
  let controller: RolesController;

  const mockRoleService = {
    create: jest.fn(),
    findAll: jest.fn(),
    getRolesByCompanyId: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RolesController],
      providers: [
        {
          provide: RolesService,
          useValue: mockRolesService,
      }]
    }).compile();

    controller = module.get<RolesController>(RolesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('create => should create a new role by the given data', async () => {
    // arrange
    const createRoleDto = {
      companyId: 1,
      name: 'test user 1',
    } as CreateRoleDto;

    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1',
    } as Role;

    jest.spyOn(mockRolesService, 'create').mockReturnValue(role);

    // act
    const result = await controller.create(createRoleDto);

    // assert
    expect(mockRolesService.create).toHaveBeenCalled();
    expect(mockRolesService.create).toHaveBeenCalledWith(createRoleDto);
    expect(result).toEqual(role);
  });

  it('findAll => should return an array of roles', async () => {
    // arrange
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1',
    };
    let companyId = 1;
    const req = { user: { companyid: companyId } };
    const roles = [role];
    jest.spyOn(mockRolesService, 'findAll').mockReturnValue(roles);

    // act
    const result = await controller.findAll(req);

    // assert
    expect(mockRolesService.findAll).toHaveBeenCalled();
    expect(result).toEqual(roles);
  });

  it('getRolesByCompanyId => should return an array of roles by a companyId', async () => {
    // arrange
    const companyId = 1;
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1'
    };

    const roles = [role];
    jest.spyOn(mockRolesService, 'getRolesByCompanyId').mockReturnValue(roles);

    // act
    const result = await controller.findRolesByCompanyId(companyId);

    // assert
    expect(mockRolesService.getRolesByCompanyId).toHaveBeenCalled();
    expect(result).toEqual(roles);
  });

  it('findOne => should find a role by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1',
    };

    jest.spyOn(mockRolesService, 'findOne').mockReturnValue(role);

    // act
    const result = await controller.findOne(id);

    // assert
    expect(mockRolesService.findOne).toHaveBeenCalled();
    expect(mockRolesService.findOne).toHaveBeenCalledWith(+id);
    expect(result).toEqual(role);
  });

  it('update => should find a role by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateRoleDto = {
      companyId: 1,
      name: 'test user 1',
    } as UpdateRoleDto;
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 2',
    };

    jest.spyOn(mockRolesService, 'update').mockReturnValue(role);

    // act
    const result = await controller.update(id, updateRoleDto);

    // assert
    expect(mockRolesService.update).toHaveBeenCalled();
    expect(mockRolesService.update).toHaveBeenCalledWith(+id, updateRoleDto);
    expect(result).toEqual(role);
  });

  it('remove => should find a role by a given id, remove and then return number of affected rows', async () => {
    // arrange
    const id = 1;
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1',
    };

    jest.spyOn(mockRolesService, 'remove').mockReturnValue(role);

    // act
    const result = await controller.remove(id);

    // assert
    expect(mockRolesService.remove).toHaveBeenCalled();
    expect(mockRolesService.remove).toHaveBeenCalledWith(+id);
    expect(result).toEqual(role);
  });
});
