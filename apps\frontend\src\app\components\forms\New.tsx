import decodeJWT from '../../../utils/jwtdecoder';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import SubMenuInnerNavBar from '../common/SubMenuInnerNavBar';
import Table, { ColumnDef } from '../common/Table';
import RequiredFieldsForm from './new/RequiredFieldsForm';
import { getFeatures } from '../../../api/users/userapis';
import { getFormCategoriesUsedByFormTemplates } from '../../../api/forms/formcategoryapis';
import { getPublishedForms } from '../../../api/forms/formtemplateapis';
import { getInProgressForms, getInProgressFormsByUser } from '../../../api/forms/formapis';
import Loader from '../common/Loader';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { PlusIcon } from '../../../assets/icons/icons';
import Modal from '../common/Modal';
import { getActiveShifts } from '../../../api/shifts/shiftapis';
import { getSections } from '../../../api/sections/sectionapis';
import { getPageNamesFromUrl } from '../PageName';

const NewForms = () => {
  const location = useLocation();
  const params = useParams();
  const [tableCount, setTableCount] = useState();
  const [categoryFilter, setCategoryFilter] = useState('All');
  const [filteredPublishedForms, setFilteredPublishedForms] = useState([]);
  const [filteredInProgressForms, setFilteredInProgressForms] = useState([]);
  const [openNewFormRequiredFields, setOpenNewFormRequiredFields] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);
  const [defaultDate, setDefaultDate] = useState<string | null>();
  const [defaultShift, setDefaultShift] = useState();
  const userRole = decodeJWT()?.role;

  const {status: publishedStatus, data: publishedForms, isLoading: isLoadingPublished } = useQuery({
    queryKey: ['published-forms'],
    queryFn: () => getPublishedForms(),
    refetchOnWindowFocus: false,
  });
  const { status: inProgressStatus, data: inProgressForms, isLoading: isLoadingInProgress } = useQuery({
    queryKey: ['in-progress-forms'],
    queryFn: () => getInProgressFormsByUser(),//userRole == 'user' ? getInProgressFormsByUser() : getInProgressForms(),
    refetchOnWindowFocus: false,
  });
  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });
  const { data: formCategories } = useQuery({
    queryKey: ['form-categories-templates'],
    queryFn: () => getFormCategoriesUsedByFormTemplates(),
    refetchOnWindowFocus: false,
  });

  const { data: shifts } = useQuery({
    queryKey: ['shifts'],
    queryFn: getActiveShifts,
    refetchOnWindowFocus: false,
  });

  const { data: sections } = useQuery({
    queryKey: ['sections'],
    queryFn: getSections,
    refetchOnWindowFocus: false,
  });

  const navigate = useNavigate();

  const publishedList = useMemo(() => {
    const list = publishedForms?.data ?? [];
    if(list && list.length > 0) {
      const index = list.findIndex((i:any) => i.name == 'Section Production Report');
      if(index > 0) {
        const item = list.splice(index, 1);
        list.unshift(item[0]);
      }
    }
    return list;
  }, [publishedForms?.data]);
  
  useEffect(() => {
    if (publishedList.length > 0) {
      categoryFilter === 'All'
      ? setFilteredPublishedForms(publishedList)
      : setFilteredPublishedForms(publishedList.filter((f: any) => f.category === categoryFilter));
    }
    if (inProgressForms?.data.length > 0) {
      categoryFilter === 'All'
      ? setFilteredInProgressForms(inProgressForms?.data)
      : setFilteredInProgressForms(inProgressForms?.data.filter((f: any) => f.category === categoryFilter));
    }
  }, [categoryFilter, publishedList, inProgressForms?.data]);

  useEffect(() => {
    let now = new Date();
    setDefaultDate(now.toISOString().slice(0, 10));
    if(shifts?.data.length > 0) {
      for(let i = 0; i < shifts?.data.length; i++) {
        let s = shifts?.data[i];
        let startTime = new Date(s.startTime).toTimeString();
        let endTime = new Date(s.endTime).toTimeString();
        let nowTime = now.toTimeString();
        
        if(endTime > startTime && nowTime > startTime && endTime > nowTime) {
          setDefaultShift(s.id);
        }
        if(startTime > endTime && (nowTime > startTime || (nowTime < startTime && nowTime < endTime))) {
          setDefaultShift(s.id);
        }
      }
    }
  }, [shifts?.data]);

  const continueWithDefaults = (defaults: any) => {
    setOpenNewFormRequiredFields(false);
    navigate(
        params?.mineId
        ? `/app/Mines/${params?.mineId}/Forms/New/NewForm/${selectedTemplateId}`
        : `/app/Forms/New/NewForm/${selectedTemplateId}`, {state: defaults}
      );
  }
  
  const publishedColumns: ColumnDef[] = [
    {
      key: 'name',
      label: 'Name',
      type: 'text',
      truncate: false,
      render: (row: any) => (
        <div className="opacity-70 hover:opacity-100 text-ellipsis overflow-hidden w-35 cursor-pointer flex flex-row gap-2 items-center"
          title="Open Form"
          data-tooltip-target="tooltip-default"
            onClick={async () => {
              setSelectedTemplateId(row.formTemplateId);
              setOpenNewFormRequiredFields(true);
              // navigate(
              //   params?.mineId
              //   ? `/app/Mines/${params?.mineId}/Forms/New/NewForm/${row.formTemplateId}`
              //   : `/app/Forms/New/NewForm/${row.formTemplateId}`
              // );
            }}>
					<PlusIcon className="text-white text-[14px] h-6 w-6" />
          <u>{row.name}</u>
        </div>
      ),
    },
    {
      key: 'category',
      label: 'Category',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.category}
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.description}
        </div>
      ),
    }
  ];
  
  const inProgressColumns: ColumnDef[] = [
    {
      key: 'document',
      label: 'Document Name',
      type: 'text',
      truncate: false,
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 cursor-pointer"
          title="Open Form"
          data-tooltip-target="tooltip-default"
            onClick={async () => {
              navigate(
                params?.mineId
                ? `/app/Mines/${params?.mineId}/Forms/New/Form/${row.formId}`
                : `/app/Forms/New/Form/${row.formId}`, {state: {}}
              );
            }}>
          <u>{row.document}</u>
        </div>
      ),
    },
    // {
    //   key: 'name',
    //   label: 'Form Name',
    //   type: 'text',
    //   render: (row: any) => (
    //     <div className="text-ellipsis overflow-hidden w-35">
    //       {row.name}
    //     </div>
    //   ),
    // },
    {
      key: 'category',
      label: 'Category',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.category}
        </div>
      ),
    },
    {
      key: 'shift',
      label: 'Shift',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.shift && row.shift.length > 0 ? row.shift : '-'}
        </div>
      ),
    },
    {
      key: 'section',
      label: 'Section',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.section && row.section.length > 0 ? row.section : '-'}
        </div>
      ),
    },
    {
      key: 'date',
      label: 'Date',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.date && row.date.length > 0 ? row.date : '-'}
        </div>
      )
    },
    // {
    //   key: 'version',
    //   label: 'Version',
    //   type: 'text',
    //   render: (row: any) => (
    //     <div className="text-ellipsis overflow-hidden w-35 ">
    //       {`${row.major}.${row.minor}.${row.revision}`}
    //     </div>
    //   ),
    // },
    {
      key: 'updatedBy',
      label: 'Saved By',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row?.updatedByUser ?? row?.createdByUser ?? '-'}
        </div>
      ),
    },
  ];

  if ((features?.data.some((feature: any) => feature.FeatureName == 'Forms') ||
    decodeJWT()?.role == 'superuser')
  ) {
    const catArr = formCategories?.data.map((c:any) => c?.name);
    let formCategoryList = ['All'];
    if(catArr && catArr.length > 0) {
      formCategoryList = ['All',...catArr];
    }
    
    return (
      <>
        <SubMenuInnerNavBar
          leftTitle="New"
          rightTitle="Forms"
          tabNames={formCategoryList}
          showSearchBox={false}
          pathToRender={
            decodeJWT()?.role == 'superuser'
              ? `Mines/${params?.mineId}/Forms/New`
              : `Forms/New`
          }
          setCategoryFilter={setCategoryFilter}
          defaultTab='All'
        />
        <div className="mt-8 px-10 2xl:px-16">
          <div>
            {openNewFormRequiredFields ? (
              <Modal
                Content={
                  <RequiredFieldsForm
                    selectedTemplateId={selectedTemplateId}
                    setOpenNewFormRequiredFields={setOpenNewFormRequiredFields}
                    continueWithDefaults={continueWithDefaults}
                  />
                }
                size="w-2/4 2xl:w-2/5 xl:w-2/5"
                backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
              ></Modal>
            ) : (
              ''
            )}
          </div>
          <div>
            {isLoadingPublished ? (
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            ) : (
              <div className={`pt-8 userMTableBg px-5 rounded-xl
                ${filteredPublishedForms.length <= 0 ? 'pb-8' : 'pb-2'}`}>
                <div className="flex justify-between">
                  <div className="block">
                    <h6 className="font-semibold text-[20px] text-white">
                      {`Create New ${categoryFilter == 'All' ? '' : categoryFilter} Form`}
                    </h6>
                  </div>
                </div>
                <div className="mt-5">
                  {filteredPublishedForms.length > 0 ? (
                    <div>
                      <Table
                        columns={publishedColumns}
                        data={
                          filteredPublishedForms?.map((ele: any) => ({
                            name:
                              ele?.name,
                            ...ele,
                          })) ?? []
                        }
                        searchText=''
                        sortable={false}
                        searchOnColumn={'name'}
                        separateLine={false}
                        scrollable={true}
                        dataRenderLimitMdScreen={8}
                        dataRenderLimitLgScreen={15}
                        tableHeightClassLg="tableheightForlg"
                        tableHeightClassMd="tableheightFormd"
                        setTableCount={setTableCount}
                      />
                    </div>
                  ) : (
                    <div className="text-[26px] font-bold  text-center text-white">
                      {`There are currently no published ${categoryFilter == 'All' ? '' : categoryFilter} forms`}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="mt-8 px-10 2xl:px-16">
          <div>
            {isLoadingInProgress ? (
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            ) : (
              <div className={`pt-8 userMTableBg px-5 rounded-xl
                ${filteredInProgressForms.length <= 0 ? 'pb-8' : 'pb-2'}`}>
                <div className="flex justify-between">
                  <div className="block">
                    <h6 className="font-semibold text-[20px] text-white">
                      {`In Progress ${categoryFilter == 'All' ? '' : categoryFilter} Forms`}
                    </h6>
                  </div>
                </div>
                <div className="mt-5">
                  {filteredInProgressForms.length > 0 ? (
                    <div>
                      <Table
                        columns={inProgressColumns}
                        data={
                          filteredInProgressForms?.map((ele: any) => ({
                            document:
                              ele?.document,
                            ...ele,
                          })) ?? []
                        }
                        searchText=''
                        sortable={false}
                        searchOnColumn={'document'}
                        separateLine={false}
                        scrollable={true}
                        dataRenderLimitMdScreen={8}
                        dataRenderLimitLgScreen={15}
                        tableHeightClassLg="tableheightForlg"
                        tableHeightClassMd="tableheightFormd"
                        setTableCount={setTableCount}
                      />
                    </div>
                  ) : (
                    <div className="text-[26px] font-bold  text-center text-white">
                      {`There are currently no ${categoryFilter == 'All' ? '' : categoryFilter} forms in progress`}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    );
  } else {
    return '';
  }
};

export default NewForms;
