import { useEffect, useState } from 'react';
import {
  EditIcon,
  TrashIcon,
  EyeIcon,
  SearchIcon,
} from '../../../assets/icons/icons';
import Table, { ColumnDef } from '../common/Table';
import AddMineForm from './AddMineForm';
import Modal from '../common/Modal';
import { useQuery } from '@tanstack/react-query';
import { getMines } from '../../../api/mines/mineapis';
import {
  useDeleteMine,
  useStatusChange,
} from '../../../services/mutations/companymutations';
import { toast } from 'react-toastify';
import decodeJWT from '../../../utils/jwtdecoder';
import { Tooltip } from '@material-tailwind/react';
import { escapeRegExp } from '../../../utils/constant';
import { useChangeTokenSuperUser } from '../../../services/mutations/usermutations';
import { useNavigate, useParams } from 'react-router-dom';
import { getTimezoneData } from '../../../api/mines/mineapis';
import Loader from '../common/Loader';
import { all } from 'axios';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import { UserMenu } from '../common/UserMenu';

export default function Mines() {
  const [openAddMineForm, setOpenAddMineForm] = useState(false);
  const [editData, setEditData] = useState(null);
  const [mineData, setMineData] = useState<any>();
  const [searchPh, setSearchPh] = useState('');
  const [mineCount, setMineCount] = useState(0);
  const [openDeleteModal, setOpenDeteteModal] = useState(false);

  const allMinesQuery = useQuery({
    queryKey: ['companies'],
    queryFn: getMines,
    refetchOnWindowFocus: false,
  });
  const { data: allMines } = allMinesQuery;
  const navigate = useNavigate();
  const changeToken = useChangeTokenSuperUser();
  const deleteMine = useDeleteMine();
  const statusChange = useStatusChange();
  const { data: timezoneData } = useQuery({
    queryKey: ['timezone'],
    queryFn: getTimezoneData,
    refetchOnWindowFocus: false,
  });
  const params = useParams();
  const url: any = getPageNamesFromUrl(params['*']);

  useEffect(() => {
    const handleOverflow = (cell: any) => {
      if (cell.offsetWidth < cell.scrollWidth) {
        cell.setAttribute('title', cell.innerText);
      } else {
        cell.removeAttribute('title');
      }
    };

    const observeDataChanges = () => {
      const tableCells = document?.querySelectorAll('.text-ellipsis');
      if (tableCells) {
        tableCells.forEach((cell) => {
          handleOverflow(cell);
        });
      }
    };

    observeDataChanges(); // Initial observation

    const observer = new MutationObserver(observeDataChanges);
    observer.observe(document.body, { subtree: true, childList: true });

    return () => {
      observer.disconnect();
    };
  }, [allMines?.data]);

  const getTimezoneNameById = (id: any) => {
    return timezoneData?.data?.find((item: any) => item?.id === id)?.name;
  };

  const regex = new RegExp(`(${escapeRegExp(searchPh)})`, 'i');
  const mineNameCaseChange = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1);
  };
  const columns: ColumnDef[] = [
    {
      key: 'name',
      label: 'Mine Name',
      type: 'text',
      render: (row) => (
        <div className="">
          <div
            className="text-ellipsis overflow-hidden w-35"
            dangerouslySetInnerHTML={{
              __html: mineNameCaseChange(row?.name)?.replace(
                regex,
                "<span style='color: #FFD084;'>$1</span>"
              ),
            }}
          ></div>
        </div>
      ),
    },
    {
      key: 'company',
      label: 'Company',
      type: 'text',
      render: (row) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row?.company?.name.charAt(0).toUpperCase() +
            row?.company?.name.slice(1)}
        </div>
      ),
    },
    {
      key: 'location',
      label: 'Location',
      type: 'text',
      render: (row) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row?.location}
        </div>
      ),
    },
    {
      key: 'timezone',
      label: 'Time zone',
      type: 'text',
      render: (row) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row?.timezoneId ? getTimezoneNameById(row?.timezoneId) : '-'}
        </div>
      ),
    },
    {
      key: 'isActive',
      label: 'Status',
      type: 'text',
      render: (row) => (
        <div className="relative w-[32px] h-[16px] rounded-full cursor-pointer">
          <label className="cursor-pointer">
            <input
              type="checkbox"
              tabIndex={Number('-1')}
              checked={row.isActive}
              className="sr-only peer"
              onChange={async (e: any) => {
                if (e.target.checked) {
                  try {
                    const activeStatus = await statusChange.mutateAsync(
                      row?.id
                    );
                    mixpanel.track('Active Mine', {
                      MshaId: activeStatus?.data?.code,
                      Mine_Name: activeStatus?.data?.name,
                    });
                    toast.success('Mine activated successfully');
                  } catch (err: any) {
                    console.error('err', err?.message);
                    mixpanel.track('Error Event', {
                      Page_Name: url,
                      Action_Name: 'Active Mine',
                    });
                  }
                } else {
                  try {
                    const inActiveStatus = await statusChange.mutateAsync(
                      row?.id
                    );
                    mixpanel.track('Inactive Mine', {
                      MshaId: inActiveStatus?.data?.code,
                      Mine_Name: inActiveStatus?.data?.name,
                    });
                    toast.success('Mine deactivated successfully');
                  } catch (err: any) {
                    console.error('err', err?.message);
                    mixpanel.track('Error Event', {
                      Page_Name: url,
                      Action_Name: 'Deactive Mine',
                    });
                  }
                }
              }}
            />
            <div
              title={row.isActive ? 'Deactivate Mine' : 'Activate Mine'}
              className={`w-full h-full bg-gray-200 ${
                row?.isActive
                  ? ' toggleShadowActive '
                  : ' toggleShadowInActive '
              } bg-opacity-25 border-[0.5px] border-gray-200 outline-none peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-[162%] rtl:peer-checked:after:-translate-x-[162%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white  after:rounded-full after:h-[10px] after:w-[10px] after:my-[1px] after:mx-[2px]  after:transition-all  peer-checked:bg-[#96FB60] peer-checked:bg-opacity-25  peer-checked:border-[1px] peer-checked:border-[#96FB60]`}
            ></div>
          </label>
        </div>
      ),
    },
    {
      key: 'action',
      label: 'Actions',
      type: 'element',
      render: (row) => (
        <div className="flex justify-center">
          <div
            title="View Mine"
            className="cursor-pointer"
            data-tooltip-target="tooltip-default"
            onClick={async () => {
              const token = await changeToken.mutateAsync(row?.id);
              if (token?.data?.jwtToken) {
                navigate(`/app/Mines/${row?.id}/Location/dashboard`);
              }
            }}
          >
            <EyeIcon className="text-white font-black h-5 w-5 mx-2 edit-icon" />
          </div>
          <div
            title="Edit Mine"
            className="cursor-pointer"
            data-tooltip-target="tooltip-default"
            onClick={() => {
              setEditData(row);
              setOpenAddMineForm(true);
              document
                ?.getElementsByClassName('scrollToTop')[0]
                ?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                });
            }}
          >
            <EditIcon className="text-white font-black h-5 w-5 mx-2 edit-icon" />
          </div>
          <div
            title="Delete Mine"
            className="cursor-pointer"
            onClick={() => {
              setOpenDeteteModal(true);
              setMineData(row);
            }}
          >
            <TrashIcon className="text-white font-black text-[14px] h-5 w-5 mx-2 delete_icon" />
          </div>
        </div>
      ),
    },
  ];

  if (decodeJWT()?.role !== 'superuser') {
    return '';
  }

  return (
    <div className="w-full">
      <div className="sticky w-full top-0 z-30 box bg-top px-10 2xl:px-16">
        <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe] pb-4 pt-4">
          <div className="">
            <div className="relative  mt-2  w-1/2">
              <span>
                <SearchIcon className="absolute top-0 left-2 h-6 mr-1 my-1.5  text-white cursor-pointer"></SearchIcon>
              </span>
              <input
                id="search-result"
                type="text"
                className={`border   text-white text-sm pl-8 outline-none bg-transparent rounded border-[#4AA8FE] block w-full p-2   autoComOff`}
                placeholder="Search Mine"
                autoFocus
                value={searchPh}
                onChange={(e: any) => setSearchPh(e?.target?.value)}
              />
            </div>
          </div>
          <div className="">
            {/* <h6 className="p-2 font-bold text-white text-[32px] text-right">
              Mine Management
            </h6> */}
            <div className="flex justify-end items-center -mr-10 relative">
              <h6 className=" font-bold text-white text-[32px]">
                Mine Management
              </h6>
              <span className="ml-4">
                <UserMenu />
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-6 px-10 2xl:px-16 ">
        <div>
          {openAddMineForm ? (
            <Modal
              Content={
                <AddMineForm
                  editData={editData}
                  setOpenAddMineForm={setOpenAddMineForm}
                />
              }
              size="w-[554px]"
              backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
              noScroll={editData ? true : false}
            ></Modal>
          ) : (
            ''
          )}
        </div>

        <div>
          {allMinesQuery?.isLoading ? (
            <div>
              <div>
                <div className="flex justify-center items-center h-full pt-[200px] white">
                  {<Loader />}
                </div>
                <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                  Loading....
                </div>
              </div>
            </div>
          ) : (
            <div className="pt-8 pb-2 userMTableBg px-5 rounded-xl">
              <div className="flex justify-between">
                <div className="block">
                  <h6 className="font-semibold text-[20px] text-white">
                    {`Mine Operations (${mineCount})`}
                  </h6>
                  <p className="font-normal my-1 text-[14px] tracking-normal leading-5 text-[#cccccc] ">
                    Manage existing mines, change settings, or deactivate mines
                  </p>
                </div>
                <div>
                  <button
                    id="add_new_mine"
                    title="Click to add mine"
                    className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center  items-center  me-2 mb-2"
                    onClick={() => {
                      setEditData(null);
                      setOpenAddMineForm(true);
                    }}
                  >
                    Add New Mine
                  </button>
                </div>
              </div>
              <div className="mt-5">
                {allMines?.data.length > 0 ? (
                  <Table
                    columns={columns}
                    data={allMines?.data ?? []}
                    searchText={searchPh}
                    sortable={false}
                    searchOnColumn={'name'}
                    separateLine={false}
                    scrollable={true}
                    dataRenderLimitMdScreen={8}
                    dataRenderLimitLgScreen={15}
                    tableHeightClassLg="tableheightForlg"
                    tableHeightClassMd="tableheightFormd"
                    setTableCount={setMineCount}
                  />
                ) : (
                  <h2 className="text-[26px] font-bold italic text-center text-white">
                    There are currently no mines
                  </h2>
                )}
              </div>

              {openDeleteModal ? (
                <Modal
                  size="w-[454px]"
                  backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
                  Content={
                    <div className="p-4">
                      <div className="text-[24px] text-white text-center font-bold">
                        Delete Mine?
                      </div>
                      <div className="my-2 text-[16px] text-white text-center text-provima text-ellipsis overflow-hidden m-auto">
                        {`Are you sure you want to delete the mine '${mineData.name}'?`}
                      </div>
                      <div className="mt-2 text-center">
                        <button
                          title="Click to Cancel"
                          onClick={() => setOpenDeteteModal(!openDeleteModal)}
                          className="w-[177px] my-2 text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 text-[16px] py-2 px-8 rounded focus:outline-none focus:shadow-outline mr-2"
                        >
                          Cancel
                        </button>
                        <button
                          title="Click to delete mine"
                          onClick={async (e: any) => {
                            e.preventDefault();
                            try {
                              const res = await deleteMine.mutateAsync(
                                mineData?.id
                              );
                              if (res?.status == 200 || res?.status == 201) {
                                setOpenDeteteModal(!openDeleteModal);
                                setMineData({});
                                mixpanel.track('Delete Mine', {
                                  MshaId: res?.data?.code,
                                  Mine_Name: res?.data?.name,
                                });
                                toast.success('Mine deleted successfully');
                              }
                            } catch (err: any) {
                              mixpanel.track('Error Event', {
                                Page_Name: url,
                                Action_Name: 'Delete Mine',
                              });
                              toast.error(err.message);
                            }
                          }}
                          className="w-[177px] my-2 text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 text-[16px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  }
                />
              ) : (
                <></>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
