import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  useAddGoal,
  useEditGoal,
  useFindActiveGoal,
} from '../../../services/mutations/goalmutations';
import { getShifts } from '../../../api/shifts/shiftapis';
import { getSections } from '../../../api/sections/sectionapis';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { CloseIcon } from '../../../assets/icons/icons';
import mixpanel from 'mixpanel-browser';
import decodeJWT from '../../../utils/jwtdecoder';
import { useParams } from 'react-router-dom';
import { getPageNamesFromUrl } from '../PageName';

const schema = yup.object({
  section: yup.string().required('Please select working section'),
  shift: yup.string().required('Please select working shift'),
  goal: yup
    .number()
    .typeError('Goal must be a number')
    .positive('Goal must be positive')
    .max(1000, 'Value must be less than or equal to 1000')
    .integer('Goal must be a whole number')
    .required('Please enter production goal`'),
  serverError: yup.string(),
});

export default function AddGoalForm(props: any) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    getValues,
    setError,
    clearErrors,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const addGoalData = useAddGoal();
  const editGoal = useEditGoal();
  const checkActiveGoalMutation = useFindActiveGoal();
  const [selectFirstChild, setSelectFirstChlid] = useState(false);
  const [customError, setCustomError] = useState('');

  const shiftData = props?.getShiftRes?.data;
  const sectionData = props?.getSectionRes?.data;
  const mshaId =  decodeJWT()?.mshaId;
  const params = useParams();
  const url = getPageNamesFromUrl(params['*']);

  const onSubmitHandler = async (data: any) => {
    try {
      if (String(getValues('goal')).trim() !== '') {
        const selcetedSection = sectionData?.filter(
          (ele: any) => ele.id == data?.section
        )[0]?.sectionName;
        const selectedShift = shiftData?.filter(
          (ele: any) => ele.id == data?.shift
        )[0]?.shiftName;

        data.sectionId = data?.section;
        data.shiftId = data?.shift;
        data.goal = String(getValues('goal')).trim();

        delete data.section;
        delete data.shift;

        if (props?.editData) {
          try{
            data.id = props?.editData?.goalId;
          const goalData = await editGoal.mutateAsync(data);
          mixpanel.track('Update Goal',{
            Goal_Id :goalData?.data?.id ,
            MshaId : mshaId,
            Goal_Value :goalData?.data?.goal ,
          })
          toast.success('You have successfully edited the goal');
          reset();
          props?.setOpenAddGoalForm(false);
          }catch(err: any){
            mixpanel.track('Error Event', {
              Page_Name: url,
              Action_Name: 'Edit Goal'
            })
          }
        } else {
          const response = await checkActiveGoalMutation.mutateAsync(data);

          if (response.data.id) {
            setCustomError(
              `Goal already exists for ${selcetedSection} and ${selectedShift}!`
            );
            setError('serverError', {
              type: 'server',
              message: `Goal already exists for ${selcetedSection} and ${selectedShift}!`,
            });
          } else {
            try{
              const addedGoalData = await addGoalData.mutateAsync(data);
             mixpanel.track('Add Goal',{
              Goal_Id :addedGoalData?.data?.id ,
              MshaId : mshaId,
              Goal_Value :addedGoalData?.data?.goal ,
            })
            toast.success('You have successfully added new goal');
            reset();
            props?.setOpenAddGoalForm(false);
            }catch(err: any){
              mixpanel.track('Error Event', {
                Page_Name: url,
                Action_Name: 'Add Goal'
              })
            }
          }
        }
      } else {
        toast.error('Please enter the valid information');
      }
    } catch (err: any) {
      toast.error('Please enter the valid information');
      console.error(err.message);
    }
  };

  useEffect(() => {
    if (props.editData) {
      clearErrors();
      setValue('section', props?.editData?.sectionId);
      setValue('shift', props?.editData?.shiftId);
      setValue('goal', props?.editData?.goal);
    } else {
      reset();
    }
  }, [props?.editData, shiftData, sectionData, reset]);

  return (
    <div>
      <div className="">
        <div className="flex justify-between mx-4">
          <div className="block">
            <h6 className="font-semibold text-[24px] text-white">
              {props?.editData ? 'Edit Goal' : 'Add New Goal'}
            </h6>
            <p className="font-normal my-1 text-[16px] tracking-normal leading-5 text-[#cccccc] ">
              {!props?.editData
                ? 'Enter all of the required information to add a new goal'
                : ``}
            </p>
            <p className="font-semibold my-1 text-xs tracking-normal leading-5 text-red-500 text-start pt-1">
              {customError}
            </p>
          </div>
          <div
            className="mt-1 cursor-pointer"
            onClick={() => {
              props?.setOpenAddGoalForm(false);
            }}
          >
            <CloseIcon />
          </div>
        </div>
        <div className="">
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <div className="rounded pt-3">
              <div className="m-2">
                <div className="grid grid-cols-2">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Working Section
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <select
                      {...register('section')}
                      name="section"
                      id="section"
                      onChange={() => {
                        clearErrors(['shift', 'serverError']);
                        setCustomError('');
                        setSelectFirstChlid(true);
                      }}
                      className={`
                        'block w-full p-[7px]  rounded bg-gray-200  focus:border-blue-500 text-[14px]'
                        ${
                          !selectFirstChild && !props?.setEditData
                            ? 'text-gray-400'
                            : 'text-black'
                        }
                      `}
                      disabled={props?.editData ? true : false}
                    >
                      <option className="hidden" value={''}>
                        Select working section
                      </option>
                      {sectionData?.map((ele: any) => (
                        <option
                          value={ele?.id}
                          key={ele?.id}
                          className="text-black"
                        >
                          {ele.sectionName.charAt(0).toUpperCase() +
                            ele.sectionName.slice(1)}
                        </option>
                      ))}
                    </select>
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.section?.message}
                    </p>
                  </div>
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Shift
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <select
                      {...register('shift')}
                      name="shift"
                      id="shift"
                      onChange={() => {
                        clearErrors(['shift', 'serverError']);
                        setCustomError('');
                        setSelectFirstChlid(true);
                      }}
                      className={`
                        'block w-full p-[7px]  rounded bg-gray-200  focus:border-blue-500 text-[14px]'
                        ${
                          !selectFirstChild && !props?.setEditData
                            ? 'text-gray-400'
                            : 'text-black'
                        }
                      `}
                      disabled={props?.editData ? true : false}
                    >
                      <option className="hidden" value={''}>
                        Select shift
                      </option>
                      {shiftData?.map((ele: any) => (
                        <option
                          value={ele?.id}
                          key={ele?.id}
                          className="text-black"
                        >
                          {ele.shiftName.charAt(0).toUpperCase() +
                            ele.shiftName.slice(1)}
                        </option>
                      ))}
                    </select>
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.shift?.message}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 py-2">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Production Goal
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('goal')}
                      type="number"
                      name="goal"
                      id="goal"
                      min="1"
                      // max="1000"
                      className="bg-gray-200 p-1.5 w-full rounded pl-2 text-[14px] text-black"
                      placeholder="Enter production goal in feet"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.goal?.message}
                    </p>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 mt-5">
                <div className="mx-3">
                  <button
                    id="cancel_button"
                    title="Click to cancel"
                    onClick={() => {
                      props?.setOpenAddGoalForm(false);
                    }}
                    className={`text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center h-9 w-full items-center  me-2 mb-2 ${
                      props?.editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {'Cancel'}
                  </button>
                </div>
                <div className="mx-3">
                  <button
                    id="create_goal"
                    title={
                      !props?.editData
                        ? 'Click to add new goal information'
                        : 'Click to save goal information'
                    }
                    type="submit"
                    className={`text-white bg-[#4AA8FE]  hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-8 py-2 text-center h-9 w-full items-center  me-2 mb-2 ${
                      props?.editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {props?.editData ? 'Save' : 'Add'}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
