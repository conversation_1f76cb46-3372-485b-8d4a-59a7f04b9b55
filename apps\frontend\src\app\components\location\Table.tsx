import React, { useState, useMemo } from 'react';
import dayjs from 'dayjs';

export interface Column {
  key: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'datetime' | 'time' | 'element';
  render?: (row: any) => React.ReactNode;
}

export interface SortConfig {
  key: string | null;
  direction: 'asc' | 'desc';
}

export interface Props {
  data: any[];
  columns: Column[];
  searchText: string;
}

const Table: React.FC<Props> = ({ data, columns, searchText }) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: null,
    direction: 'asc',
  });

  const sortedData = useMemo(() => {
    if (!sortConfig.key) return data;
    const sorted = [...data].sort((a, b) => {
      const keyA = a[sortConfig.key as string];
      const keyB = b[sortConfig.key as string];
      if (keyA < keyB) return sortConfig.direction === 'asc' ? -1 : 1;
      if (keyA > keyB) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
    return sorted;
  }, [data, sortConfig]);

  const requestSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const renderCellContent = (row: any, column: Column) => {
    if (column.render) return column.render(row);
    const cellValue = row[column.key];
    switch (column.type) {
      case 'date':
        return dayjs(cellValue).format('MM-DD-YY');
      case 'datetime':
        return dayjs(cellValue).format('MM-DD-YY hh:mm A');
      case 'time':
        return dayjs(cellValue).format('HH:mm');
      default:
        return String(cellValue);
    }
  };

  return (
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-[#21303C]">
        <tr>
          {columns?.map(({ key, label }) => (
            <th
              key={key}
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => requestSort(key)}
            >
              {label}
              {sortConfig.key === key && (
                <span>{sortConfig.direction === 'asc' ? ' ▲' : ' ▼'}</span>
              )}
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {sortedData?.map((row, rowIndex) => (
          <tr key={rowIndex}>
            {columns?.map((column) => (
              <td key={column.key} className="px-6 py-4 whitespace-nowrap">
                {renderCellContent(row, column)}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>

    // <>Checkins...........</>
  );
};

export default Table;
