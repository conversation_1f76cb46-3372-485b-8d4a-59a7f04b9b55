import { useEffect, useMemo, useState } from 'react';
import Table, { ColumnDef } from '../../common/Table';
import { CSVIcon } from '../../../../../src/assets/icons/icons';
import CommonChartForReportForEnv from './reportChartForEnv';
import CommonChartSync from './commonSyncChart';
import mixpanel from 'mixpanel-browser';
import decodeJWT from '../../../../utils/jwtdecoder';
import { getPageNamesFromUrl } from '../../PageName';
import { useParams } from 'react-router-dom';

const data = [
  {
    label: '7 Scoop South Charger',
    id: 'A091',
    humidityAvg: '78%',
    tempAvg: '64℉',
    absPressure: '420.6',
    gases: ['Abs. Pressure', 'Temperature', 'Humidity'],
    values: [
      [1, 5, 3, 6, 0, 9], // Abs. Pressure
      [72, 80, 56, 82, 76, 86], // Temperature
      [80, 79, 83, 74, 86, 72], // Humidity
    ],
  },
  {
    label: '7Y S North XC-12',
    id: 'A092',
    humidityAvg: '78%',
    tempAvg: '64℉',
    absPressure: '420.6',
    gases: ['Abs. Pressure', 'Temperature', 'Humidity'],
    values: [
      [5, 2, 4, 6, 0, 1],
      [66, 67, 68, 70, 69, 71],
      [78, 79, 77, 76, 75, 74],
    ],
  },
  {
    label: '7Y 5 North XC-12',
    id: 'A093',
    humidityAvg: '78%',
    tempAvg: '64℉',
    absPressure: '420.6',
    gases: ['Abs. Pressure', 'Temperature', 'Humidity'],
    values: [
      [2, 7, 3, 6, 3, 2],
      [60, 62, 65, 64, 66, 68],
      [81, 80, 79, 82, 78, 77],
    ],
  },
  {
    label: 'Main West Belt XC-6',
    id: 'A094',
    humidityAvg: '78%',
    tempAvg: '64℉',
    absPressure: '420.6',
    gases: ['Abs. Pressure', 'Temperature', 'Humidity'],
    values: [
      [8, 3, 4, 7, 0, 1],
      [70, 69, 68, 72, 74, 75],
      [85, 86, 87, 84, 83, 82],
    ],
  },
  {
    label: '7Y 5 North XC-3',
    id: 'A095',
    humidityAvg: '78%',
    tempAvg: '64℉',
    absPressure: '420.6',
    gases: ['Abs. Pressure', 'Temperature', 'Humidity'],
    values: [
      [0, 1, 4, 8, 3, 1],
      [67, 65, 66, 69, 70, 71],
      [76, 78, 77, 74, 72, 73],
    ],
  },
  {
    label: '7 Scoop North Charger',
    id: 'A096',
    humidityAvg: '78%',
    tempAvg: '64℉',
    absPressure: '420.6',
    gases: ['Abs. Pressure', 'Temperature', 'Humidity'],
    values: [
      [1, 2, 7, 5, 0, 3],
      [68, 67, 66, 70, 71, 72],
      [79, 80, 78, 76, 74, 73],
    ],
  },
  {
    label: '7Y 3 North XC-22',
    id: 'A097',
    humidityAvg: '78%',
    tempAvg: '64℉',
    absPressure: '420.6',
    gases: ['Abs. Pressure', 'Temperature', 'Humidity'],
    values: [
      [2, 5, 3, 6, 0, 9],
      [69, 70, 71, 72, 68, 66],
      [77, 76, 75, 74, 73, 72],
    ],
  },
];

const ReportEnvironment = () => {
  const params = useParams();
  const decoded = decodeJWT();
  const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
  const [sync, setSync] = useState(false);
  const selectedGases = data?.filter((ele: any) =>
    selectedRowIds?.includes(ele?.id)
  );
  const uniqueGases = useMemo(() => {
    return [...new Set(selectedGases.flatMap((item) => item.gases))];
  }, [selectedGases]);
  const [gasStates, setGasStates] = useState<Record<string, boolean>>({});

  useEffect(() => {
    setGasStates((prev) => {
      let updated = { ...prev };
      let changed = false;

      uniqueGases.forEach((gas) => {
        if (!(gas in updated)) {
          updated[gas] = true;
          changed = true;
        }
      });

      return changed ? updated : prev; // prevent state update if no changes
    });
  }, [uniqueGases]);

  const handleToggle = (gas: string) => {
    setGasStates((prev: any) => ({
      ...prev,
      [gas]: !prev[gas],
    }));
  };

  const [showModal, setShowModal] = useState(false);
  const [fileName, setFileName] = useState('');

  const handleCheckboxChange = (row: any) => {
    setSelectedRowIds((prev) => {
      const isSelected = prev.includes(row.id);
      const updated = isSelected
        ? prev.filter((id) => id !== row.id)
        : [...prev, row.id];

      mixpanel.track('Sensor Check', {
        Page_Name: getPageNamesFromUrl(
          params['*'] ?? 'Atmosphere Environmental Report'
        ),
        MineName: decoded?.minename,
      });

      return updated;
    });
  };

  const columns: ColumnDef[] = [
    {
      key: 'status',
      label: `<div class="inline-flex items-center pt-1">
    <label
      class="relative flex items-center cursor-pointer"
      htmlFor="selectAll">
    <input type="checkbox" id="selectAll" class="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] checked:before:bg-gray-900 hover:before:opacity-10"/>
    <span class="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-3 w-3"
          viewBox="0 0 20 20"
          fill="#1A252F"
          stroke="#1A252F"
          strokeWidth="1"
        >
          <path
            fillRule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clipRule="evenodd"
          ></path>
        </svg>
      </span>
    </label>
  </div>`,
      type: 'sr_no',
      render: (row: any) => (
        <div className="inline-flex items-center pt-1">
          <label
            className="relative flex items-center cursor-pointer"
            htmlFor="check"
          >
            <input
              type="checkbox"
              className="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] checked:before:bg-gray-900 hover:before:opacity-10"
              id="selectCheckbox"
              onChange={() => {
                handleCheckboxChange(row);
              }}
            />
            <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                viewBox="0 0 20 20"
                fill="#1A252F"
                stroke="#1A252F"
                strokeWidth="1"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </span>
          </label>
        </div>
      ),
    },
    { key: 'label', label: 'Label', type: 'text' },
    { key: 'id', label: 'ID', type: 'text' },
    {
      key: 'absPressure',
      label: 'Abs. Pressure Avg',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.absPressure}</div>
          <div className="text-[12px] text-white/50 float-right">in. H20</div>
        </div>
      ),
    },
    { key: 'tempAvg', label: 'Temperature Avg', type: 'text' },
    { key: 'humidityAvg', label: 'Humidity Avg', type: 'text' },
  ];

  const downloadCSV = () => {
    const csvRows = [];

    // Add headers
    const headers = [
      'Label',
      'ID',
      'Abs. Pressure Avg',
      'Temperature Avg',
      'Humidity Avg',
    ];
    csvRows.push(headers.join(','));

    // Add data
    data.forEach((row) => {
      const values = [
        row.label,
        row.id,
        row.absPressure,
        row.tempAvg,
        row.humidityAvg,
      ];
      csvRows.push(values.join(','));
    });

    const blob = new Blob([csvRows.join('\n')], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}.csv`;

    a.click();
    window.URL.revokeObjectURL(url);
  };
  return (
    <div className="px-[4%]">
      <div className="text-white overflow-hidden">
        {sync &&
        Object.keys(gasStates).filter((key) => gasStates[key])?.length == 2 &&
        selectedRowIds?.length != 0 ? (
          <>
            <CommonChartSync
              data={{
                row:
                  selectedGases?.map((ele) => ({
                    label: ele?.label,
                    values: [...ele?.values],
                    gases: ele?.gases,
                  })) || [],
              }}
              gases={Object.keys(gasStates).filter((key) => gasStates[key])}
            />
          </>
        ) : (
          <CommonChartForReportForEnv
            data={{
              row:
                selectedGases?.map((ele) => ({
                  label: ele?.label,
                  values: [...ele?.values],
                  gases: ele?.gases,
                })) || [],
            }}
            gases={
              selectedRowIds?.length != 0
                ? Object.keys(gasStates).filter((key) => gasStates[key])
                : []
            }
          />
        )}
      </div>
      <div className="pt-8 pb-2 userMTableBg px-5 rounded-xl">
        {' '}
        <div className="flex justify-between items-start gap-4 mb-4">
          {/* Left section: Gas buttons and Sync */}
          <div className="flex gap-4 flex-wrap">
            {uniqueGases.map((gas) => (
              <button
                key={gas}
                className={`text-white px-4 py-2 rounded-lg text-sm font-medium  ${
                  gasStates[gas] ? 'bg-[#FFB132]' : ''
                }`}
                onClick={() => {
                  handleToggle(gas);

                  mixpanel.track('Report Filter Module', {
                    Module_Name: gas,
                    Page_Name: getPageNamesFromUrl(
                      params['*'] ?? 'Atmosphere Environmental Report'
                    ),
                  });
                }}
              >
                {gas}
              </button>
            ))}

            {uniqueGases.length > 1 && (
              <button
                onClick={() => {
                  setSync(!sync);

                  mixpanel.track('Module Sync', {
                    Module_Name: "sync",
                    Page_Name: getPageNamesFromUrl(
                      params['*'] ?? 'Atmosphere Gas Report'
                    ),
                  });
                }}
                className={`${
                  sync
                    ? 'bg-[#FFB132] border-none'
                    : 'border-[2px] border-dashed py-[6px] px-[30px]'
                } text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline`}
              >
                Sync
              </button>
            )}
          </div>

          {/* Right section: Export button */}
          <div
            className="flex items-center text-[16px] text-[#FFB132] gap-2 cursor-pointer "
            onClick={() => {
              mixpanel.track('CSV Export', {
                // PageName: 'Atmosphere Environmental Report',
                Page_Name: getPageNamesFromUrl(params['*'] ?? ''),
              });

              setShowModal(true);
            }}
          >
            <CSVIcon /> Export to CSV
          </div>
        </div>
        <Table
          columns={columns}
          data={data}
          searchText={''}
          searchOnColumn=""
          backgroundColor={false}
          scrollable={true}
          sortable={true}
          dataRenderLimitMdScreen={4}
          dataRenderLimitLgScreen={5}
          tableHeightClassLg={`h-[240px]`}
          tableHeightClassMd={`h-[240px]`}
        />
      </div>
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[#0F0F0F] text-white p-4 rounded-xl w-[25%] shadow-xl">
            <h2 className="text-xl font-bold mb-4 text-center">
              Export to CSV
            </h2>

            <label className="block mb-2 font-semibold">File Name</label>
            <input
              type="text"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              className="w-full px-4 py-2 rounded-md text-black focus-none outline-none"
              placeholder="Enter file name"
            />

            <div className="flex justify-between mt-6 w-full">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 w-[48%] border border-blue-500 text-white rounded-md hover:bg-blue-500"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  downloadCSV();
                  setShowModal(false);
                }}
                className="px-4 py-2 w-[48%] bg-blue-500 hover:bg-blue-600 text-white rounded-md"
              >
                Export Data
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default ReportEnvironment;
