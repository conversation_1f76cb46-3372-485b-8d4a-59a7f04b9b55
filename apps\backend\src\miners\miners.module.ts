import { Module } from '@nestjs/common';
import { MinersController } from './miners.controller';
import { MinersService } from './miners.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Miner } from './entities/miner.entity';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [TypeOrmModule.forFeature([Miner]), UsersModule],
  controllers: [MinersController],
  providers: [MinersService],
})
export class MinersModule {}
