import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Mine } from '../../mines/entities/mine.entity';
import { Role } from '../../roles/entities/role.entity';import { User } from '../../users/entities/user.entity';

@Entity('companies')
export class Company {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ unique: true, nullable: false })
  name: string;

  @Column({ unique: true, nullable: false })
  code: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @OneToMany(() => Mine, (mine) => mine.company)
  mines: Mine[];

  @OneToMany(() => Role, (role) => role.company)
  roles: Role[];

  @OneToMany(() => User, (user) => user.company)
  users: User[];
}
