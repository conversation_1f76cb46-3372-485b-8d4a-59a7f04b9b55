import { Test, TestingModule } from '@nestjs/testing';
import { RoleFeatureAccessController } from './role_feature_access.controller';

describe('RoleFeatureAccessController', () => {
  let controller: RoleFeatureAccessController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RoleFeatureAccessController],
    }).compile();

    controller = module.get<RoleFeatureAccessController>(RoleFeatureAccessController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
