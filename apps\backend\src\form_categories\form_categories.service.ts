import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateFormCategoryDto } from './dto/create-form-category.dto';
import { FormCategory } from './entities/form_category.entity';
@Injectable()
export class FormCategoriesService {
  constructor(
    @InjectRepository(FormCategory) private formCategoryRepo: Repository<FormCategory>
  ) {}

  async create(formCategoryDto: CreateFormCategoryDto, user?: any): Promise<FormCategory> {
    if(user) {
      formCategoryDto.mineId = user.mineid;
      formCategoryDto.createdBy = user.userId;
    }
    return this.formCategoryRepo.save(formCategoryDto);
  }

  async findAll(mineId: number): Promise<FormCategory[]> {
    return await this.formCategoryRepo.find({
      where: {
        mineId: mineId,
        isDelete: false
      },
    });
  }

  async findUsedByTemplates(mineId: number): Promise<FormCategory[]> {
    const formCategoryData = await this.formCategoryRepo.createQueryBuilder('fc')
      .select([
        'MAX(fc.id) AS id',
        'MAX(fc.name) AS name',
      ])
      .innerJoin('form_templates', 'ft', 'ft.formCategoryId = fc.id')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.formTemplateId = ft.id')
      .where('fc.mineId = :mineId', { mineId: mineId })
      .andWhere('fc.is_delete = :isDelete', { isDelete: false })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('ftd.is_published = :isPublished', { isPublished: true })
      .groupBy('fc.name')
      .addOrderBy('fc.name', 'ASC')
      .getRawMany();
      return formCategoryData;
  }

  async findUsedBySavedForms(mineId: number): Promise<FormCategory[]> {
    const formCategoryData = await this.formCategoryRepo.createQueryBuilder('fc')
      .select([
        'MAX(fc.id) AS id',
        'MAX(fc.name) AS name',
      ])
      .innerJoin('form_templates', 'ft', 'ft.formCategoryId = fc.id')
      .innerJoin('forms', 'f', 'f.formTemplateId = ft.id')
      .where('fc.mineId = :mineId', { mineId: mineId })
      .andWhere('fc.is_delete = :isDelete', { isDelete: false })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('f.is_delete = :isDelete', { isDelete: false })
      .andWhere('f.is_submitted = :isSubmitted', { isSubmitted: false })
      .groupBy('fc.name')
      .addOrderBy('fc.name', 'ASC')
      .getRawMany();
      return formCategoryData;
  }

  async findUsedByCompletedForms(mineId: number): Promise<FormCategory[]> {
    const formCategoryData = await this.formCategoryRepo.createQueryBuilder('fc')
      .select([
        'MAX(fc.id) AS id',
        'MAX(fc.name) AS name',
      ])
      .innerJoin('form_templates', 'ft', 'ft.formCategoryId = fc.id')
      .innerJoin('forms', 'f', 'f.formTemplateId = ft.id')
      .where('fc.mineId = :mineId', { mineId: mineId })
      .andWhere('fc.is_delete = :isDelete', { isDelete: false })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('f.is_delete = :isDelete', { isDelete: false })
      .andWhere('f.is_submitted = :isSubmitted', { isSubmitted: true })
      .groupBy('fc.name')
      .addOrderBy('fc.name', 'ASC')
      .getRawMany();
      return formCategoryData;
  }

  async findOne(id: number): Promise<FormCategory> {
    const FormCategory = this.formCategoryRepo.findOne({
      where: {
        id,
      },
    });

    if (!FormCategory) {
      throw new NotFoundException('Form Category Not Found!');
    }
    return FormCategory;
  }

  async deleteFormCategory(id: number, user?: any) {
    let formCategory = await this.findOne(id);
    if (!formCategory) {
      throw new NotFoundException(`form category with given id ${id} is not found`);
    } else {
      if (formCategory.isDelete === true) {
        formCategory.isDelete = false;
      } else {
        formCategory.isDelete = true;
      }
      formCategory.updatedBy = user.userId;
      return this.formCategoryRepo.save(formCategory);
    }
  }
}
