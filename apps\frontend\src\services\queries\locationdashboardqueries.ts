import { useQuery } from '@tanstack/react-query';
import {
  getLocationLiveCheckInsData,
  getLocationReportCheckInsData,
} from '../../api/location/locationdashboardapi';

export function useLocationLiveCheckInsData(mineId: number) {
  return useQuery({
    queryKey: ['location-live-checkins'],
    queryFn: () => getLocationLiveCheckInsData(mineId),
    refetchOnWindowFocus: false,
  });
}

export function useLocationReportCheckInsData(mineId: number, date: string) {
  return useQuery({
    queryKey: ['location-report-checkins'],
    queryFn: () => getLocationReportCheckInsData(mineId, date),
    refetchOnWindowFocus: false,
  });
}
