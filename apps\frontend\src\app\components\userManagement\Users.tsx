import { useEffect, useRef, useState } from 'react';
import { UserMenu } from '../common/UserMenu';
import {
  EditIcon,
  KeyIcon,
  SearchIcon,
  TrashIcon,
} from '../../../assets/icons/icons';
import Table, { ColumnDef } from '../common/Table';
import AddUserForm from './AddUserForm';
import Modal from '../common/Modal';
import { useQuery } from '@tanstack/react-query';
import { getUsers } from '../../../api/users/userapis';
import {
  useDeleteUser,
  useResetPassword,
  useStatusChange,
} from '../../../services/mutations/usermutations';
import { toast } from 'react-toastify';
import decodeJWT from '../../../utils/jwtdecoder';
import { Tooltip } from '@material-tailwind/react';
import { escapeRegExp } from '../../../utils/constant';
import { getFeatures } from '../../../api/users/userapis';
import Loader from '../common/Loader';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import { useParams } from 'react-router-dom';
export default function Users() {
  const [openAddUserFrom, setOpenAddUserFrom] = useState(false);
  const [editData, setEditData] = useState(null);
  const [userData, setUserData] = useState<any>();
  const [searchPh, setSearchPh] = useState('');
  const [disabledButton, setDisabledButton] = useState(false);
  const [openDeleteModal, setOpenDeteteModal] = useState(false);
  const [openChangePassModal, setOpenChangePassModal] = useState(false);
  const [tableCount, setTableCount] = useState();
  const { data, isLoading, isFetching } = useQuery({
    queryKey: ['users'],
    queryFn: getUsers,
    refetchOnWindowFocus: false,
  });
  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  const resetPassword = useResetPassword();
  const deleteUser = useDeleteUser();
  const statusChange = useStatusChange();
  const decoded = decodeJWT();
  const params = useParams();
  const url = getPageNamesFromUrl(params['*']);
  useEffect(() => {
    const handleOverflow = (cell: any) => {
      if (cell.offsetWidth < cell.scrollWidth) {
        cell.setAttribute('title', cell.innerText);
      } else {
        cell.removeAttribute('title');
      }
    };

    const observeDataChanges = () => {
      const tableCells = document?.querySelectorAll('.text-ellipsis');
      if (tableCells) {
        tableCells.forEach((cell) => {
          handleOverflow(cell);
        });
      }
    };

    observeDataChanges(); // Initial observation

    const observer = new MutationObserver(observeDataChanges);
    observer.observe(document.body, { subtree: true, childList: true });

    return () => {
      observer.disconnect();
    };
  }, [data?.data]);

  const regex = new RegExp(`(${escapeRegExp(searchPh)})`, 'i');
  const columns: ColumnDef[] = [
    {
      key: 'name',
      label: 'Name',
      type: 'text',
      render: (row: any) => (
        <div className="">
          <div
            className="text-ellipsis overflow-hidden w-35"
            // title={row.users_first_name + ' ' + row.users_last_name}

            dangerouslySetInnerHTML={{
              __html: row.name.replace(
                regex,
                "<span style='color: #FFD084;'>$1</span>"
              ),
            }}
          ></div>
        </div>
      ),
    },
    {
      key: 'users_username',
      label: 'Username',
      type: 'text',
    },
    {
      key: 'roleName',
      label: 'Role',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.roleName.charAt(0).toUpperCase() + row.roleName.slice(1)}
        </div>
      ),
    },
    {
      key: 'users_is_active',
      label: 'Status',
      type: 'text',
      render: (row) => (
        <div
          title={row.users_is_active ? 'Deactivate User' : 'Activate User'}
          className={`relative w-[32px] h-[16px]  rounded-full  ${
            decodeJWT()?.username == row?.users_username
              ? ' cursor-not-allowed '
              : 'cursor-pointer'
          }`}
        >
          <label
            className={
              decodeJWT()?.username == row?.users_username
                ? ' cursor-not-allowed '
                : 'cursor-pointer'
            }
          >
            <input
              type="checkbox"
              disabled={
                decodeJWT()?.username == row?.users_username ? true : false
              }
              tabIndex={Number('-1')}
              checked={row.users_is_active}
              className="sr-only peer"
              onChange={async (e: any) => {
                if (e.target.checked) {
                  if (decodeJWT()?.username !== row?.users_username) {
                    try {
                      await statusChange.mutateAsync(row?.userId);
                      mixpanel.track('Active User', {
                        MshaId: decoded?.mshaId,
                      });
                      toast.success('User activated successfully');
                    } catch (err) {
                      console.log(err);
                      mixpanel.track('Error Event', {
                        Page_Name: url,
                        Action_Name: 'Active User',
                      });
                    }
                  }
                } else {
                  try {
                    await statusChange.mutateAsync(row?.userId);
                    mixpanel.track('Inactive User', {
                      MshaId: decoded?.mshaId,
                    });
                    toast.success('User deactivated successfully');
                  } catch (err) {
                    console.log('err', err);
                    mixpanel.track('Error Event', {
                      Page_Name: url,
                      Action_Name: 'Deactive User',
                    });
                  }
                }
              }}
            />
            <div
              className={`w-full h-full bg-gray-200 ${
                row?.users_is_active
                  ? ' toggleShadowActive '
                  : ' toggleShadowInActive '
              }  ${
                decodeJWT()?.username == row?.users_username
                  ? ' peer-checked:border-gray-200 peer-checked:bg-gray-200 after:bg-gray-200'
                  : ''
              } bg-opacity-25 border-[0.5px] border-gray-200 outline-none peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-[162%] rtl:peer-checked:after:-translate-x-[162%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white  after:rounded-full after:h-[10px] after:w-[10px] after:my-[1px] after:mx-[2px]  after:transition-all  peer-checked:bg-[#96FB60] peer-checked:bg-opacity-25  peer-checked:border-[1px] peer-checked:border-[#96FB60]`}
            ></div>
          </label>
        </div>
      ),
    },
    {
      key: 'action',
      label: 'Actions',
      type: 'element',
      render: (row) => (
        <div className="flex justify-center">
          <div
            title="Edit User"
            className="cursor-pointer"
            data-tooltip-target="tooltip-default"
            onClick={() => {
              setEditData(row);
              setOpenAddUserFrom(true);
              document
                ?.getElementsByClassName('scrollToTop')[0]
                ?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                });
            }}
          >
            <EditIcon className="text-white font-black h-5 w-5 mx-2  edit-icon" />
          </div>
          <div
            title="Reset Password"
            className={`${
              row.users_is_active
                ? decodeJWT()?.username !== row?.users_username
                  ? 'cursor-pointer'
                  : 'cursor-not-allowed opacity-50'
                : 'cursor-not-allowed opacity-50'
            }`}
            onClick={() => {
              if (row.users_is_active) {
                if (decodeJWT()?.username !== row?.users_username) {
                  setOpenChangePassModal(true);
                  setUserData(row);
                }
              }
            }}
          >
            <KeyIcon className="text-white font-black h-5 w-5 mx-2  key_icon " />
          </div>
          <div
            title="Delete User"
            className={`${
              decodeJWT()?.username !== row?.users_username
                ? 'cursor-pointer'
                : 'cursor-not-allowed opacity-50'
            }`}
            onClick={() => {
              if (decodeJWT()?.username !== row?.users_username) {
                setOpenDeteteModal(true);
                setUserData(row);
              }
            }}
          >
            <TrashIcon className="text-white font-black text-[14px] h-5 w-5 mx-2 delete_icon" />
          </div>
        </div>
      ),
    },
  ];

  useEffect(() => {
    if (localStorage.getItem('refreshingPage')) {
      localStorage.removeItem('refreshingPage');
      setTimeout(() => {
        toast.info(
          `Your Role has been updated to ${
            decodeJWT()?.role.charAt(0).toUpperCase() +
            decodeJWT()?.role.slice(1)
          }`
        );
      }, 1000);
    }
  });

  if (
    features?.data.some((feature: any) => feature.FeatureName == 'Users') ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <div className="w-full">
        <div className="sticky w-full top-0 z-30  box  bg-top px-10 2xl:px-16">
          <div className="grid grid-cols-3 border-b-[1px] border-[#80c2fe]  pb-4 pt-4 ">
            <div className="">
              <h6 className=" font-bold text-white text-[32px] text-left">
                Users
              </h6>
            </div>
            <div className=" h-12 w-72">
              <div className="relative  pt-1">
                <span>
                  <SearchIcon className="absolute top-0 left-2 h-6 mr-1 my-[12px]   cursor-pointer"></SearchIcon>
                </span>
                <input
                  id="search-result"
                  type="text"
                  className={`border   text-white text-sm pl-8 outline-none bg-transparent rounded border-[#4AA8FE] block w-full p-2   autoComOff`}
                  placeholder="Search User"
                  autoFocus
                  value={searchPh}
                  onChange={(e: any) => {
                    setSearchPh(e?.target?.value);
                    mixpanel.track('Personnel Search', {
                      MshaId: decoded?.mshaId,
                      Page_Name: url,
                    });
                  }}
                />
              </div>
            </div>
            <div className="">
              {/* <h6 className="p-2 font-bold text-white text-[32px] text-right">
                Settings
              </h6> */}
              <div className="flex justify-end items-center -mr-10 relative">
                <h6 className=" font-bold text-white text-[32px]">Settings</h6>
                <span className="ml-4">
                  <UserMenu />
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-8  px-10 2xl:px-16">
          <div>
            {openAddUserFrom ? (
              <Modal
                Content={
                  <AddUserForm
                    editData={editData}
                    setOpenAddUserFrom={setOpenAddUserFrom}
                  />
                }
                size="w-2/4 2xl:w-2/5 xl:w-2/5"
                backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
              ></Modal>
            ) : (
              ''
            )}
          </div>
          <div>
            {isLoading ? (
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            ) : (
              <div className="pt-8 pb-2 userMTableBg px-5 rounded-xl">
                <div className="flex justify-between">
                  <div className="block">
                    <h6 className="font-semibold text-[20px] text-white">
                      {`Platform Users  (${tableCount ? tableCount : 0})`}
                    </h6>
                    <p className="font-normal my-1 text-[14px] tracking-normal leading-5 text-[#cccccc] ">
                      Manage existing users, change roles/ permissions, or
                      deactivate users
                    </p>
                  </div>
                  <div>
                    <button
                      id="add_new_user"
                      title="Click to open add user form"
                      className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center  items-center  me-2 mb-2"
                      onClick={() => {
                        setEditData(null);
                        setOpenAddUserFrom(true);
                      }}
                    >
                      Add New User
                    </button>
                  </div>
                </div>
                <div className="mt-5">
                  {data?.data.length > 0 ? (
                    <div>
                      <Table
                        columns={columns}
                        data={
                          data?.data?.map((ele: any) => ({
                            name:
                              ele?.users_first_name +
                              ' ' +
                              ele?.users_last_name,
                            ...ele,
                          })) ?? []
                        }
                        searchText={searchPh}
                        sortable={false}
                        searchOnColumn={'name'}
                        separateLine={false}
                        scrollable={true}
                        dataRenderLimitMdScreen={8}
                        dataRenderLimitLgScreen={15}
                        tableHeightClassLg="tableheightForlg"
                        tableHeightClassMd="tableheightFormd"
                        setTableCount={setTableCount}
                      />
                    </div>
                  ) : (
                    <div className="text-[26px] font-bold  text-center text-white">
                      There are currently no users
                    </div>
                  )}
                </div>

                {openChangePassModal ? (
                  <Modal
                    size="w-2/4 2xl:w-1/3 xl:w-2/4"
                    Content={
                      <div className="p-4 text-provima">
                        <div className="font-bold text-[24px] text-white text-center text-ellipsis overflow-hidden m-auto">
                          {`Reset  ${userData?.users_first_name} ${userData?.users_last_name}'s Password?`}
                        </div>
                        <div className="text-[24px] font-normal text-center text-white">
                          A new password will be emailed to the user.
                        </div>
                        <div className="mt-8 text-center">
                          <button
                            title="Click to cancel"
                            id="cancel_button"
                            onClick={() => {
                              setOpenChangePassModal(!openChangePassModal);
                              setUserData(null);
                            }}
                            className="  text-white text-[14px] hover:border-[#4AA8FE]/75 font-normal py-2 px-8 rounded focus:outline-none focus:shadow-outline border mr-2 border-[#4AA8FE] my-2"
                          >
                            Cancel
                          </button>
                          <button
                            disabled={disabledButton}
                            id="reset_button"
                            title="Click to reset password"
                            onClick={async (e: any) => {
                              e.preventDefault();
                              setDisabledButton(true);
                              try {
                                const res = await resetPassword.mutateAsync({
                                  email: userData?.users_email,
                                });
                                if (res?.status == 200 || res?.status == 201) {
                                  setOpenChangePassModal(!openChangePassModal);
                                  setDisabledButton(false);
                                  mixpanel.track('Password Reset', {
                                    MshaId: decoded?.mshaId,
                                  });
                                  toast.success('Password reset successfully');
                                  setUserData(null);
                                } else {
                                  setOpenChangePassModal(!openChangePassModal);
                                  // toast.error('');
                                }
                              } catch (err: any) {
                                setOpenChangePassModal(!openChangePassModal);
                                setDisabledButton(false);
                                toast.error(err.message);
                                mixpanel.track('Error Event', {
                                  Page_Name: url,
                                  Action_Name: 'Password Reset',
                                });
                              }
                            }}
                            className="my-2 bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 hover:border-[#4AA8FE]/75 text-white text-[14px]  font-normal py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                          >
                            Yes, Reset Password
                          </button>
                        </div>
                      </div>
                    }
                    backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
                  />
                ) : (
                  <></>
                )}
                {openDeleteModal ? (
                  <Modal
                    size="w-2/4 2xl:w-1/3 xl:w-2/4"
                    Content={
                      <div className="p-4">
                        <div className="text-[24px] text-white text-center ">
                          Are you sure you want to delete?
                        </div>
                        <div className="my-2 text-[56px] text-[#4AA8FE]  text-center text-provima  text-ellipsis overflow-hidden m-auto">
                          {userData?.users_first_name +
                            ' ' +
                            userData?.users_last_name}
                        </div>
                        <div className="mt-2 text-center">
                          <button
                            title="Click to Cancel"
                            id="cancel_button"
                            onClick={() => setOpenDeteteModal(!openDeleteModal)}
                            className="my-2  text-white hover:border-[#4AA8FE]/75 text-[14px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE] mr-2"
                          >
                            Cancel
                          </button>
                          <button
                            title="Click to delete user"
                            id="delete_button"
                            onClick={async (e: any) => {
                              e.preventDefault();
                              try {
                                const res = await deleteUser.mutateAsync(
                                  userData?.userId
                                );
                                if (res?.status == 200 || res?.status == 201) {
                                  setOpenDeteteModal(!openDeleteModal);
                                  setUserData({});
                                  mixpanel.track('Delete User', {
                                    MshaId: decoded?.mshaId,
                                  });
                                  toast.success('User deleted successfully');
                                }
                              } catch (err: any) {
                                toast.error(err.message);
                                mixpanel.track('Error Event', {
                                  Page_Name: url,
                                  Action_Name: 'Delete User',
                                });
                              }
                            }}
                            className="my-2 bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 hover:border-[#4AA8FE]/75 text-white text-[14px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                          >
                            Yes, Permanently Delete
                          </button>
                        </div>
                      </div>
                    }
                    backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
                  />
                ) : (
                  <></>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } else {
    return '';
  }
}
