import { Controller, Get, Request, UseGuards } from '@nestjs/common';
import { Roles } from '../auth/gurads/roles.decorator';
import { Features } from '../auth/gurads/feature.decorator';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { RolesGuard } from '../auth/gurads/roles.guard';
import { MinersService } from './miners.service';
import { ApiTags } from '@nestjs/swagger';

@Controller('portal/v1/miners')
@ApiTags('miner')
export class MinersController {
  constructor(private readonly minerService: MinersService) {}
  @Get()
  @UseGuards(JwtAuthGuard)
  GetAllCompanyMiners(@Request() req) {
    const user = req.user;
    return this.minerService.getCompanyMiners(user);
  }
}
