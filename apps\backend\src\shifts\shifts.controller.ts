import {
  <PERSON>,
  Get,
  Post,
  Patch,
  Param,
  Body,
  UseGuards,
  Request
} from '@nestjs/common';
import { Shift } from './entities/shift.entity';
import { ShiftsService } from './shifts.service';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { CreateShiftDto } from './dto/create-shift.dto';
import { UpdateShiftDto } from './dto/update-shift.dto';
import { ApiTags } from '@nestjs/swagger';

@Controller('portal/v1/shifts')
@ApiTags('shifts')
export class ShiftsController {
  constructor(private readonly shiftsService: ShiftsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() createShiftDto: CreateShiftDto, @Request() req) {
    let mineId = req.user.mineid;
    return this.shiftsService.create(createShiftDto, mineId);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Request() req) {
    let mineId = req.user.mineid;
    return this.shiftsService.findAll(mineId);
  }

  @Get('name/:name')
  @UseGuards(JwtAuthGuard)
  async findByName(@Param('name') name: string, @Request() req): Promise<Shift> {
    let mineId = req.user.mineid;
    const decodedName = decodeURIComponent(name);
    return await this.shiftsService.findByName(mineId, decodedName);
  }

  @Get('all-shifts')
  @UseGuards(JwtAuthGuard)
  async findAllShifts() {
    return this.shiftsService.findAllShifts();
  }

  @Get('active-shifts')
  @UseGuards(JwtAuthGuard)
  async findActiveShifts(@Request() req) {
    let mineId = req.user.mineid;
    return this.shiftsService.findActiveShifts(mineId);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: number) {
    return this.shiftsService.findOne(+id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  async update(@Param('id') id: number, @Body() updateShiftDto: UpdateShiftDto) {
    return this.shiftsService.update(id, updateShiftDto);
  }

  @Patch('isActive/:id')
  @UseGuards(JwtAuthGuard)
  async updateShiftStatus(@Param('id') id: number) {
    return this.shiftsService.updateShiftStatus(id);
  }
}
