import { getUserById } from '../../../api/users/userapis';
import decodeJWT from '../../../utils/jwtdecoder';
import { useQuery } from '@tanstack/react-query';

import SubMenuInnerNavBar from '../common/SubMenuInnerNavBar';
import { SunIcon } from '../../../assets/icons/icons';
import { WifiIcon } from '../../../assets/icons/icons';
import { AxeIcon } from '../../../assets/icons/icons';
import Card from '../common/Card';
import { getLocationDashboardData } from '../../../api/location/locationdashboardapi';
import dayjs from 'dayjs';
import { IwtEnv } from '../../../api/apiClient';
import { getFeatures } from '../../../api/users/userapis';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import Loader from '../common/Loader';
import { UserMenu } from '../common/UserMenu';

declare var iwtEnv: IwtEnv;
const LocDashboard = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['user_by_id'],
    queryFn: () => getUserById(Number(decodeJWT()?.userId)),
    refetchOnWindowFocus: false,
  });

  const decoded = decodeJWT();

  const { data: dashboardData, isLoading: dashboardLoading } = useQuery({
    queryKey: ['locationDashboardData'],
    queryFn: () => getLocationDashboardData(decoded?.mineid),
    refetchInterval: Number(iwtEnv?.timeIntervalForApi) * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  // Parse input datetime string
  const datetime = dayjs(dashboardData?.lastUpdatedTs);

  // Format the date
  const formattedDate = ` Today ` + datetime.format('[@] hh:mm a');

  useEffect(() => {
    if (localStorage.getItem('refreshingPage')) {
      localStorage.removeItem('refreshingPage');
      setTimeout(() => {
        toast.info(
          `Your Role has been updated to ${
            decodeJWT()?.role.charAt(0).toUpperCase() +
            decodeJWT()?.role.slice(1)
          }`
        );
      }, 1000);
    }
  });

  if (
    features?.data.some((feature: any) => feature.FeatureName == 'Location') ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <>
        <div className="sticky w-full top-0 z-30  agBreakup2  bg-top px-10 2xl:px-16">
          <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe]  pb-1 pt-4 ">
            <div className="">
              <h6 className="font-bold text-white text-[32px]">Dashboard</h6>
            </div>

            <div className="">
              <div className="flex justify-end items-center -mr-10 relative">
                <h6 className="font-bold text-white text-[32px]">Locations</h6>
                <span className="ml-4">
                  <UserMenu />
                </span>
              </div>
              <div className="mb-2 text-right">
                <span className="text-[#ffb132] text-[12px]">
                  Last Updated:{' '}
                </span>
                <span className="text-white text-[12px] mr-2">
                  {formattedDate}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-column justify-start agBreakup xl:px-10 2xl:px-16">
          <div className="mt-10  w-1/3">
            <div className="flex items-center space-x-2">
              <SunIcon />
              <span className="text-[#ffb132] text-[14px]">Above Ground</span>
            </div>
          </div>
          <div className="w-1/3 h-48 font-[600] flex justify-center items-center text-white text-[88px]">
            {dashboardData?.totalAG ? dashboardData?.totalAG : 0}
          </div>
          <div className="w-1/3"></div>
        </div>
        <div className="xl:px-10 2xl:px-16">
          <div className="flex flex-column justify-start">
            <div className="mt-10  w-1/3">
              <div className="flex items-center space-x-2">
                <AxeIcon />
                <span className="text-[#ffb132] text-[14px]">Below Ground</span>
              </div>
            </div>
            <div className="w-1/3 h-48 font-[600] flex justify-center items-center text-white text-[88px]">
              {dashboardData?.totalUG ? dashboardData?.totalUG : 0}
            </div>
            {dashboardData?.totalOffline &&
            dashboardData?.totalOffline !== 0 ? (
              <div className="w-1/3 flex justify-end mt-10 mr-3 h-20">
                <div className="text-base py-4 px-2 border border-slate-500 rounded-md text-white h-full flex justify-start">
                  <div className="leading-[43.57px]">
                    <WifiIcon />
                  </div>
                  <div className="flex-col mr-2">
                    <div className="font-[700] text-[36px] pl-4">
                      {dashboardData?.totalOffline
                        ? dashboardData?.totalOffline
                        : 0}
                    </div>{' '}
                    <div className="text-[12px] font-[400] mt-2">OFFLINE</div>
                  </div>
                </div>
              </div>
            ) : (
              ''
            )}
          </div>
          <div className="grid grid-cols-6 gap-2 w-[97%] m-auto">
            {dashboardLoading ? (
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            ) : (
              dashboardData?.ugBreakup &&
              dashboardData?.ugBreakup?.map((section: any) => (
                <Card
                  sectionName={section.sectionName}
                  numOfMiners={section?.sectionBreakup?.totalNodes}
                  instruments={section?.instruments ?? []}
                />
              ))
            )}
          </div>
        </div>
      </>
    );
  } else {
    return '';
  }
};

export default LocDashboard;
