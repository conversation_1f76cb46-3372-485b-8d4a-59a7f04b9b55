import FormGroup from './FormGroup';
import { FormInput, Group, Item } from '../types';

interface Props {
	groups: Group[];
	items: Item[];
	saveInputValue: (input: FormInput) => void;
	readOnly: boolean;
}

const Form = ({
	groups, items, saveInputValue, readOnly
}: Props) => {

	return (
		<div className="form-area flex flex-row flex-wrap h-fit w-full overflow-x-auto overflow-y-hidden p-4">
				{groups.map((grp:Group, index:number) =>
					<FormGroup
						key={index} group={grp}
						items={items.filter(item => item.groupId === grp.id)}
						questions={grp.questions}
						saveInputValue={saveInputValue}
						readOnly={readOnly}
					/>
				)}
		</div>
	);
};

export default Form;