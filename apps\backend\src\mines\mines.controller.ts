import { Controller, Query, Req, UseGuards } from '@nestjs/common';
import { MinesService } from './mines.service';
import {
  Get,
  ParseIntPipe,
  Post,
  Body,
  Param,
  NotFoundException,
  Patch,
  Delete,
} from '@nestjs/common';
import { CreateMineDto } from './dtos/create-mine.dto';
import { Mine } from './entities/mine.entity';
import { UpdateMineDto } from './dtos/update-mine.dto';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { ApiTags } from '@nestjs/swagger';
import { Roles } from '../auth/gurads/roles.decorator';

@Controller('portal/v1/mines')
@ApiTags('mines')
export class MinesController {
  constructor(private mineService: MinesService) {}
  @Get('/:id')
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard)
  async getMine(@Param('id') id: string) {
    const mine = await this.mineService.getMine(parseInt(id));
    if (!mine) throw new NotFoundException('Mine with given id not found');
    return mine;
  }

  @Get()
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard)
  findAll() {
    return this.mineService.findAll();
  }

  @Get('/company/:id')
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard)
  findAllByCompanyId(@Query('companyId') id: string) {
    return this.mineService.findAllByCompanyId(parseInt(id));
  }

  @Post('/checkminename')
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard)
  async checkMineName(@Body() mineBody: any) {
    const mines = await this.mineService.checkMineName(mineBody);
    return mines;
  }

  @Post()
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard)
  createMine(@Body() body: CreateMineDto, @Req() req) {
    return this.mineService.create(body, req.user);
  }

  @Patch('status/:id')
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard)
  async updateMineStatus(@Param('id', ParseIntPipe) id: number, @Req() req) {
    const mine = new Mine();
    let updatedUser = await this.mineService.updateMineStatus(id, req.user);
    return updatedUser;
  }

  @Patch('/isDelete/:id')
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard)
  async deleteMine(@Param('id') id: number, @Req() req) {
    let deletedMine = await this.mineService.deleteMine(id, req.user);
    return deletedMine;
  }

  @Patch('/:id')
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard)
  updateMine(@Param('id') id: string, @Body() body: UpdateMineDto, @Req() req) {
    return this.mineService.update(parseInt(id), body, req.user);
  }
}
