import { ApiClient } from '../apiClient';

export interface WatchlistData {
  id?: number;
  userId?: number;
  minerId: number;
}

export const addUserWatchlist = async (data: WatchlistData) => {
  return await ApiClient.post(`/api/portal/v1/watchlistitems`, data);
};

export const getUserWatchlist = async (mineId: Number, userId: Number) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/locations/live/watchlist/${mineId}/${userId}`
  );
};

export const deleteUserWatchlistItem = async (id: number) => {
  return await ApiClient.delete(`/api/portal/v1/watchlistitems/${id}`);
};

export const clearUserWatchlist = async () => {
  return await ApiClient.post(`/api/portal/v1/watchlistitems/clear-watchlist`);
};

export const getMineAllMiners = async () => {
  return await ApiClient.get(`/api/portal/v1/miners`);
};

export const deleteSelectedUserWatchlist = async (data: string[]) => {
  return await ApiClient.post(
    `/api/portal/v1/watchlistitems/delete-items`,
    data
  );
};
