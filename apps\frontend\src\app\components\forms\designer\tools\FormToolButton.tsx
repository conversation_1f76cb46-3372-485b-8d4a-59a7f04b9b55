interface ToolProps {
  label?: string;
  title?: string;
  icon?: any;
  overlay?: boolean;
  isActive?: boolean;
  handleClick?: any;
}

const FormToolButton = ({ label, title, icon, isActive, handleClick }: ToolProps) => {
	return (
		<div title={title} className={`flex flex-col justify-center text-white ${isActive ? 'cursor-pointer opacity-70 hover:opacity-100' : 'opacity-30'}`}
			onClick={() => isActive && handleClick()}
		>
			<div className={`text-center w-[38px] px-1 mx-auto`}>
				{icon ? icon : ''}
			</div>
			{label && 
				<div className="text-xs mb-2 text-center w-[38px] mx-auto">
					<label className={`text-center`}>{label ? label : ''}</label>
				</div>
			}
		</div>
	);
};

export default FormToolButton;
