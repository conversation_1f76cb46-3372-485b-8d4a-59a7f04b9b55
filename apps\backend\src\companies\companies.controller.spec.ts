import { Test, TestingModule } from '@nestjs/testing';
import { CompaniesController } from './companies.controller';
import { CompaniesService } from './companies.service';

describe('CompaniesController', () => {
  let controller: CompaniesController;

  const mockCompaniesService = {
    create: jest.fn((dto) => {
      return {
        id: 1,
        ...dto,
      };
    }),

    update: jest.fn().mockImplementation((id, dto) => {
      return {
        id,
        ...dto,
      };
    }),

    delete: jest.fn().mockImplementation((id) => {
      return {
        id: Date.now(),
        name: 'Southern Copper Corporation',
        code: 'SCCO',
        createdBy: 1,
        createdAt: Date,
        updatedBy: 1,
        updatedAt: Date,
      };
    }),

    find: jest.fn().mockImplementation(() => {
      return [
        {
          id: Date.now(),
          name: 'Southern Copper Corporation',
          code: 'SCCO',
          createdBy: 1,
          createdAt: Date,
          updatedBy: 1,
          updatedAt: Date,
        },
      ];
    }),

    findOne: jest.fn().mockImplementation(() => {
      return {
        id: Date.now(),
        name: 'Southern Copper Corporation',
        code: 'SCCO',
        createdBy: 1,
        createdAt: Date,
        updatedBy: 1,
        updatedAt: Date,
      };
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CompaniesController],
      providers: [CompaniesService],
    })
      .overrideProvider(CompaniesService)
      .useValue(mockCompaniesService)
      .compile();

    controller = module.get<CompaniesController>(CompaniesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a company', async () => {
    const dto = {
      name: 'Southern Copper Corporation',
      code: 'SCCO',
      createdBy: 1,
    };
    expect(
      await controller.create({
        name: dto.name,
        code: dto.code,
        createdBy: dto.createdBy,
      })
    ).toEqual({
      id: expect.any(Number),
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
      updatedBy: expect.any(null),
      ...dto,
    });
    expect(mockCompaniesService.create).toHaveBeenCalledWith(dto);
  });

  it('should update a company', () => {
    const dto = {
      name: 'Southern Copper Corporation',
      code: 'SCCO',
      updatedBy: 1,
    };
    expect(controller.update(1, dto)).toEqual({
      id: 1,
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
      createdBy: expect.any(Number),
      ...dto,
    });
    expect(mockCompaniesService.update).toHaveBeenCalledWith(dto);
  });

  it('should delete a company', () => {
    const dto = {
      name: 'Southern Copper Corporation',
      code: 'SCCO',
      createdBy: 1,
      createdAt: Date,
      updatedBy: 1,
      updatedAt: Date,
    };
    expect(controller.delete(1)).toEqual({
      id: 1,
      ...dto,
    });
    expect(mockCompaniesService.delete).toHaveBeenCalledWith(dto);
  });

  it('should get all companies', () => {
    const res = [
      {
        id: 1,
        name: 'Southern Copper Corporation',
        code: 'SCCO',
        createdBy: 1,
        createdAt: Date,
        updatedBy: 1,
        updatedAt: Date,
      },
    ];

    expect(controller.find()).toEqual([...res]);
    expect(mockCompaniesService.find).toHaveBeenCalledWith(res);
  });

  it('should get one company', () => {
    const res = {
      id: 1,
      name: 'Southern Copper Corporation',
      code: 'SCCO',
      createdBy: 1,
      createdAt: Date,
      updatedBy: 1,
      updatedAt: Date,
    };

    expect(controller.findOne(1)).toEqual({ ...res });
    expect(mockCompaniesService.findOne).toHaveBeenCalledWith(res);
  });
});
