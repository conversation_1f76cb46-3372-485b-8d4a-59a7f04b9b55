import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Company } from './entities/company.entity';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { MinesService } from '../mines/mines.service';
import { RolesService } from '../roles/roles.service';
import { RoleFeatureAccessService } from '../role_feature_access/role_feature_access.service';
import { FeaturesService } from '../features/features.service';

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(Company) private companyRepo: Repository<Company>,
    private mineService: MinesService,
    private roleService: RolesService,
    private featureService: FeaturesService,
    private roleFeatureAccessService: RoleFeatureAccessService
  ) {}

  async checkCompanyName(companyDto: any) {
    let name = await this.companyRepo.findOne({
      where: { name: companyDto?.name },
    });

    let code = await this.companyRepo.findOne({
      where: { code: companyDto?.code },
    });

    if (name) {
      return { ...name, duplicate: 'name' };
    } else if (code) {
      return { ...code, duplicate: 'code' };
    }
  }

  async create(companyDto: CreateCompanyDto): Promise<Company> {
    let name = await this.companyRepo.findOne({
      where: { name: companyDto?.name },
    });

    if (name) {
      throw new NotFoundException('Company Name Already Exist!');
    }
    let code = await this.companyRepo.findOne({
      where: { code: companyDto?.code },
    });
    if (code) {
      throw new NotFoundException('Company Code Already Exist!');
    }

    try {
      const company = this.companyRepo.create(companyDto);
      const res = await this.companyRepo.save(company);

      let roles = ['admin', 'manager', 'user'];

      for (let role of roles) {
        let role_res = await this.roleService.create({
          name: role,
          companyId: res.id,
          createdBy: res.createdBy,
        });
        let features = await this.featureService.findAll();

        if (role === 'manager') {
          features = features?.filter(
            (feature) =>
              feature.name === 'Dashboard' ||
              feature.name === 'Production' ||
              feature.name == 'Goals' ||
              feature.name == 'Forms' ||
              feature.name == 'Reports'
          );
        } else if (role === 'user') {
          features = features?.filter(
            (feature) =>
              feature.name === 'Production' ||
              feature.name === 'Compliance' ||
              feature.name == 'Forms'
          );
        }
        for (let feature of features) {
          await this.roleFeatureAccessService.create({
            roleId: role_res.id,
            featureId: feature.id,
            createdBy: res.id,
          });
        }
      }

      return res;
    } catch (err) {}
  }

  async find(): Promise<Company[]> {
    return await this.companyRepo.find();
  }

  async getAllCompanyMines() {
    const allcompanyMines = await this.companyRepo
      .createQueryBuilder('companies')
      .select([
        'companies.name as CompanyName',
        'mine.name AS MineName',
        'mine.code AS MineId',
        "CONCAT(user.first_name,' ', user.last_name) AS AdminName",
        'companies.created_at AS CreatedAt',
        'mine.id AS userMineId',
      ])
      .innerJoin('mines', 'mine', 'mine.company_id = companies.id')
      .innerJoin('users_mines', 'userMine', 'mine.id = userMine.mine_id')
      .innerJoin(
        'users',
        'user',
        'user.id = userMine.user_id AND user.is_delete = :isDelete',
        { isDelete: false }
      )
      .innerJoin('users_roles', 'userRole', 'userRole.user_id = user.id')
      .innerJoin(
        'roles',
        'role',
        'role.id = userRole.role_id  AND role.name = :roleName',
        { roleName: 'admin' }
      )
      .addOrderBy('companies.created_at', 'DESC')
      .getRawMany();

    const companyMinesData = allcompanyMines.reduce((acc, curr) => {
      const existingItem = acc.find((item) => item.MineId === curr.MineId);

      if (existingItem) {
        existingItem.AdminName += `, ${curr.AdminName}`;
      } else {
        acc.push(curr);
      }

      return acc;
    }, []);
    return companyMinesData;
  }

  async findOne(id: number): Promise<Company> {
    return this.companyRepo.findOne({
      where: {
        id,
      },
      relations: ['mines'],
    });
  }

  async update(id: number, data: any, user?: any): Promise<Company> {
    let updatedMine: any = {};
    if (data?.mineName) {
      updatedMine.name = data?.mineName;
    }
    if (data?.location) {
      updatedMine.location = data?.location;
    }
    if (data?.mineCode) {
      updatedMine.code = data?.mineCode;
    }
    if (data?.mineCode) {
      updatedMine.code = data?.mineCode;
    }
    if (data?.timezone) {
      updatedMine.timezoneId = data?.timezoneId;
    }
    const mine = await this.mineService.update(id, updatedMine, user);

    if (!mine) {
      throw new NotFoundException('Mine Not Found!');
    }

    const companyId = mine?.companyId;
    const company: any = await this.companyRepo.findOneBy({ id: companyId });

    if (!company) {
      throw new NotFoundException('Company Not Found!');
    }
    let updatedCompany: any = {};
    if (data?.companyName) {
      updatedCompany.name = data?.companyName;
    }
    if (data?.companyName) {
      updatedCompany.code = data?.companyName;
    }

    Object.assign(company, updatedCompany);

    return this.companyRepo.save(company);
  }

  async delete(id: number): Promise<Company> {
    const company = await this.companyRepo.findOneBy({ id });

    if (!company) {
      throw new NotFoundException('Company Not Found!');
    }

    return this.companyRepo.remove(company);
  }
}
