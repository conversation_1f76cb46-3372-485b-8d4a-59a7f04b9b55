import { Module } from '@nestjs/common';
import { FormTemplatesController } from './form_templates.controller';
import { FormTemplatesService } from './form_templates.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FormTemplate } from './entities/form_template.entity';
import { FormTemplateDefinition } from '../form_template_definitions/entities/form_template_definitions.entity';
import { FormTemplateDefinitionsService } from '../form_template_definitions/form_template_definitions.service';
import { RolesModule } from '../roles/roles.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    RolesModule,
    UsersModule,
    TypeOrmModule.forFeature([FormTemplate, FormTemplateDefinition])
  ],
  controllers: [FormTemplatesController],
  providers: [FormTemplatesService, FormTemplateDefinitionsService],
  exports: [FormTemplatesService],
})
export class FormTemplatesModule {}
