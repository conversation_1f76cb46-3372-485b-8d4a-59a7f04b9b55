{"name": "@iwtportal/source", "version": "0.0.0", "license": "MIT", "scripts": {"test": "nx test backend --watchAll"}, "private": true, "dependencies": {"@heroicons/react": "^2.1.3", "@hookform/resolvers": "^3.3.4", "@material-tailwind/react": "^2.1.9", "@nestjs/class-transformer": "^0.4.0", "@nestjs/class-validator": "^0.13.4", "@nestjs/common": "^10.0.2", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.0.2", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.2", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.2.0", "@nestjs/typeorm": "^10.0.1", "@ntegral/nestjs-sendgrid": "^1.0.0", "@sendgrid/mail": "^8.1.1", "@tanstack/react-query": "^5.20.5", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "axios": "^1.6.0", "bcrypt": "^5.1.1", "chart.js": "^4.4.2", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-zoom": "^2.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "dayjs-plugin-utc": "^0.1.2", "flowbite-react": "^0.7.2", "framer-motion": "^11.0.6", "hammerjs": "^2.0.8", "http-proxy-middleware": "^3.0.0", "jwt-decode": "^4.0.0", "mixpanel-browser": "^2.64.0", "moment": "^2.30.1", "mssql": "^10.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "react-chartjs-2": "^5.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-hook-form": "^7.50.1", "react-icons": "^5.0.1", "react-plotly.js": "^2.6.0", "react-query": "^3.39.3", "react-responsive": "^9.0.2", "react-router-dom": "^6.22.0", "react-select": "^5.8.0", "react-signature-canvas": "^1.1.0-alpha.2", "react-tailwindcss-datepicker": "^1.6.6", "react-toastify": "^10.0.4", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "tslib": "^2.3.0", "typeorm": "^0.3.20", "xlsx": "^0.18.5", "yup": "^1.3.3"}, "devDependencies": {"@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/cypress": "18.0.2", "@nx/eslint": "18.0.2", "@nx/eslint-plugin": "18.0.2", "@nx/jest": "18.0.2", "@nx/js": "18.0.2", "@nx/nest": "^18.0.2", "@nx/node": "18.0.2", "@nx/react": "18.0.2", "@nx/vite": "18.0.2", "@nx/web": "18.0.2", "@nx/webpack": "18.0.2", "@nx/workspace": "18.0.2", "@swc-node/register": "~1.6.7", "@swc/cli": "~0.1.62", "@swc/core": "~1.3.85", "@swc/helpers": "~0.5.2", "@testing-library/react": "14.0.0", "@types/jest": "^29.4.0", "@types/multer": "^1.4.13", "@types/mixpanel-browser": "^2.60.0", "@types/node": "18.16.9", "@types/react": "18.2.33", "@types/react-dom": "18.2.14", "@types/react-plotly.js": "^2.6.3", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "10.4.13", "cypress": "^13.0.0", "eslint": "~8.48.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-cypress": "^2.13.4", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "jest": "^29.4.1", "jest-environment-node": "^29.4.1", "jsdom": "~22.1.0", "nx": "18.0.2", "postcss": "8.4.21", "prettier": "^2.6.2", "sonarqube-scanner": "^3.3.0", "tailwindcss": "3.2.7", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "~5.3.2", "vite": "^5.0.0", "vitest": "^1.0.4", "webpack-cli": "^5.1.4"}, "optionalDependencies": {"@nx/nx-linux-x64-gnu": "18.0.4"}}