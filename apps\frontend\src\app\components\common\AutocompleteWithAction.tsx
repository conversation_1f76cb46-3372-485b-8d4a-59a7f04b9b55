import { useParams } from 'react-router-dom';
import { SearchIcon } from '../../../assets/icons/icons';
import React, { useState, ChangeEvent, MouseEvent, useEffect } from 'react';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import decodeJWT from '../../../utils/jwtdecoder';

const AutocompleteWithButton = (props: {
  setSerachText: any;
  miners?: any;
  isAutoComplete?: boolean;
  isWithButton?: boolean;
  watchAction?: any;
  optionClick?: any;
}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const [options, setOptions] = useState<any[]>([]);
  let buttonClickOption: any | null = null;
  const params = useParams();
  const data = props?.miners;
  const decoded = decodeJWT();

  const url = getPageNamesFromUrl(params['*']);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    const filteredOptions = data?.filter((option: any) => {
      return option.minerName?.toLowerCase()?.includes(value?.toLowerCase());
    });
    if (value !== '') {
      setOptions(filteredOptions);
    } else {
      setOptions([]);
    }
    props.setSerachText(value);
    buttonClickOption = null;

    mixpanel.track('Personnel Search', {
      Page_Name: url,
      MshaId: decoded?.mshaId,
    });
  };

  const handleOptionClick = (option: any) => {
    setOptions([]);
    setInputValue(option.minerName);
    props.setSerachText(option.minerName);
    props.optionClick(option);
    mixpanel.track('Personnel Click', {
      Page_Name: url
    })
  };

  const handleActionButtonClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (buttonClickOption) {
      props.watchAction(buttonClickOption);
      setOptions([]);
    }
  };
  const menuRef = React.useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOptions([]);
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);

    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [setOptions]);

  useEffect(() => {
    setInputValue('');
  }, [params['*']]);

  return (
    <div className="relative pt-[9px]" ref={menuRef}>
      <div
        id="searchInput"
        className="flex items-center h-12 w-72 text-base p-4 border border-[#4AA8FE] justify-start rounded-md text-white"
      >
        <SearchIcon />{' '}
        <input
          type="text"
          value={inputValue}
          placeholder="Search for personnel"
          className="ml-2 bg-transparent border-none outline-none focus:border-none  text-white"
          onChange={handleInputChange}
          autoFocus
        ></input>
      </div>
      {props.isAutoComplete ? (
        <ul className="w-72 absolute z-10 mt-1 bg-[#253543] text-white r-1 shadow-md rounded-md max-h-[200px] overflow-y-auto">
          {options?.map((option) => (
            <li
              key={option.MinerId}
              onClick={() => handleOptionClick(option)}
              className="px-4 py-2 cursor-pointer hover:bg-[#253543]-100"
            >
              {option.minerName}
              {props.isWithButton ? (
                !option.IsWatched ? (
                  <button
                    className="ml-2 bg-[#253543]-500 text-[#4AA8FE] rounded float-right"
                    onClick={(e) => {
                      e.stopPropagation();
                      buttonClickOption = option;
                      handleActionButtonClick(e);
                      if (!option.IsWatched) {
                        mixpanel.track('Watchlist Add', {
                          Page_Name: url,
                          MshaId: decoded?.mshaId,
                        });
                      }
                    }}
                  >
                    WATCH
                  </button>
                ) : (
                  <label className=" float-right text-[#676767]">WATCH</label>
                )
              ) : (
                ''
              )}
            </li>
          ))}
        </ul>
      ) : (
        <></>
      )}
    </div>
  );
};

export default AutocompleteWithButton;
