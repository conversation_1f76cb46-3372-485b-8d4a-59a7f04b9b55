import {
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
export class CreateProductionGoalDto {
  @IsNumber()
  @ApiProperty()
  sectionId: number;

  @IsNumber()
  @ApiProperty()
  shiftId: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  goal: number;

  @ApiProperty()
  effectiveDate: string;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
