import BarChart from '../common/BarChart';
import ProgressBar from '../common/ProgressBar';
import { ProdDownArrow } from '../../../assets/icons/icons';
import { ProdUpArrow } from '../../../assets/icons/icons';
import MultiLineGraph from '../common/MultiLineGraph';
import { useParams } from 'react-router-dom';
import FillAreaGraph from '../common/FillAreaGraph';
import Plot from 'react-plotly.js';
import { useQuery } from '@tanstack/react-query';
import { useOutletContext } from 'react-router';
import decodeJWT from '../../../utils/jwtdecoder';
import { getLiveMineData } from '../../../api/production/productionReportapi';
import { IwtEnv } from '../../../api/apiClient';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import ProdLiveSectionGraph from '../common/ProdLiveSectionGraph';
import Loader from '../common/Loader';
import GraphSkeleton from '../common/GraphSkeleton';

declare var iwtEnv: IwtEnv;

export default function ProdLiveMine() {
  const params = useParams();

  const [
    searchText,
    setLastUpdatedDate,
    filterData,
    setlastEformSubmitted,
    setTimzezone,
  ] = useOutletContext() as [string, any, any, any, any];

  const liveMineQuery = useQuery({
    queryKey: ['UpdatedLiveMineData'],
    queryFn: () => getLiveMineData(Number(decodeJWT()?.mineid)),
    refetchInterval: Number(iwtEnv?.timeIntervalForProd) * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  const {
    data: liveMineData,
    refetch: refetchLiveMineData,
    status: apiStatus,
  } = liveMineQuery;

  const prodReportMineData = {
    activity: {
      lastReport: '6:07pm',
      sectionEquipment: {
        prefixSign: '+',
        feets: 12,
        feetsUnit: 'ft',
        onTimeColor: 'green',
        goal: 800,
      },
      beltAvailability: {
        prefixSign: '-',
        feets: 22,
        feetsUnit: 'ft',
        onTimeColor: 'green',
        goal: 800,
      },
    },
  };

  useEffect(() => {
    setLastUpdatedDate(liveMineData?.data?.lastUpdatedTs);
    setlastEformSubmitted(liveMineData?.data?.lastSavedOrSubmittedTs);
    setTimzezone(liveMineData?.data?.tzAbbreviation);
  }, [
    liveMineData?.data?.lastUpdatedTs,
    liveMineData?.data?.lastSavedOrSubmittedTs,
    liveMineData?.data?.tzAbbreviation,
  ]);

  const getHighestSummedShiftData = (graphData: any) => {
    const summedValuesBySection: { [key: number]: number } = {};

    graphData?.coordinates?.forEach((coordinate: any) => {
      if (summedValuesBySection[coordinate.sectionID]) {
        summedValuesBySection[coordinate.sectionID] += coordinate.totalMined;
      } else {
        summedValuesBySection[coordinate.sectionID] = coordinate.totalMined;
      }
    });

    const highestValue = Math.max(...Object.values(summedValuesBySection));

    return highestValue;
  };

  const highestSummedShiftValue = getHighestSummedShiftData(
    liveMineData?.data?.sectionwiseGraphData
  );
  let sectionNames = liveMineData?.data?.sectionwiseGraphData?.xAxisPoints;

  const shiftNames: any = liveMineData?.data?.shifts;
  const colorArray = [
    'rgba(255, 194, 94, 1)',
    'rgba(38, 31, 82, 1)',
    'rgba(254, 74, 106, 1)',
    'rgba(74, 168, 254, 1)',
  ];

  const repeatCount = shiftNames?.length;

  const repeatedColorArray = Array.from(
    { length: repeatCount },
    (_, i) => colorArray[i % colorArray.length]
  );

  const shiftTrace = shiftNames?.map((shift: any, index: number) => {
    return {
      x: sectionNames,
      y: getShiftDataById(shift.id),
      type: 'bar',
      marker: {
        color: repeatedColorArray[index],
      },
      width: [0.2, 0.2, 0.2, 0.2, 0.2, 0.2],
      barcornerradius: 10,
      name: shift.shiftName,
      text: '',
      textposition: 'outside',
      textfont: { color: 'white' },
      hoverinfo: 'text',
      hovertext: getShiftDataHandoverText(shift),
      showlegend: shift.shiftName == 'OWL_Shift' ? false : true,
    };
  });

  function getShiftDataById(shiftId: number) {
    return liveMineData?.data?.sectionwiseGraphData?.coordinates?.map(
      (section: any) => {
        const shiftData = section.shiftData.find((d: any) => d.id === shiftId);
        return shiftData ? shiftData.value : 0;
      }
    );
  }

  function getShiftDataHandoverText(shift: any) {
    return liveMineData?.data?.sectionwiseGraphData?.coordinates?.map(
      (section: any) => {
        const shiftData = section.shiftData.find((d: any) => d.id === shift.id);
        return shift.shiftName + ' : ' + (shiftData ? shiftData.value : 0);
      }
    );
  }

  const liveMineHeading = [
    { heading: 'Mined' },
    { heading: 'Feet Mined/Hour' },
    { heading: 'Belt Availability' },
    { heading: 'Total Downtime' },
    // { heading: 'Last Downtime Started' },
    { heading: 'Total Alerts' },
  ];

  function barGap() {
    const screenWidth = window.innerWidth;
    if (screenWidth > 1536) {
      return 0.7;
    } else if (screenWidth > 1280) {
      return 0.5;
    }
  }

  function marginRight() {
    const screenWidth = window.innerWidth;
    if (screenWidth > 1536) {
      return 50;
    } else if (screenWidth > 1280) {
      return 55;
    }
  }

  const barGraphLayout = {
    barmode: 'stack',
    barcornerradius: 20,
    bargap: 0.1,
    bargroupgap: barGap(),
    xaxis: {
      title: 'Working Section',
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
    },
    yaxis: {
      title: {
        text: 'Feet Mined',
        standoff: 20,
      },
      side: 'right',
      type: 'linear',
      range: [-1, highestSummedShiftValue + 100],
      color: 'white',
      showgrid: false,
    },
    showlegend: true,
    margin: { t: 30, l: 30, b: 72, r: marginRight() },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
    dragmode: false,
  };

  const barGraphData = shiftTrace;

  const lastReport = liveMineData?.data?.activityMetrics?.lastReport
    ? dayjs(liveMineData?.data?.activityMetrics?.lastReport).format('hh:mm a')
    : null;

  function calculateAchieveGoal(averageGoal: any, achievedGoal: any) {
    if (
      (achievedGoal >= 10 && achievedGoal < 100) ||
      (achievedGoal <= -10 && achievedGoal > -100)
    ) {
      return achievedGoal;
    } else {
      let percentageGoal = (achievedGoal / averageGoal) * 100;
      return percentageGoal;
    }
  }

  let inspectionPercentage = calculateAchieveGoal(
    liveMineData?.data?.averageGoal,
    liveMineData?.data?.activityMetrics?.inspectionDowntime?.feets
  );

  let plannedPercentage = calculateAchieveGoal(
    liveMineData?.data?.averageGoal,
    liveMineData?.data?.activityMetrics?.plannedDowntime?.feets
  );

  let unplannedPercentage = calculateAchieveGoal(
    liveMineData?.data?.averageGoal,
    liveMineData?.data?.activityMetrics?.unplannedDowntime?.feets
  );

  let ontimePercentage = calculateAchieveGoal(
    liveMineData?.data?.averageGoal,
    liveMineData?.data?.activityMetrics?.onTime?.feets
  );

  return (
    <div>
      <div className="grid grid-cols-5 gap-4 pt-6 w-full h-full px-[4%] mx-auto bg-[#2c5c8bf6] pb-6">
        {liveMineHeading.map((heading, index) => (
          <div key={index}
            className={`h-[100px] border-[#4AA8FE] rounded-lg border-[1px] ${
              heading?.heading == 'Belt Availability' ||
              heading?.heading == 'Total Alerts'
                ? 'border-dashed  border-[1px] opacity-40'
                : 'border-[1px]'
            } `}
          >
            <div
              className={`${
                heading?.heading == 'Belt Availability' ||
                heading?.heading == 'Total Alerts'
                  ? // ? 'blur-sm'
                    'opacity-40'
                  : ''
              }`}
              style={{ overflow: 'hidden' }}
            >
              <div className="text-center text-[14px] text-[#FFD084] font-normal pt-3 overflow-hidden">
                {heading.heading}
                {index === 0 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {liveMineData?.data?.mined
                      ? liveMineData?.data?.mined
                      : '0'}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'ft'}
                    </span>
                  </div>
                )}
                {index === 1 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {liveMineData?.data?.feetMinedPerHour
                      ? liveMineData?.data?.feetMinedPerHour
                      : '0'}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'fmph'}
                    </span>
                  </div>
                )}
                {index === 2 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3 ">
                    {0.0}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'%'}
                    </span>
                  </div>
                )}
                {index === 3 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {liveMineData?.data?.totalDowntime
                      ? liveMineData?.data?.totalDowntime
                      : '0'}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'mins'}
                    </span>
                  </div>
                )}
                {index === 4 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {!liveMineData?.data?.lastDowntimeStarted ||
                    liveMineData?.data?.lastDowntimeStarted === null
                      ? '-'
                      : liveMineData?.data?.lastDowntimeStarted}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {liveMineData?.data?.lastDowntimeStartedPostfix}
                    </span>
                  </div>
                )}
                {index === 5 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {0}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'%'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex px-[4%] w-full pt-4">
        <div className="w-[50%]  !h-96">
          <div className="w-[100%]  overflow-hidden">
            <ProdLiveSectionGraph
              data={liveMineData?.data?.cutsGraphData}
              shifts={liveMineData?.data?.shifts}
              mine={'mine'}
              apiStatus={apiStatus}
            />
          </div>
        </div>
        <div className="w-1/2">
          <div className="!w-[95%]  h-96 m-auto">
            <div className="">
              {Object?.keys(
                apiStatus == 'success'
                  ? liveMineData?.data?.sectionwiseGraphData
                  : {}
              )?.length === 0 ||
              liveMineData?.data?.sectionwiseGraphData?.coordinates?.length ==
                0 ? (
                <GraphSkeleton
                  className="2xl:!h-[35.5vh] xl:!h-[50vh] mt-10  "
                  apiStatus={apiStatus}
                />
              ) : (
                <Plot
                  id="barGraph1"
                  // style={{ width: '100%' }}
                  className="h-80 !w-[96%]"
                  data={barGraphData}
                  // layout={barGraphLayout}
                  layout={{
                    ...barGraphLayout,
                    legend: {
                      orientation: 'h',
                      x: 0.5,
                      xanchor: 'center',
                      y: 1.15,
                      font: { color: 'white' },
                    },
                  }}
                  config={{
                    displayModeBar: false,
                    scrollZoom: false,
                    responsive: true,
                    dragmode: false,
                    modeBarButtonsToRemove: ['resetScale2d'],
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="px-[4%] pt-0 flex">
        <div className=" w-1/2  h-[260px] activityColor rounded-md ">
          <div className="flex">
            <div className="font-bold text-2xl text-white pt-4 pl-4 w-1/2">
              Activity
            </div>
            <div className="text-end font-normal text-sm text-white pt-6 xl:pr-2 2xl:pr-4 w-1/2">
              Last Report:{' '}
              {lastReport === null
                ? '-'
                : ` ${lastReport} ${
                    liveMineData?.data?.tzAbbreviation
                      ? liveMineData?.data?.tzAbbreviation
                      : ''
                  }`}
            </div>
          </div>
          <div className="flex pt-2 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Inspection
            </div>
            <div className=" flex w-1/2 justify-end ">
              <div
                className={`${
                  !liveMineData?.data?.activityMetrics?.inspectionDowntime
                    ?.feets ||
                  liveMineData?.data?.activityMetrics?.inspectionDowntime
                    ?.feets === 0
                    ? 'flex items-center 2xl:pl-4 xl:pl-3'
                    : 'flex items-center'
                }`}
              >
                <div
                  className={`font-normal text-xs pt-1 text-right w-[65px] ${
                    liveMineData?.data?.activityMetrics?.inspectionDowntime
                      ?.feets < 0
                      ? 'text-[#96FB60]'
                      : !liveMineData?.data?.activityMetrics?.inspectionDowntime
                          ?.feets ||
                        liveMineData?.data?.activityMetrics?.inspectionDowntime
                          ?.feets === 0
                      ? 'text-white pr-2'
                      : 'text-[#FE4A6A]'
                  }`}
                >
                  {liveMineData?.data?.activityMetrics?.inspectionDowntime
                    ?.feets > 0
                    ? '-' +
                      liveMineData?.data?.activityMetrics?.inspectionDowntime
                        ?.feets +
                      ' ' +
                      'ft'
                    : liveMineData?.data?.activityMetrics?.inspectionDowntime
                        ?.feets === 0 ||
                      !liveMineData?.data?.activityMetrics?.inspectionDowntime
                        ?.feets
                    ? '0 ft'
                    : liveMineData?.data?.activityMetrics?.inspectionDowntime
                        ?.feets +
                      ' ' +
                      ' ft'}
                </div>
                <div className="pt-2 pl-2 pr-2">
                  {liveMineData?.data?.activityMetrics?.inspectionDowntime
                    ?.feets > 0 ? (
                    <ProdDownArrow />
                  ) : !liveMineData?.data?.activityMetrics?.inspectionDowntime
                      ?.feets ||
                    liveMineData?.data?.activityMetrics?.inspectionDowntime
                      ?.feets === 0 ? (
                    ''
                  ) : (
                    <ProdUpArrow />
                  )}
                </div>
                <div className="pt-2  pr-4 ">
                  <ProgressBar
                    value={
                      inspectionPercentage > 0
                        ? '-' + inspectionPercentage.toFixed(2)
                        : !liveMineData?.data?.activityMetrics
                            ?.inspectionDowntime?.feets ||
                          liveMineData?.data?.activityMetrics
                            ?.inspectionDowntime?.feets === 0
                        ? ''
                        : inspectionPercentage.toFixed(2)
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex pt-2 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Planned Downtime
            </div>
            <div className=" flex w-1/2 justify-end">
              <div
                className={`${
                  !liveMineData?.data?.activityMetrics?.plannedDowntime
                    ?.feets ||
                  liveMineData?.data?.activityMetrics?.plannedDowntime
                    ?.feets === 0
                    ? 'flex items-center pl-4'
                    : 'flex items-center'
                }`}
              >
                <div
                  className={`font-normal text-xs pt-1 text-right w-[65px] ${
                    liveMineData?.data?.activityMetrics?.plannedDowntime
                      ?.feets < 0
                      ? 'text-[#96FB60]'
                      : !liveMineData?.data?.activityMetrics?.plannedDowntime
                          ?.feets ||
                        liveMineData?.data?.activityMetrics?.plannedDowntime
                          ?.feets === 0
                      ? 'text-white pr-2'
                      : 'text-[#FE4A6A]'
                  }`}
                  // style={{ width: '100px', textAlign: 'right' }}
                >
                  {liveMineData?.data?.activityMetrics?.plannedDowntime?.feets >
                  0
                    ? '-' +
                      liveMineData?.data?.activityMetrics?.plannedDowntime
                        ?.feets +
                      ' ' +
                      'ft'
                    : liveMineData?.data?.activityMetrics?.plannedDowntime
                        ?.feets === 0 ||
                      !liveMineData?.data?.activityMetrics?.plannedDowntime
                        ?.feets
                    ? '0 ft'
                    : liveMineData?.data?.activityMetrics?.plannedDowntime
                        ?.feets + ' ft'}
                </div>
                <div className="pt-2 pl-2 pr-2">
                  {liveMineData?.data?.activityMetrics?.plannedDowntime?.feets >
                  0 ? (
                    <ProdDownArrow />
                  ) : !liveMineData?.data?.activityMetrics?.plannedDowntime
                      ?.feets ||
                    liveMineData?.data?.activityMetrics?.plannedDowntime
                      ?.feets === 0 ? (
                    ''
                  ) : (
                    <ProdUpArrow />
                  )}
                </div>
                <div className="pt-2 pr-4">
                  <ProgressBar
                    value={
                      plannedPercentage > 0
                        ? '-' + plannedPercentage.toFixed(2)
                        : !liveMineData?.data?.activityMetrics?.plannedDowntime
                            ?.feets ||
                          liveMineData?.data?.activityMetrics?.plannedDowntime
                            ?.feets === 0
                        ? ''
                        : plannedPercentage.toFixed()
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex pt-2 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Unplanned Downtime
            </div>
            <div className=" flex w-1/2 justify-end">
              <div
                className={`${
                  !liveMineData?.data?.activityMetrics?.unplannedDowntime
                    ?.feets ||
                  liveMineData?.data?.activityMetrics?.unplannedDowntime
                    ?.feets === 0
                    ? 'flex items-center pl-4'
                    : 'flex items-center'
                }`}
              >
                <div
                  className={`font-normal text-xs pt-1 text-right w-[65px] ${
                    liveMineData?.data?.activityMetrics?.unplannedDowntime
                      ?.feets < 0
                      ? 'text-[#96FB60]'
                      : !liveMineData?.data?.activityMetrics?.unplannedDowntime
                          ?.feets
                      ? 'text-white pr-2'
                      : !liveMineData?.data?.activityMetrics?.unplannedDowntime
                          ?.feets ||
                        liveMineData?.data?.activityMetrics?.unplannedDowntime
                          ?.feets === 0
                      ? 'text-white pr-2'
                      : 'text-[#FE4A6A]'
                  }`}
                >
                  {liveMineData?.data?.activityMetrics?.unplannedDowntime
                    ?.feets > 0
                    ? '-' +
                      liveMineData?.data?.activityMetrics?.unplannedDowntime
                        ?.feets +
                      ' ' +
                      'ft'
                    : liveMineData?.data?.activityMetrics?.unplannedDowntime
                        ?.feets === 0 ||
                      !liveMineData?.data?.activityMetrics?.unplannedDowntime
                        ?.feets
                    ? '0 ft'
                    : liveMineData?.data?.activityMetrics?.unplannedDowntime
                        ?.feets + ' ft'}
                </div>
                <div className="pt-2 pl-2 pr-2">
                  {liveMineData?.data?.activityMetrics?.unplannedDowntime
                    ?.feets > 0 ? (
                    <ProdDownArrow />
                  ) : !liveMineData?.data?.activityMetrics?.unplannedDowntime
                      ?.feets ||
                    liveMineData?.data?.activityMetrics?.unplannedDowntime
                      ?.feets === 0 ? (
                    ''
                  ) : (
                    <ProdUpArrow />
                  )}
                </div>
                <div className="pt-2 pr-4">
                  <ProgressBar
                    value={
                      unplannedPercentage > 0
                        ? '-' + unplannedPercentage.toFixed(2)
                        : !liveMineData?.data?.activityMetrics
                            ?.unplannedDowntime?.feets ||
                          liveMineData?.data?.activityMetrics?.unplannedDowntime
                            ?.feets === 0
                        ? ''
                        : unplannedPercentage.toFixed()
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex pt-2 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              On Time
            </div>
            <div className=" flex w-1/2 justify-end">
              <div
                className={`${
                  !liveMineData?.data?.activityMetrics?.onTime?.feets ||
                  liveMineData?.data?.activityMetrics?.onTime?.feets === 0
                    ? 'flex items-center pl-4'
                    : 'flex items-center'
                }`}
              >
                <div
                  className={`font-normal text-xs pt-1 text-right w-[65px] ${
                    liveMineData?.data?.activityMetrics?.onTime?.feets > 0
                      ? 'text-[#96FB60]'
                      : !liveMineData?.data?.activityMetrics?.onTime?.feets ||
                        liveMineData?.data?.activityMetrics?.onTime?.feets === 0
                      ? 'text-white pr-2'
                      : 'text-[#FE4A6A]'
                  }`}
                >
                  {liveMineData?.data?.activityMetrics?.onTime?.feets > 0
                    ? '+' +
                      liveMineData?.data?.activityMetrics?.onTime?.feets +
                      ' ' +
                      'ft'
                    : liveMineData?.data?.activityMetrics?.onTime?.feets ===
                        0 || !liveMineData?.data?.activityMetrics?.onTime?.feets
                    ? '0 ft'
                    : +liveMineData?.data?.activityMetrics?.onTime?.feets +
                      ' ft'}
                </div>
                <div className="pt-2 pl-2 pr-2">
                  {liveMineData?.data?.activityMetrics?.onTime?.feets < 0 ? (
                    <ProdDownArrow />
                  ) : liveMineData?.data?.activityMetrics?.onTime?.feets ===
                      0 ||
                    !liveMineData?.data?.activityMetrics?.onTime?.feets ? (
                    ''
                  ) : (
                    <ProdUpArrow />
                  )}
                </div>
                <div className="pt-2 pr-4 ">
                  <ProgressBar
                    value={
                      ontimePercentage > 0
                        ? '+' + ontimePercentage.toFixed(2)
                        : liveMineData?.data?.activityMetrics?.onTime?.feets ===
                            0 ||
                          !liveMineData?.data?.activityMetrics?.onTime?.feets
                        ? ''
                        : ontimePercentage.toFixed()
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex pt-2 opacity-40 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Section Equipment
            </div>
            <div className=" flex w-1/2 justify-end">
              <div className="font-normal text-xs pt-1  text-[#96FB60] ">
                {prodReportMineData.activity.sectionEquipment.prefixSign +
                  prodReportMineData.activity.sectionEquipment.feets +
                  prodReportMineData.activity.sectionEquipment.feetsUnit}
              </div>
              <div className="pt-2 pl-2">
                {prodReportMineData.activity.sectionEquipment.prefixSign ===
                '-' ? (
                  <ProdDownArrow />
                ) : (
                  <ProdUpArrow />
                )}
              </div>
              <div className="pt-2 pr-4">
                <ProgressBar
                  value={
                    prodReportMineData.activity.sectionEquipment.prefixSign +
                    prodReportMineData.activity.sectionEquipment.feets
                  }
                />
              </div>
            </div>
          </div>
          <div className="flex pt-2 opacity-40 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Belt Availability
            </div>
            <div className=" flex w-1/2 justify-end">
              <div className="font-normal text-xs pt-1 xl:pl-6 2xl:pl-12 text-[#FE4A6A] ">
                {prodReportMineData.activity.beltAvailability.prefixSign +
                  prodReportMineData.activity.beltAvailability.feets +
                  prodReportMineData.activity.beltAvailability.feetsUnit}
              </div>
              <div className="pt-2 pl-2">
                {prodReportMineData.activity.beltAvailability.prefixSign ===
                '-' ? (
                  <ProdDownArrow />
                ) : (
                  <ProdUpArrow />
                )}
              </div>
              <div className="pt-2 pr-4">
                <ProgressBar
                  value={
                    prodReportMineData.activity.beltAvailability.prefixSign +
                    prodReportMineData.activity.beltAvailability.feets
                  }
                />
              </div>
            </div>
          </div>
        </div>
        <div className=" w-1/2 insightsColor h-[260px] xl:ml-8 2xl:ml-12 rounded-sm border-dashed border-[#4AA8FE] border-[1px] unselectable opacity-40">
          <div className="opacity-40">
            <div className="font-bold text-2xl text-white pt-4 pl-8">
              Insights
            </div>
            <div className="text-white text-sm px-4 py-2 pl-14">
              <ul className="list-disc">
                <li>
                  Inspections are taking longer than normal. Shorter inspection
                  windows would +32 feet.
                </li>
                <li>Section 2 started 18 minutes late. Production -12 feet.</li>
                <li>
                  Unplanned downtime is 20% higher so far today than normal
                  resulting in -52 feet of lost production.
                </li>
                <li>
                  Belt 7A was down for 32 minutes resulting in 22 lost feet.
                  Consider decreasing inspection intervals to ensure higher
                  up-time.
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
