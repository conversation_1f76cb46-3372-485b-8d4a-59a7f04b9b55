import {
  Body,
  Controller,
  Get,
  HttpException,
  Param,
  Post,
  Req,
  Request,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthPayloadDto, RefreshTokenDto } from './dto/auth.dto';
import { AuthService } from './auth.service';
import { ApiTags } from '@nestjs/swagger';
import { LocalGuard } from './gurads/local.guard';
import { JwtAuthGuard } from './gurads/jwt.guard';
import { RolesGuard } from './gurads/roles.guard';
import { Roles } from './gurads/roles.decorator';
import { Features } from './gurads/feature.decorator';
import { RefreshJwtAuthGuard } from './gurads/refresh-jwt.guard';

@Controller('portal/v1/auth')
@ApiTags('auth')
export class AuthController {
  constructor(private authservice: AuthService) {}

  @Post('login')
  @UseGuards(LocalGuard)
  login(@Body() authPayload: AuthPayloadDto) {
    return this.authservice.validateUser(
      authPayload.username,
      authPayload.password
    );
  }

  @Get('status')
  @Roles(['superuser'])
  @Features(['users'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  status(@Request() req) {
    //this.authservice.sendEmail(req.user);
    return req.user;
  }

  @Post('refresh-token')
  @UseGuards(RefreshJwtAuthGuard)
  refreshToken(@Request() req) {
    return this.authservice.refreshToken(req.user.userId);
  }

  @Post('refresh-token/:id')
  @UseGuards(RefreshJwtAuthGuard)
  refreshTokenForSuperUser(@Request() req, @Param('id') id: string) {
    return this.authservice.refreshToken(req.user.userId, parseInt(id));
  }
}
