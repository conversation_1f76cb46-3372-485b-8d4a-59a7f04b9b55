import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  addCompany,
  editCompany,
  CompanyData,
  checkCompanyName,
  getMine,
  deleteMine,
  statusChange,
} from '../../api/companies/companyapis';

export function useAddCompany() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CompanyData) => addCompany(data),
    onSettled: async (data: any, error: any) => {
      if (error) {
        return error.response?.data?.message;
      } else {
        await queryClient.invalidateQueries({ queryKey: ['add-company'] });
      }
    },
    onSuccess: function () {
      queryClient.invalidateQueries({ queryKey: ['companies'], exact: true });
    },
  });
}

export function useEditCompany() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CompanyData) => {
      const id: any = data?.id;
      delete data.id;
      return editCompany(id, data)
    },
    onSettled: async (data: any, error: any) => {
      if (error) {
        return error.response?.data?.message;
      } else {
        await queryClient.invalidateQueries({ queryKey: ['edit-company'] });
      }
    },
    onSuccess: function () {
      queryClient.invalidateQueries({ queryKey: ['companies'], exact: true });
    },
  });
}

export function useCheckCompanyName() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => checkCompanyName(data),
    onSettled: async (data: any, error: any) => {
      if (error) {
        return error.response?.data?.message;
      } else {
        await queryClient.invalidateQueries({ queryKey: ['get-company'] });
      }
    },
    onSuccess: function () {
      queryClient.invalidateQueries({
        queryKey: ['company'],
        exact: true,
      });
    },
  });
}

export function useGetMine() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => getMine(data),
    onSettled: async (data: any, error: any) => {
      if (error) {
        return error.response?.data?.message;
      } else {
        await queryClient.invalidateQueries({ queryKey: ['get-mine'] });
      }
    },
    onSuccess: function () {
      queryClient.invalidateQueries({
        queryKey: ['mine'],
        exact: true,
      });
    },
  });
}

export function useDeleteMine() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteMine(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['delete-mine'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['companies'], exact: true });
    },
  });
}

export function useStatusChange() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return statusChange(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['change-status'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['companies'], exact: true });
    },
  });
}
