export function addMonths(date: Date, months: number) {
  var month = (date.getMonth() + months) % 12;
  //create a new Date object that gets the last day of the desired month
  var last = new Date(date.getFullYear(), month + 1, 0);

  //compare dates and set appropriately
  if (date.getDate() <= last.getDate()) {
    date.setMonth(month);
  } else {
    date.setMonth(month, last.getDate());
  }

  return date;
}

export function getWeek(whichWeek: string) {
  let curr = new Date();
  if (whichWeek === 'last') {
    curr = new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000);
  }

  let week = [];

  for (let i = 1; i <= 7; i++) {
    let first = curr.getDate() - curr.getDay() + i;
    let day = new Date(curr.setDate(first)).toISOString().slice(0, 10);
    week.push(day);
  }
  return [week[0], week[6]];
}
export function getTotalUGTimeAvg(timeug: number[]) {
  let time = 0;
  for (let t of timeug) time += t;
  return time / timeug.length;
}
