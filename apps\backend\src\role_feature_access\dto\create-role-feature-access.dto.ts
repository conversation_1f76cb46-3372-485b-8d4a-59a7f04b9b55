import {
  IsBoolean,
  IsDefined,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateRoleFeatureAccessDto {
  @IsDefined()
  @ApiProperty()
  roleId: number;

  @IsDefined()
  @ApiProperty()
  featureId: number;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
