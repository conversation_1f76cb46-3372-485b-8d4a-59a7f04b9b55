import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { FormTemplate } from '../../form_templates/entities/form_template.entity';
import { Form } from '../../forms/entities/forms.entity';

@Entity('form_template_definitions')
export class FormTemplateDefinition {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'mine_id', type: 'int', nullable: false })
  mineId: number;

  @Column({ name: 'form_template_id', type: 'int', nullable: false })
  formTemplateId: number;

  @Column({ type: 'varchar', length: 'max', name: 'definition', nullable: false })
  definition: string;

  @Column({ name: 'is_published', nullable: false })
  isPublished: boolean;

  @Column({ name: 'published_at', nullable: true })
  publishedAt: Date;

  @Column({ name: 'published_by', nullable: true })
  publishedBy: number;

  @Column({ name: 'major', nullable: false })
  major: number;

  @Column({ name: 'minor', nullable: false })
  minor: number;

  @Column({ name: 'revision', nullable: false })
  revision: number;

  @Column({ name: 'is_delete', default: false, nullable: false })
  isDelete: boolean;

  @CreateDateColumn({ name: 'created_at', nullable: true })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at', nullable: true })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @ManyToOne(() => FormTemplate)
  @JoinColumn({ name: 'form_template_id' })
  formTemplate: FormTemplate;

  @OneToMany(() => Form, form => form.formTemplateDefinition)
  forms: Form[];
}
