import decodeJWT from '../../../utils/jwtdecoder';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import SubMenuInnerNavBar from '../common/SubMenuInnerNavBar';
import Table, { ColumnDef } from '../common/Table';
import { getFeatures } from '../../../api/users/userapis';
import { getFormCategoriesUsedByCompletedForms } from '../../../api/forms/formcategoryapis';
import { getSubmittedForms, getSubmittedFormsByUser } from '../../../api/forms/formapis';
import Loader from '../common/Loader';
import { useNavigate, useParams } from 'react-router-dom';
import { RevokeIcon } from '../../../assets/icons/icons';
import Modal from '../common/Modal';
import { toast } from 'react-toastify';
import { useRevokeForm } from '../../../services/mutations/formmutations';

const CompletedForms = () => {
  const params = useParams();
  const [tableCount, setTableCount] = useState();
  const [categoryFilter, setCategoryFilter] = useState('All');
  const [filteredCompletedForms, setFilteredCompletedForms] = useState([]);
  const [formData, setFormData] = useState<any>();
  const [openRevokeModal, setOpenRevokeModal] = useState(false);
  const userRole = decodeJWT()?.role;

  const [selectedDate, setSelectedDate] = useState({
    startDate: new Date().toISOString().split('T')[0],
  });
  
  const [date, setDate] = useState({
    startDate: new Date().toISOString().split('T')[0],
  });

  const formattedDate = useMemo(() => {
    const startDate = new Date(selectedDate?.startDate) ?? new Date(date?.startDate) ?? new Date();
    const localStartDate = new Date(new Date(startDate).toLocaleDateString());
    const month = (localStartDate.getMonth() + 1).toString().padStart(2, '0');
    const day = localStartDate.getDate().toString().padStart(2, '0');
    const year = localStartDate.getFullYear();

    const formattedDateHyphen = `${month}-${day}-${year}`;
    return formattedDateHyphen
  }, [selectedDate, date]);

  const {data: completedForms, refetch, isLoading } = useQuery({
    queryKey: ['completed-forms', formattedDate],
    queryFn: () => userRole == 'user' ? getSubmittedFormsByUser(formattedDate) : getSubmittedForms(formattedDate),
    refetchOnWindowFocus: false,
  });

  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });
  const { data: formCategories, isLoading: formCategoriesLoading } = useQuery({
    queryKey: ['form-categories-completed'],
    queryFn: () => getFormCategoriesUsedByCompletedForms(),
    refetchOnWindowFocus: false,
  });

  const navigate = useNavigate();
  const revokeForm = useRevokeForm();
  
  useEffect(() => {
    if (completedForms?.data) {
      categoryFilter === 'All'
      ? setFilteredCompletedForms(completedForms?.data)
      : setFilteredCompletedForms(completedForms?.data.filter((f: any) => f.category === categoryFilter));
    }
  }, [categoryFilter, completedForms?.data]);
  
  const completedColumns: ColumnDef[] = [
    {
      key: 'document',
      label: 'Document Name',
      type: 'text',
      truncate: false,
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 cursor-pointer"
          title="Open Form"
          data-tooltip-target="tooltip-default"
            onClick={async () => {
              navigate(
                params?.mineId
                ? `/app/Mines/${params?.mineId}/Forms/Completed/${row.formId}`
                : `/app/Forms/Completed/${row.formId}`
              );
            }}>
          <u>{row.document}</u>
        </div>
      ),
    },
    // {
    //   key: 'name',
    //   label: 'Form Name',
    //   type: 'text',
    //   render: (row: any) => (
    //     <div className="text-ellipsis overflow-hidden w-35">
    //       {row.name}
    //     </div>
    //   ),
    // },
    {
      key: 'category',
      label: 'Category',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.category}
        </div>
      ),
    },
    {
      key: 'shift',
      label: 'Shift',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.shift && row.shift.length > 0 ? row.shift : '-'}
        </div>
      ),
    },
    {
      key: 'section',
      label: 'Section',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.section && row.section.length > 0 ? row.section : '-'}
        </div>
      ),
    },
    {
      key: 'date',
      label: 'Date',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.date && row.date.length > 0 ? row.date : '-'}
        </div>
      )
    },
    // {
    //   key: 'version',
    //   label: 'Version',
    //   type: 'text',
    //   render: (row: any) => (
    //     <div className="text-ellipsis overflow-hidden w-35 ">
    //       {`${row.major}.${row.minor}.${row.revision}`}
    //     </div>
    //   ),
    // },
    {
      key: 'submittedBy',
      label: 'Completed By',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row?.submittedByUser ?? row?.updatedByUser ?? row?.createdByUser ?? '-'}
        </div>
      ),
    },
    {
      key: 'action',
      label: 'Actions',
      type: 'element',
      render: (row) => (
        <div className="flex justify-center gap-x-1">
          {/* <div
            title="Revoke Submission"
            className="cursor-pointer"
            data-tooltip-target="tooltip-default"
            onClick={() => {
              setFormData(row);
              setOpenRevokeModal(true);
              document
                ?.getElementsByClassName('scrollToTop')[0]
                ?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                });
            }}
          >
            <RevokeIcon className="text-white font-black text-[14px] h-7 w-5 mx-2 revoke_icon" />
          </div> */}
          <div className="pt-1">
            <button
              id="revoke_submission"
              title="Revoke Submission"
              className="text-[#21303C] bg-[#ffffff]/75 hover:bg-[#ffffff] font-medium rounded-lg text-sm px-4 py-1 text-center"
              onClick={() => {
                setFormData(row);
                setOpenRevokeModal(true);
                document
                  ?.getElementsByClassName('scrollToTop')[0]
                  ?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                  });
              }}>Revoke</button>
          </div>
          <div className="pt-1">
            <button
              id="new_continuation"
              title="Click to create new form"
              className="text-white bg-[#4AA8FE]/75 hover:bg-[#4AA8FE] font-medium rounded-lg text-sm px-4 py-1 text-center"
              onClick={() => {
                navigate(
                  params?.mineId
                  ? `/app/Mines/${params?.mineId}/Forms/New/NewForm/${row.formTemplateId}/Continuation/${row.formId}`
                  : `/app/Forms/New/NewForm/${row.formTemplateId}/Continuation/${row.formId}`
                );
              }}>New Form</button>
          </div>
        </div>
      ),
    },
  ];

  if ((features?.data.some((feature: any) => feature.FeatureName == 'Forms') ||
    decodeJWT()?.role == 'superuser')
  ) {
    const catArr = formCategories?.data.map((c:any) => c?.name);
    let formCategoryList = ['All'];
    if(catArr && catArr.length > 0) {
      formCategoryList = ['All',...catArr];
    }
    return (
      <>
        <SubMenuInnerNavBar
          leftTitle="Completed"
          rightTitle="Forms"
          tabNames={formCategoryList}
          showSearchBox={false}
          pathToRender={
            decodeJWT()?.role == 'superuser'
              ? `Mines/${params?.mineId}/Forms/Completed`
              : 'Forms/Completed'
          }
          selectedDate={selectedDate}
          setSelectedDate={setSelectedDate}
          date={date}
          setDate={setDate}
          setCategoryFilter={setCategoryFilter}
          defaultTab='All'
        />
        <div className="mt-8 px-10 2xl:px-16">
          <div>
            {isLoading ? (
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            ) : (
              <div className={`pt-8 userMTableBg px-5 rounded-xl
                ${filteredCompletedForms.length <= 0 ? 'pb-8' : 'pb-2'}`}>
                <div className="flex justify-between">
                  <div className="block">
                    <h6 className="font-semibold text-[20px] text-white">
                      {`Completed ${categoryFilter == 'All' ? '' : categoryFilter} Forms`}
                    </h6>
                  </div>
                </div>
                <div className="mt-5">
                  {filteredCompletedForms.length > 0 ? (
                    <div>
                      <Table
                        columns={completedColumns}
                        data={
                          filteredCompletedForms?.map((ele: any) => ({
                            document:
                              ele?.document,
                            ...ele,
                          })) ?? []
                        }
                        searchText=''
                        sortable={true}
                        searchOnColumn={'document'}
                        separateLine={false}
                        scrollable={true}
                        dataRenderLimitMdScreen={8}
                        dataRenderLimitLgScreen={15}
                        tableHeightClassLg="tableheightForlg"
                        tableHeightClassMd="tableheightFormd"
                        setTableCount={setTableCount}
                      />
                    </div>
                  ) : (
                    <div className="text-[26px] font-bold text-center text-white">
                      {`There are currently no completed ${categoryFilter == 'All' ? '' : categoryFilter} forms`}
                    </div>
                  )}
                </div>

                {openRevokeModal ? (
                  <Modal
                    size="w-2/4 2xl:w-1/3 xl:w-2/4"
                    Content={
                      <div className="p-4">
                        <div className="text-[24px] text-white text-center ">
                          Are you sure you want to revoke?
                        </div>
                        <div className="my-2 text-[28px] text-[#4AA8FE]  text-center text-provima  text-ellipsis overflow-hidden m-auto">
                          {formData?.document}
                        </div>
                        <div className="mt-2 text-center">
                          <button
                            title="Click to Cancel"
                            id="cancel_button"
                            onClick={() => setOpenRevokeModal(!openRevokeModal)}
                            className="my-2  text-white hover:border-[#4AA8FE]/75 text-[14px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE] mr-2"
                          >
                            Cancel
                          </button>
                          <button
                            title="Click to revoke form"
                            id="revoke_button"
                            onClick={async (e: any) => {
                              e.preventDefault();
                              try {
                                const res = await revokeForm.mutateAsync(
                                  formData?.formId
                                );
                                if (res?.status == 200 || res?.status == 201) {
                                  setOpenRevokeModal(!openRevokeModal);
                                  setFormData({});
                                  toast.success('Form revoked successfully');
                                }
                              } catch (err: any) {
                                toast.error(err.message);
                              }
                            }}
                            className="my-2 bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 hover:border-[#4AA8FE]/75 text-white text-[14px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                          >
                            Yes, Revoke
                          </button>
                        </div>
                      </div>
                    }
                    backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
                  />
                ) : (
                  <></>
                )}
              </div>
            )}
          </div>
        </div>
      </>
    );
  } else {
    return '';
  }
};

export default CompletedForms;
