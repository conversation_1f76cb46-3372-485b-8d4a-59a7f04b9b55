describe('frontend-e2e', () => {
  beforeEach(() => {
    cy.on('uncaught:exception', (e) => {
      if (e.message.includes('EOF is not defined')) {
        return false;
      }
    });

    cy.visit('http://localhost:4200/app/');
    cy.wait(1000);
    cy.fixture('loginData').then((loginData) => {
      const data = loginData.users[6];
      cy.get('#username').type(data.validUserName1);
      cy.wait(1000);
      cy.get('#password').type(data.validPass1);
      cy.wait(1000);

      // cy.contains('Sign In').click();
      cy.get('#signin').click();
      cy.wait(1000);
      cy.url().should('include', '/Dashboard');
      cy.wait(1000);
    });
  });

  it('should visit to location dashboard', () => {
    cy.contains('.font-normal', 'Location').click();
    cy.wait(1000);
    cy.contains('.font-normal', 'Live').click();
    cy.wait(1000);
    // cy.url().should('include', '/Location/dashboard/');

    // cy.get('#locLive').click();
    // cy.wait(1000);
    // cy.url().should('include', '/Location/live/checkins');

    cy.contains('CHECKINS').click();
    cy.url().should('include', '/Location/live/checkins');
    // cy.fixture('locationData').then((locationData) => {
    //   const searchTerm = locationData.loc[0];
    //   // cy.get('#searchIcon').type(searchTerm.validSeachText).clear();
    //     cy.get('#searchInput')
    //     .should('be.visible')
    //     .type(searchTerm.validSeachText)
    //     .should('have.value', searchTerm.validSeachText)
    //     .clear()
    //     .should('have.value', '');
    //   cy.wait(2000);
    // });

    cy.wait(1000);
    cy.contains('SECTIONS').click();
    cy.url().should('include', '/Location/live/sections');
    cy.wait(2000);
    cy.get('#sortingDropdown').click();
    cy.wait(2000);
    cy.contains('#selectOption', 'First Departure').click();
    cy.get('#sortingDropdown').should('have.text', 'First Departure');
    cy.contains('8YE').click();
    cy.wait(1000);
    cy.contains('WATCHLIST').click();
    cy.url().should('include', '/Location/live/watchlist');
    cy.wait(1000);
    cy.get('#clearWatchlist').click();
    cy.wait(2000);
    cy.get('#clearwatchListCancel').click();
    cy.wait(2000);
    cy.get('#clearWatchlist').click();
    cy.wait(2000);
    // cy.get('#clearList').click();
    cy.wait(2000);
    // cy.get('#selectCheckbox').click();
    // cy.wait(2000);
    // cy.get('#removeWatchlist').click();
    // cy.wait(2000);
    // cy.get('#cancelRemoveWatchlist').click();
    // cy.wait(2000);
    // cy.get('#removeWatchlist').click();
    // cy.wait(2000);
    // cy.get('#removeSelectedWatchlist').click();
    // cy.wait(2000);
    cy.contains('.font-normal', 'Report').click();
    cy.wait(2000);
    // cy.get('.react-datepicker__input-container input').click();
    // cy.wait(1000);
    // cy.get('.react-datepicker__month-container').should('be.visible');
    // cy.wait(1000);
    // cy.get('.react-datepicker__day--today').click();
    // cy.wait(2000);

    cy.contains('SECTIONS').click();
    cy.url().should('include', '/Location/report/sections');
    cy.wait(2000);
    // cy.get('#sortingDropdown').click();
    // cy.wait(2000);
    // cy.contains('#selectOption', 'First Departure').click();
    // cy.get('#sortingDropdown').should('have.text', 'First Departure');
    cy.contains('8YE').click();
    cy.wait(1000);

    cy.contains('PERSONNEL').click();
    cy.url().should('include', 'Location/report/personnel');
    cy.wait(2000);
  });
});
