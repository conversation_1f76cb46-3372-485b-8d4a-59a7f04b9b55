import { Link, Outlet, useParams } from 'react-router-dom';
import decodeJWT from '../../../utils/jwtdecoder';
import { useEffect, useRef, useState } from 'react';
import { SearchIcon } from '../../../../src/assets/icons/icons';
import Datepicker from 'react-tailwindcss-datepicker';
import { UserMenu } from '../common/UserMenu';
import dayjs from 'dayjs';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';

const AtmosphereReport = () => {
  let decoded = decodeJWT();
  const params = useParams();
  const [selectedMenu, setSelectedMenu] = useState('gas');
  const today = new Date();
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay() + 1);
  const endOfWeek = new Date(today);
  endOfWeek.setDate(today.getDate() - today.getDay() + 7);
  const [dateRangeSelected, setDateRangeSelected] = useState({
    startDate: startOfWeek,
    endDate: endOfWeek,
  });

  const lastUpdatedDate = new Date().toDateString();
  // Parse input datetime string
  const datetime = dayjs(lastUpdatedDate);

  // Format the date
  const formattedDate = ` Today ` + datetime.format('[@] hh:mm a');
  const wrapperRef = useRef<HTMLDivElement>(null);
  const tabNames = ['gas', 'Environmental', 'Ventilation', 'Notifications'];

  const sections = ['All', '8YN', '7YN', '7YE', '6YE', '6YN', 'No Section'];
  const [selected, setSelected] = useState('All');

  const handleCalendarIconClick = () => {
    mixpanel.track(
      'Date Picker Change track the Shortcuts and date selection ',
      {
        Page_Name: getPageNamesFromUrl(params['*'] ?? ''),
        MineName: decoded?.minename,
      }
    );
    const input = wrapperRef.current?.querySelector('input');
    if (input) input.focus();
  };
  useEffect(() => {
    const path = params['*'];
    const lastSegment = path?.split('/').pop();

    if (lastSegment && tabNames.includes(lastSegment)) {
      setSelectedMenu(lastSegment);
    }
  }, [params]);
  return (
    <>
      <div className="sticky w-full top-0 z-30  agBreakup2  bg-top px-10 2xl:px-16">
        <div className="grid grid-cols-3   pt-2 ">
          <div className="">
            <h6 className="pt-2 font-bold text-white text-[32px]">Reports</h6>
          </div>
          <div
            className={`${selectedMenu == 'Notifications' ? 'invisible' : ''}`}
          >
            <div className="relative pt-[9px]">
              <div
                id="searchInput"
                className="flex items-center h-12 w-72 text-base p-4 border border-[#4AA8FE] justify-start rounded-md text-white"
              >
                <SearchIcon />{' '}
                <input
                  type="text"
                  placeholder="Search for sensor"
                  className="ml-2 bg-transparent border-none outline-none focus:border-none  text-white"
                  autoFocus
                ></input>
              </div>
            </div>
          </div>

          <div className="">
            <div className="flex justify-end items-center pt-2 relative">
              <h6 className="font-bold text-white text-[32px]"> Atmosphere</h6>
              <span className="ml-4">
                <UserMenu />
              </span>
            </div>
            <div className="mb-2 text-right">
              {/* <span className="text-[#ffb132] text-[12px]">Last Updated: </span>
              <span className="text-white text-[12px] mr-2">
                {formattedDate}
              </span> */}
            </div>
          </div>
        </div>
        <div
          className={` flex justify-between border-b-[1px]  border-[#80c2fe]`}
        >
          <div>
            <div
              className={`flex flex-column justify-start  text-white w-[35%] gap-20 cursor-pointer text-[14px] mt-[10px] font-normal`}
            >
              {tabNames.map((tab, index) => (
                <Link
                  to={
                    decodeJWT()?.role == 'superuser'
                      ? `/app/Mines/${params?.mineId}/Atmosphere/report/${tab}`
                      : `/app/Atmosphere/report/${tab}`
                  }
                  key={tab}
                  onClick={() => setSelectedMenu(tab)}
                  className={`${selectedMenu == tab ? 'text-[#FFB132]' : ''} `}
                >
                  <span
                    className={`cursor-pointer ${
                      selectedMenu === tab
                        ? 'border-b-[5px] pb-[7px] border-[#FFB132]'
                        : ''
                    }`}
                  >
                    {tab.toUpperCase()}
                  </span>
                  {selectedMenu === tab ? <div className={`h-2.5`}></div> : ''}{' '}
                </Link>
              ))}
            </div>
          </div>
          <div className="flex justify-end w-[25%] ">
            <div
              className="w-[272px] custom-datepicker relative"
              ref={wrapperRef}
            >
              <Datepicker
                value={dateRangeSelected}
                onChange={(newValue: any) => setDateRangeSelected(newValue)}
                asSingle={false}
                displayFormat="MMM DD, YYYY"
                separator={'-'}
                className={'custom-datepicker'}
                popoverDirection="down"
                useRange={false}
                maxDate={new Date(new Date().setHours(0, 0, 0, 0))}
              />
              <div
                className="absolute bottom-[10px] right-[10px] cursor-pointer peer"
                onClick={handleCalendarIconClick}
                title=" Edit date range"
              >
                <svg
                  className="h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {selectedMenu !== 'Notifications' && (
        <div className="flex flex-row mt-3 W-[50%] overflow-x-scroll px-8 2xl:px-14">
          {sections?.map((section, index) => (
            <div key={index}>
              {selected === section ? (
                <div className="text-black mx-3 text-[12px] cursor-pointer rounded-sm px-1.5 py-1 font-[500]   bg-[#FFB132]">
                  {section} <div className=" "></div>
                </div>
              ) : (
                <div
                  className="text-white mx-3 text-[12px] px-1.5 py-1 font-[500] cursor-pointer"
                  onClick={() => {
                    mixpanel.track('On Section Click', {
                      SectionName: section,
                      Page_Name: getPageNamesFromUrl(params['*'] ?? ''),
                      MineName: decoded?.minename,
                    });
                    setSelected(section);
                  }}
                >
                  {section}{' '}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      <Outlet context={[dateRangeSelected]} />
    </>
  );
};

export default AtmosphereReport;
