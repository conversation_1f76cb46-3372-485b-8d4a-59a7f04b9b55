import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  checkTemplateNameMutation,
  FormTemplateData,
  FormTemplateDefinitionData,
  addFormTemplate,
  cloneFormTemplate,
  editFormTemplate,
  addDefinitionRevision,
  editFormTemplateDefinition,
  publishFormTemplateDefinition,
  deleteFormTemplate,
  uploadFile,
  unpublishFormTemplateDefinition
} from '../../api/forms/formtemplateapis';

export function useCheckTemplateNameMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (name: string) => checkTemplateNameMutation(name),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['check-template-name'] });
      }
    },
  });
}

export function useAddFormTemplate() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: FormTemplateData) => addFormTemplate(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-form-template'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-templates','form-template','form'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useEditFormTemplate() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) => {
      const id: any = data?.id;
      delete data.id;
      return editFormTemplate(id, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['edit-form-template'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-templates','form-template','form'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useCloneFormTemplate() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) => {
      const id: any = data?.cloneId;
      delete data.cloneId;
      return cloneFormTemplate(id, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['clone-form-template'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-templates','form-template','form'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function usePublishFormTemplateDefinition() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: any) => publishFormTemplateDefinition(id),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['publish-form-template'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-templates','published-forms','form-categories-templates'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useUnpublishFormTemplateDefinition() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (formTemplateId: any) => unpublishFormTemplateDefinition(formTemplateId),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['unpublish-form-template'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-templates','published-forms','form-categories-templates'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useAddDefinitionRevision() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: FormTemplateDefinitionData) => {
      const templateId: any = data?.templateId;
      delete data.templateId;
      return addDefinitionRevision(templateId, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-form-template-definition'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-templates','form-template','form','all-shifts','all-sections'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useEditFormTemplateDefinition() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: FormTemplateDefinitionData) => {
      const id: any = data?.id;
      delete data.id;
      return editFormTemplateDefinition(id, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['edit-form-template-definition'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-templates','form-template','form','all-shifts','all-sections'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useDeleteFormTemplate() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteFormTemplate(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['delete-form-template'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form-templates','form-template','form'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useUploadFile() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: FormData) => uploadFile(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['upload-file'] });
      }
    },
  });
}