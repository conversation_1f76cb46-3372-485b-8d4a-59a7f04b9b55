import React, { useEffect, useState } from 'react';
import Table, { ColumnDef } from '../common/Table';
import { DownArrow, UpArrow } from '../../../assets/icons/icons';
import Modal from '../common/Modal';
import { toast } from 'react-toastify';
import { Link, useOutletContext, useParams } from 'react-router-dom';
import {
  useClearUserWatchlist,
  useDeleteSelectedUserWatchlist,
  useDeleteUserWatchlistItem,
} from '../../../services/mutations/watchlistmutations';

import { getUserWatchlist } from '../../../api/users/watchlistapis';
import { useQuery } from '@tanstack/react-query';
import decodeJWT from '../../../utils/jwtdecoder';
import { IwtEnv } from '../../../api/apiClient';
import { escapeRegExp } from '../../../utils/constant';
import Loader from '../common/Loader';
import { getPageNamesFromUrl } from '../PageName';
import mixpanel from 'mixpanel-browser'
declare var iwtEnv: IwtEnv;
export default function Watchlist() {
  const [searchText, setLastUpdatedDate] = useOutletContext() as [string, any];
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [watchlistItem, setWatchlistItem] = useState<any>();
  const [openDeleteModal, setOpenDeteteModal] = useState(false);
  const [clearWatchlistModal, setClearWatchlistModal] = useState(false);
  const [openDeleteSelectedModal, setOpenDeleteSelectedModal] = useState(false);
  const [watchlistData, setWatchlistData] = useState<any>([]);
  const [disabledButton, setDisabledButton] = useState(false);

  const regex = new RegExp(`(${escapeRegExp(searchText)})`, 'i');
  const deleteWatchlistItem = useDeleteUserWatchlistItem();
  const clearUserWatchlist = useClearUserWatchlist();
  const deleteSelectedWatchlistItem = useDeleteSelectedUserWatchlist();
  const params = useParams();
  const decoded = decodeJWT();
  const url = getPageNamesFromUrl(params['*']);
  
  const toggleRowSelection = (watchlistId: number) => {
    const selectedIndex = selectedRows.indexOf(watchlistId);
    const newSelectedRows = [...selectedRows];

    if (selectedIndex === -1) {
      newSelectedRows.push(watchlistId);
    } else {
      newSelectedRows.splice(selectedIndex, 1);
    }
    setSelectedRows(newSelectedRows);

    const checkbox = document.getElementById('selectAll');
    if (checkbox) {
      if (newSelectedRows.length === watchlistData.length) {
        checkbox.checked = true;
      } else {
        checkbox.checked = false;
      }
    }
  };

  const userTokenData = decodeJWT();
  const {
    data,
    refetch: refetchWatchlistData,
    isLoading,
    isFetching: isFetchingWatchlistData,
  } = useQuery({
    queryKey: ['user-watchlist'],
    refetchOnWindowFocus: false,
    queryFn: () =>
      getUserWatchlist(
        Number(userTokenData?.mineid),
        Number(userTokenData?.userId)
      ),
    staleTime: 0,
    gcTime: 10,
    refetchInterval: Number(iwtEnv?.timeIntervalForApi) * 60 * 1000,
  });

  useEffect(() => {
    setLastUpdatedDate(data?.data?.lastUpdatedTs);
  }, [data?.data?.lastUpdatedTs]);

  useEffect(() => {
    if (selectAll) {
      setSelectedRows(watchlistData?.map((row: any) => row.watchlistId) || []);
    } else {
      setSelectedRows([]);
    }
    setWatchlistData(
      data?.data.watchlist.filter((ele: any) =>
        ele.minerName.toLowerCase().includes(searchText.toLowerCase())
      )
    );
    //refetchWatchlistData();
  }, [selectAll, setSelectAll, data?.data.watchlist, searchText]);

  useEffect(() => {
    const checkbox = document.getElementById('selectAll');
    if (checkbox) {
      setSelectAll(false);
      checkbox.checked = false;
      setSelectedRows([]);
    }
  }, [searchText]);
  const handleSelectAllChange = (e: any) => {
    setSelectAll(e.target.checked);
    if (e.target.checked) {
      setSelectedRows(watchlistData?.map((row: any) => row.watchlistId) || []);
    }
  };

  useEffect(() => {
    const checkbox = document.getElementById('selectAll');
    if (checkbox) {
      checkbox.addEventListener('change', handleSelectAllChange);
    }
    return () => {
      if (checkbox) {
        checkbox.removeEventListener('change', handleSelectAllChange);
      }
    };
  }, [handleSelectAllChange]);

  const columns: ColumnDef[] = [
    {
      key: 'Status',
      label: `<div class="inline-flex items-center pt-1">
          <label
            class="relative flex items-center cursor-pointer"
            htmlFor="selectAll">
          <input type="checkbox" id="selectAll" class="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] checked:before:bg-gray-900 hover:before:opacity-10"/>
          <span class="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-3 w-3"
                viewBox="0 0 20 20"
                fill="#1A252F"
                stroke="#1A252F"
                strokeWidth="1"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </span>
          </label>
        </div>`,
      type: 'sr_no',
      render: (row: any) => (
        <div className="inline-flex items-center pt-1">
          <label
            className="relative flex items-center cursor-pointer"
            htmlFor="check"
          >
            <input
              type="checkbox"
              checked={selectedRows?.includes(row.watchlistId)}
              className="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] checked:before:bg-gray-900 hover:before:opacity-10"
              id="selectCheckbox"
              onChange={(e) => {
                toggleRowSelection(row.watchlistId);
              }}
            />
            <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                viewBox="0 0 20 20"
                fill="#1A252F"
                stroke="#1A252F"
                strokeWidth="1"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </span>
          </label>
        </div>
      ),
    },
    {
      key: 'minerName',
      label: 'Miner',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden" title={row?.minerName}>
          <Link
            className="underline"
            to={
              params?.mineId
                ? `/app/Mines/${params?.mineId}/Location/report/personnel/${row?.minerId}`
                : `/app/Location/report/personnel/${row?.minerId}`
            }
              onClick={() => {
                mixpanel.track('Personnel Click', {
                Page_Name: getPageNamesFromUrl(params['*']),
                MshaId: decodeJWT()?.mshaId,
                MinerId: row?.minerId
                }); 
              }}
            dangerouslySetInnerHTML={{
              __html: row?.minerName?.replace(
                regex,
                "<span style='color: #FFD084;'>$1</span>"
              ),
            }}
          ></Link>
        </div>
      ),
    },

    {
      key: 'ug',
      label: 'UG',
      type: 'time',
      render: (row: any) => (
        <div
          className={
            row.ugCC == 'R'
              ? 'text-[#FE4A6A]'
              : row.ugCC == 'G'
              ? 'text-[#96FB60]'
              : ''
          }
          title={row.ug}
        >
          <p className="inline">{row.ug ? row.ug : '-'}</p>
        </div>
      ),
    },
    {
      key: 'ugHrs',
      label: 'UG (hrs)',
      type: 'text',
      render: (row: any) => (
        <div
          className={
            row.ugHrsCC == 'R'
              ? 'text-[#FE4A6A]'
              : row.ugHrsCC == 'G'
              ? 'text-[#96FB60]'
              : ''
          }
          title={row.ugHrs}
        >
          <p className="inline">{row.ugHrs ? row.ugHrs : '-'}</p>
        </div>
      ),
    },
    {
      key: 'onSection',
      label: 'On- Section',
      type: 'text',
      render: (row: any) => (
        <div
          className={
            row.onSectionCC == 'R'
              ? 'text-[#FE4A6A] inline'
              : row.onSectionCC == 'G'
              ? 'text-[#96FB60] inline'
              : 'inline'
          }
          title={row.onSection}
        >
          <p className="inline">{row.onSection ? row.onSection : '-'}</p>
        </div>
      ),
    },
    {
      key: 'section',
      label: 'Section',
      type: 'text',
      render: (row: any) => (
        <div>
          <p className="inline">{row.section ? row.section : '-'}</p>
        </div>
      ),
    },
    {
      key: 'isWatched',
      label: 'Watch',
      type: 'action',
      render: (row: any) => (
        <div className="">
          {row.isWatched ? (
            <button
              className="bg-transparent hover:bg-blue-300 text-white-300 font-semibold hover:text-white py-2 px-4 border-[1px] border-[#4AA8FE] hover:border-transparent rounded-lg mr-10"
              title="Click to remove from watchlist"
              onClick={() => {
                try{
                  if (row.isWatched) {
                  setOpenDeteteModal(true);
                  setWatchlistItem(row);
                  mixpanel.track('Watchlist Remove', {
                    Page_Name: url,
                    MshaId: decoded?.mshaId
                  })
                }
                }catch(err: any){
                  mixpanel.track('Error Event',{
                    Page_Name: url,
                    Action_Name: 'Watchlist Remove'
                  })
                }
              }}
            >
              Remove
            </button>
          ) : (
            <button
              title="Click to add in watchlist"
              className="bg-transparent hover:bg-blue-300 text-white-300 font-semibold hover:text-white py-2 px-4 border-[1px] border-[#4AA8FE] hover:border-transparent rounded-lg"
            >
              Watch
            </button>
          )}
        </div>
      ),
    },
  ];
  
  return isLoading && isFetchingWatchlistData ? (
    <div>
      <div>
        <div className="flex justify-center items-center h-full pt-8 white">
          {<Loader />}
        </div>
        <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
          Loading....
        </div>
      </div>
    </div>
  ) : data?.data?.watchlist.length > 0 ? (
    <div className="box min-h-[70vh]">
      <div className="pt-6 px-[4%] bg-[#2c5b8b]">
        <div className="w-[100%] grid grid-cols-2 divide-x tableTitle rounded-tl-lg rounded-tr-lg">
          <p className="font-bold text-white text-[28px] p-6 border-none">
            Check-Ins
          </p>
          <p className="text-[#FFD084] text-end p-6 border-none text-[14px]">
            <a
              id="clearWatchlist"
              className="cursor-pointer"
              title="Click to clear watchlist"
              onClick={() => {
                try{
                  setClearWatchlistModal(true);
                mixpanel.track('Watchlist Clear', {
                  Page_Name: url,
                  MshaId: decoded?.mshaId,
                });
                }catch(err){
                  mixpanel.track('Error Event',{
                    Page_Name: url,
                    Action_Name: 'Watchlist Clear'
                  })
                }
              }}
            >
              CLEAR WATCHLIST
            </a>
            {selectedRows.length > 0 ? (
              <a
                id="removeWatchlist"
                className="cursor-pointer pl-4 pr-1 "
                title="Click to remove selected watchlist item(s)"
                onClick={() => {
                  try{
                    setOpenDeleteSelectedModal(true);
                  mixpanel.track('Watchlist Remove', {
                    Page_Name: url,
                    MshaId: decoded?.mshaId
                  })
                  }catch(err: any){
                    mixpanel.track('Error Event',{
                      Page_Name: url,
                      Action_Name: 'Watchlist Remove'
                    })
                  }
                }}
              >
                REMOVE
              </a>
            ) : (
              <a className="cursor-not-allowed pl-4 pr-1 text-[#567799]">
                REMOVE
              </a>
            )}
          </p>
        </div>
      </div>
      <div className="w-[100%] px-[4%]">
        <Table
          columns={columns}
          data={watchlistData ?? []}
          searchText={searchText}
          searchOnColumn="minerName"
          sortable={true}
          scrollable={true}
          dataRenderLimitMdScreen={6}
          dataRenderLimitLgScreen={14}
          tableHeightClassLg="h-[36rem]"
          tableHeightClassMd="h-[16rem]"
          paddingClass="pr-6 pl-6 pb-6"
          backgroundColor={true}
        />
      </div>
      {openDeleteModal ? (
        <Modal
          size="lg"
          Content={
            <div className="p-4">
              <div className="text-[24px] text-white text-center ">
                Are you sure you want to remove?
              </div>
              <div className="my-2 text-[56px] text-[#4AA8FE]  text-center text-provima">
                {watchlistItem?.minerName}
              </div>
              <div className="mt-2 text-center">
                <button
                  onClick={() => setOpenDeteteModal(!openDeleteModal)}
                  title="Click to cancel"
                  id="clearWatchlistButton"
                  className="   text-white  hover:border-[#4AA8FE]/75 text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline border border-[#4AA8FE] mr-2"
                >
                  Cancel
                </button>

                <button
                  disabled={disabledButton}
                  onClick={async () => {
                    try {
                      setDisabledButton(true);
                      const res = await deleteWatchlistItem.mutateAsync(
                        watchlistItem?.watchlistId
                      );
                      if (res?.status == 200 || res?.status == 201) {
                        setOpenDeteteModal(!openDeleteModal);
                        toast.success(
                          'Miner removed from watchlist successfully'
                        );
                        //refetchWatchlistData();
                        setDisabledButton(false);
                      }
                    } catch (err: any) {
                      toast.error(err.message);
                      setDisabledButton(false);
                    }
                  }}
                  title="Click to remove"
                  className="bg-[#4AA8FE] hover:border-[#4AA8FE]/75 hover:bg-[#4AA8FE]/75 text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                >
                  Yes, Remove
                </button>
              </div>
            </div>
          }
          backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
        />
      ) : (
        <></>
      )}

      {openDeleteSelectedModal ? (
        <Modal
          size="lg"
          Content={
            <div className="p-4">
              <div className="text-[24px] text-white text-center ">Remove?</div>
              <br />
              <div className="text-[16px] text-[#999999] text-center text-provima">
                Are you sure you want to remove the
                <p className="font-semibold text-white">selected miner?</p>
              </div>
              <div className="mt-2 text-center">
                <button
                  id="cancelRemoveWatchlist"
                  onClick={() =>
                    setOpenDeleteSelectedModal(!openDeleteSelectedModal)
                  }
                  title="Click to cancel"
                  className="  hover:border-[#4AA8FE]/75  text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline border border-[#4AA8FE] mr-2"
                >
                  Cancel
                </button>

                <button
                  id="removeSelectedWatchlist"
                  disabled={disabledButton}
                  onClick={async () => {
                    try {
                      setDisabledButton(true);
                      const res = await deleteSelectedWatchlistItem.mutateAsync(
                        selectedRows
                      );
                      if (res?.status == 200 || res?.status == 201) {
                        setOpenDeleteSelectedModal(!openDeleteSelectedModal);
                        toast.success(
                          'Selected miner(s) removed from watchlist successfully.'
                        );
                        //refetchWatchlistData();
                        setSelectedRows([]);
                        setDisabledButton(false);
                      }
                    } catch (err: any) {
                      toast.error(err.message);
                      setDisabledButton(false);
                    }
                  }}
                  title="Click to remove selected watchlist item(s)"
                  className="bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                >
                  Yes, Remove
                </button>
              </div>
            </div>
          }
          backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
        />
      ) : (
        <></>
      )}

      {clearWatchlistModal ? (
        <Modal
          size="lg"
          Content={
            <div className="p-4">
              <div className="text-[24px] text-white text-center ">
                Are you sure you want to <br />
                clear the entire watchlist?
              </div>
              <div className="italic font-normal text-[14px] text-white text-center pt-8">
                warning: this cannot be undone.
              </div>
              <div className="mt-2 text-center">
                <button
                  id="clearwatchListCancel"
                  onClick={() => setClearWatchlistModal(!clearWatchlistModal)}
                  title="Click to cancel"
                  className=" hover:border-[#4AA8FE]/75  text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline border border-[#4AA8FE] mr-2"
                >
                  Cancel
                </button>
                <button
                  id="clearList"
                  disabled={disabledButton}
                  onClick={async () => {
                    try {
                      setDisabledButton(true);
                      const res = await clearUserWatchlist.mutateAsync();
                      if (res?.status == 200 || res?.status == 201) {
                        setClearWatchlistModal(!clearWatchlistModal);
                        toast.success('Watchlist cleared successfully.');
                        //refetchWatchlistData();
                        setDisabledButton(false);
                      }
                    } catch (err: any) {
                      toast.error(err.message);
                      setDisabledButton(false);
                    }
                  }}
                  title="Click to clear watchlist"
                  className="bg-[#4AA8FE]  hover:bg-[#4AA8FE]/75 text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                >
                  Clear the list
                </button>
              </div>
            </div>
          }
          backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
        />
      ) : (
        <></>
      )}
    </div>
  ) : (
    <div className="text-center p-8 bg-[#2c5b8b]">
      <p className="font-bold text-white  text-2xl">Watchlist empty</p>
      <p className="font-light text-white  text-2xl">
        Search for personnel above
      </p>
    </div>
  );
}
