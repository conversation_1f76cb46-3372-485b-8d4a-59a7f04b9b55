import { BadRequestException } from '@nestjs/common';
import { validate } from '@nestjs/class-validator';
import { plainToClass } from '@nestjs/class-transformer';

export async function validateAndTransformDto<T extends Record<string, any>>(
  dtoClass: new () => T,
  inputDto: T
): Promise<T> {
  for (const key in inputDto) {
    if (typeof inputDto[key] === 'string') {
      inputDto[key] = inputDto[key].trim();
    }
  }
  const validatedDto = plainToClass(dtoClass, inputDto);
  const errors = await validate(validatedDto);

  if (errors.length > 0) {
    const errorMessages = errors.map(
      (error) => error.constraints[Object.keys(error.constraints)[0]]
    );

    throw new BadRequestException(errorMessages[0]);
  }

  return validatedDto;
}
