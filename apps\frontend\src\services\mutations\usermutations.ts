import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  checkUsername,
  login,
  AuthenticationData,
  addUser,
  UserData,
  getUsers,
  editUser,
  autogenerateUsername,
  deleteUser,
  resetPassword,
  statusChange,
  checkUserByEmail,
  changeTokenForSuperUser,
} from '../../api/users/userapis';
import {
  DateSelection,
  saveDateRange,
} from '../../api/users/dateSelectionapis';
import decodeJWT from '../../utils/jwtdecoder';

export function useLogin() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: AuthenticationData) => login(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['login'] });
      }
    },
    onSuccess: (res) => {
      localStorage.setItem('token', res.data.jwtToken);
    },
  });
}

export function useFindByUsername() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: string) => checkUsername(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['check-username'] });
      }
    },
  });
}

export function useSaveDateRange() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: DateSelection) => saveDateRange(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['date-selection'] });
        await queryClient.invalidateQueries({
          queryKey: ['personnelData'],
        });
      }
    },
  });
}

export function useFindByEmail() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (email: string) => checkUserByEmail(email),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['check-email'] });
      }
    },
  });
}

export function useGenerateUsername() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) =>
      autogenerateUsername(data?.firstname, data?.lastname),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['generate-username'],
        });
      }
    },
  });
}

export function useAddUser() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: UserData) => addUser(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-user'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['users'], exact: true });
    },
  });
}

export function useEditUser() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: UserData) => {
      const id: any = data?.id;
      delete data.id;
      return editUser(id, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['edit-user'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['users'], exact: true });
    },
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteUser(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['delete-user'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['users'], exact: true });
    },
  });
}

export function useResetPassword() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) => {
      return resetPassword(data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['delete-user'] });
      }
    },
  });
}

export function useStatusChange() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return statusChange(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['change-status'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['users'], exact: true });
    },
  });
}

export function useChangeTokenSuperUser() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (mineId: number) => {
      return changeTokenForSuperUser(mineId);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['change-token'] });
      }
    },
    onSuccess: (res) => {
      localStorage?.removeItem('token');
      localStorage.setItem('token', res?.data?.jwtToken);
    },
  });
}
