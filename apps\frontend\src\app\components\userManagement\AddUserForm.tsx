import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  useAddUser,
  useEditUser,
  useFindByUsername,
  useGenerateUsername,
  useFindByEmail,
} from '../../../services/mutations/usermutations';
import { getAllRoles } from '../../../api/roles/rolesapis';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { emailRegEx } from '../../../utils/constant';
import decodeJWT from '../../../utils/jwtdecoder';
import { CloseIcon } from '../../../assets/icons/icons';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import { useParams } from 'react-router-dom';

const schema = yup.object({
  firstName: yup
    .string()
    .max(20, 'First name should not exceed 20 characters')
    .required('Please enter first name'),
  lastName: yup
    .string()
    .max(20, 'Last name should not exceed 20 characters')
    .required('Please enter last name'),
  email: yup.string().required('Please enter email address'),
  cEmail: yup.string().required('Please confirm email address'),
  username: yup
    .string()
    .max(25, 'Username name should not exceed 25 characters')
    .required('Please enter username'),
  role: yup.string().required('Please select role'),
});

export default function AddUserForm(props: any) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
    getValues,
    setError,
    clearErrors,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const addUserData = useAddUser();
  const editUser = useEditUser();
  const generateUsername = useGenerateUsername();
  const checkUsernameMutation = useFindByUsername();
  const checkEmailMutation = useFindByEmail();
  const [selectFirstCHild, setSelectFirstChlid] = useState(false);
  const decoded = decodeJWT();
  const params = useParams();
  const url = getPageNamesFromUrl(params['*']);

  const { data, isLoading } = useQuery({
    queryKey: ['roles'],
    queryFn: getAllRoles,
    refetchOnWindowFocus: false,
  });
  const listJobTitle = data?.data;

  const storeEditUser = async (data: any) => {
    const roleId = listJobTitle?.filter((ele: any) => ele.id == data?.role)[0];
    data.jobTitle = roleId?.name;
    data.id = props?.editData?.userId;
    data.roleId = data?.role;
    delete data?.cEmail;
    delete data?.role;
    try{
    const res = await editUser.mutateAsync(data);
    mixpanel.track('Update User', {
      MshaId: decoded?.mshaId,
    });
    toast.success('User updated successfully');
    reset();
    props?.setOpenAddUserFrom(false);
    }catch(err: any){
      console.error('err',err);
      mixpanel.track('Error Event',{
        Page_Name: url,
        Action_Name: 'Update User'
      })
    }
    
  };

  const onSubmitHandler = async (data: any) => {
    if (!emailRegEx.test(data.email)) {
      setError('email', {
        type: 'manual',
        message: 'Please enter valid email address',
      });
    } else {
      if (data?.email === data?.cEmail) {
        try {
          if (
            String(getValues('firstName')).trim() !== '' &&
            String(getValues('lastName')).trim() !== '' &&
            String(getValues('username')).trim() !== '' &&
            String(getValues('email')).trim() !== '' &&
            !getValues('username').includes(' ')
          ) {
            data.firstName = String(getValues('firstName')).trim();
            data.lastName = String(getValues('lastName')).trim();
            data.username = String(getValues('username')).trim();
            data.email = String(getValues('email')).trim();

            if (props?.editData) {
              if (
                String(getValues('username')).trim() !==
                props?.editData?.users_username
              ) {
                const response = await checkUsernameMutation.mutateAsync(
                  getValues('username').trim()
                );

                if (response.data.id) {
                  setError('username', {
                    type: 'manual',
                    message: 'Username already exist!',
                  });
                } else {
                  storeEditUser(data);
                }
              } else {
                storeEditUser(data);
              }
            } else {
              const response = await checkUsernameMutation.mutateAsync(
                getValues('username').trim()
              );

              if (response.data.id) {
                setError('username', {
                  type: 'manual',
                  message: 'Username already exists',
                });
              } else {
                const responseEmail = await checkEmailMutation.mutateAsync(
                  getValues('email').trim()
                );

                if (responseEmail.data.id) {
                  setError('email', {
                    type: 'manual',
                    message: 'User already exists with this email address',
                  });
                } else {
                  const roleId = listJobTitle?.filter(
                    (ele: any) => ele.id == data?.role
                  )[0];
                  delete data?.cEmail;
                  data.jobTitle = roleId?.name;
                  data.roleId = data?.role;
                  delete data.jobTitle;
                  delete data.role;

                  try{
                    const res = await addUserData.mutateAsync(data);
                  mixpanel.track('Add User', {
                    MshaId: decoded?.mshaId,
                  });
                    mixpanel.people.set({
                      $UserName: res?.data?.username,
                      $First_Name: res?.data?.firstName,
                      $Last_Name: res?.data?.lastName,
                      $UserId: decoded?.userId,
                      $MshaId: res?.data?.MshaId,
                      $Email: res?.data?.email,
                    });
                  toast.success('User added successfully');
                  reset();
                  props?.setOpenAddUserFrom(false);
                  }catch(err: any){
                    console.error('err',err);
                    mixpanel.track('Error Event', {
                      Page_Name: url,
                      Action_Name: 'Add User'
                    })
                  }
                }
              }
            }
          } else {
            if (getValues('username').includes(' ')) {
              setError('username', {
                type: 'manual',
                message: 'No spacing is allowed in a username',
              });
            } 
            // else {
            //   toast.error('Please enter the valid information');
            // }
          }
        } catch (err: any) {
          if (getValues('username').includes(' ')) {
            setError('username', {
              type: 'manual',
              message: 'No spacing is allowed in a username',
            });
          } 
          // else {
          //   toast.error('Please enter the valid information');
          // }

          console.error(err.message);
        }
      } else {
        setError('cEmail', {
          type: 'manual',
          message: 'Email addresses does not match',
        });
      }
    }
  };

  useEffect(() => {
    if (props.editData) {
      clearErrors();
      setValue('email', props?.editData?.users_email);
      setValue('cEmail', props?.editData?.users_email);
      setValue('firstName', props?.editData?.users_first_name);
      setValue('lastName', props?.editData?.users_last_name);
      setValue('role', props?.editData?.roleId);
      setValue('username', props?.editData?.users_username);
    } else {
      reset();
    }
  }, [props?.editData, listJobTitle, reset]);

  useEffect(() => {
    if (
      (String(getValues('firstName')).trim() !== '' ||
        String(getValues('lastName')).trim() !== '') &&
      !props?.editData
    ) {
      const data = {
        firstname: String(getValues('firstName')).trim(),
        lastname: String(getValues('lastName')).trim(),
      };
      try {
        const res = async () => {
          const response = await generateUsername.mutateAsync(data);
          if (response?.data !== 'uundefined' && response.data !== 404) {
            setValue('username', response?.data);
            clearErrors('username');
          } else {
            if (String(getValues('firstName')).trim() == '') {
              setValue('username', '');
            } else {
              setValue('username', String(getValues('firstName')).charAt(0));
            }
          }
        };
        if (String(getValues('lastName')).trim() !== '') {
          res();
        } else {
          if (String(getValues('firstName')).trim() == '') {
            setValue('username', '');
          } else {
            setValue('username', String(getValues('firstName')).charAt(0));
          }
        }
      } catch (err: any) {
        if (String(getValues('firstName')).trim() == '') {
          setValue('username', '');
        } else {
          setValue('username', String(getValues('firstName')).charAt(0));
        }
      }
    }

    if (String(getValues('firstName')).trim() == '' && !props?.editData) {
      setValue('username', '');
    }
  }, [watch('firstName'), watch('lastName')]);

  return (
    <div>
      <div className="">
        <div className="flex justify-between mx-4">
          <div className="block">
            <h6 className="font-semibold text-[24px] text-white">
              {props?.editData ? 'Edit  User' : 'Add New User'}
            </h6>
            <p className="font-normal my-1 text-[16px] tracking-normal leading-5 text-[#cccccc] ">
              {!props?.editData
                ? 'Enter all of the required information to add a new user to the platform'
                : ``}
            </p>
          </div>
          <div
            className="mt-1 cursor-pointer"
            onClick={() => {
              props?.setOpenAddUserFrom(false);
            }}
          >
            <CloseIcon />
          </div>
        </div>
        <div className="">
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <div className="rounded pt-3">
              <div className="m-2">
                <div className="grid grid-cols-2">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      First Name
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('firstName')}
                      type="text"
                      name="firstName"
                      id="firstName"
                      className="bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black"
                      placeholder="Enter First Name"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.firstName?.message}
                    </p>
                  </div>
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Email Address
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('email')}
                      type="text"
                      name="email"
                      id="email"
                      className={
                        props?.editData
                          ? 'bg-[#A4A5A5] p-1.5 w-full  rounded pl-2 text-[14px] text-black opacity-[0.7] cursor-not-allowed'
                          : 'bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black'
                      }
                      placeholder="Enter Email Address"
                      disabled={props?.editData ? true : false}
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.email?.message}
                    </p>
                  </div>
                  <div></div>
                </div>

                <div className="grid grid-cols-2 py-2">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Last Name
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('lastName')}
                      type="text"
                      name="lastName"
                      id="lastName"
                      className="bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black"
                      placeholder="Enter Last Name"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.lastName?.message}
                    </p>
                  </div>
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Confirm Email Address
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('cEmail')}
                      type="text"
                      name="cEmail"
                      id="cEmail"
                      className={
                        props?.editData
                          ? 'bg-[#A4A5A5] p-1.5 w-full  rounded pl-2 text-[14px] text-black opacity-[0.7] cursor-not-allowed'
                          : 'bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black'
                      }
                      placeholder="Confirm Email Address"
                      disabled={props?.editData ? true : false}
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.cEmail?.message}
                    </p>
                  </div>
                  <div className=""></div>
                </div>

                <div className="grid grid-cols-2">
                  <div className="mx-2">
                    <span className="text-[12px] text-white">
                      Username (auto-generated)
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('username')}
                      type="text"
                      name="username"
                      id="username"
                      className="bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black"
                      placeholder="Enter Username"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.username?.message}
                    </p>
                  </div>
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Role
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <select
                      disabled={
                        props?.editData &&
                        decodeJWT()?.username == props?.editData?.users_username
                          ? true
                          : false
                      }
                      {...register('role')}
                      name="role"
                      id="role"
                      onChange={() => {
                        clearErrors('role');
                        setSelectFirstChlid(true);
                      }}
                      className={`${
                        props?.editData &&
                        decodeJWT()?.username == props?.editData?.users_username
                          ? 'bg-[#A4A5A5] p-[7px] w-full  rounded pl-2 text-[14px] text-black cursor-not-allowed'
                          : 'block w-full p-[7px]  rounded bg-gray-200  focus:border-blue-500 text-[14px]'
                      }   ${
                        !selectFirstCHild && !props?.editData
                          ? 'text-gray-400'
                          : 'text-black'
                      }`}
                    >
                      <option className="hidden" value={''}>
                        Select Role
                      </option>
                      {listJobTitle?.map((ele: any) => (
                        <option key={ele?.id} value={ele?.id} className="text-black">
                          {ele.name.charAt(0).toUpperCase() + ele.name.slice(1)}
                        </option>
                      ))}
                    </select>
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.role?.message}
                    </p>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 mt-5">
                <div className="mx-3">
                  <button
                    id="cancel_button"
                    title="Click to cancel"
                    onClick={() => {
                      props?.setOpenAddUserFrom(false);
                    }}
                    className={`text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center h-9 w-full items-center  me-2 mb-2 ${
                      props?.editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {'Cancel'}
                  </button>
                </div>
                <div className="mx-3">
                  <button
                    id="create_user"
                    title={
                      !props?.editData
                        ? 'Click to add new user information'
                        : 'Click to save user information'
                    }
                    type="submit"
                    className={`text-white bg-[#4AA8FE]  hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-8 py-2 text-center h-9 w-full items-center  me-2 mb-2 ${
                      props?.editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {props?.editData ? 'Save' : 'Add'}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
