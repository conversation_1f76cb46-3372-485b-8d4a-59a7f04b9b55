import Sidebar from './components/common/SideBar';
import { Outlet, useLocation, useParams } from 'react-router-dom';
import { NavBar } from './components/common/NavBar';
import 'react-toastify/dist/ReactToastify.css';
import Login from './components/login/Login';
import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import decodeJWT from '../utils/jwtdecoder';
import ErrorPage from './components/errorPage/error-page';
import Feature from './components/feature/Feature';
import Compliance from './components/compliance/Compliance';
import ProdReportSection from './components/production/prodReport/ProdReportSection';
import ProdReportCompare from './components/production/prodReport/ProdReportCompare';
import ProdReportMine from './components/production/prodReport/ProdReportMine';
import ProdReport from './components/production/ProdReport';
import ProdLiveAlerts from './components/production/prodReport/ProdLiveAlerts';
import ProdLiveSection from './components/production/ProdLiveSection';
import ProdLiveMine from './components/production/ProdLiveMine';
import ProdLive from './components/production/ProdLive';
import ReportPersonnel from './components/location/report/ReportPersonnel';
import LocReport from './components/location/LocReport';
import ReportSection from './components/location/report/ReportSection';
import ReportCheckins from './components/location/report/ReportCheckins';
import Section from './components/location/Section';
import Watchlist from './components/location/Watchlist';
import Checkins from './components/location/CheckIns';
import LocLive from './components/location/LocLive';
import LocDashboard from './components/location/LocDashoard';
import Goals from './components/goalManagement/Goals';
import Users from './components/userManagement/Users';
import Dashboard from './components/dashboard/Dashboard';
import Mines from './components/mineManagement/Mines';
import FormTemplates from './components/forms/Templates';
import FormDesigner from './components/forms/designer/Designer';
import NewForms from './components/forms/New';
import FormPage from './components/forms/new/FormPage';
import CompletedForms from './components/forms/Completed';
import { useQuery } from '@tanstack/react-query';
import { getFeatures } from '../api/users/userapis';
import Loader from './components/common/Loader';
import AuditLogs from './components/AuditLogs/AuditLogs';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from './components/PageName';
import Shifts from './components/shifts/Shifts';
import AtmosphereLive from './components/atmosphere/atmoshpereLive';
import AtmosphereReport from './components/atmosphere/atmosphereReport';
import AtmosphereDashboard from './components/atmosphere/atmosphereDashboard';
import NotificationComp from './components/atmosphere/notificationCom';
import LiveVentilation from './components/atmosphere/Live/ventilation';
import LiveEnvironment from './components/atmosphere/Live/environmental';
import LiveGas from './components/atmosphere/Live/gas';
import ReportVentilation from './components/atmosphere/Report/ventilation';
import ReportEnvironment from './components/atmosphere/Report/environmental';
import ReportGas from './components/atmosphere/Report/reportGas';

export default function LandingPage(props: any) {
  const [open, setOpen] = useState(false);
  const decoded = decodeJWT();
  const location = useLocation();
  const params = useParams();

  const { data: features, status: apiStatus } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    props?.setRouteItem([
      {
        path: '*',
        element: (
          <div className="w-[400px] m-auto pt-[20px]">
            <h1 className="text-white text-[24px]">PAGE NOT FOUND</h1>
          </div>
        ),
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Dashboard'
            : 'Dashboard',
        element: <Dashboard />,
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Setting/users'
            : 'Setting/users',
        element: <Users />,
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Setting/goals'
            : 'Setting/goals',
        element: <Goals />,
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Setting/Features'
            : 'Setting/Features',
        element: <Feature />,
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Setting/Shifts'
            : 'Setting/Shifts',
        element: <Shifts />,
        errorElement: <ErrorPage />,
      },
      {
        path: 'mines',
        element: <Mines />,
        errorElement: <ErrorPage />,
      },
      {
        path: 'AuditLogs',
        element: <AuditLogs />,
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Location'
            : 'Location',
        element: <LocDashboard />,
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Location/Dashboard'
            : 'Location/Dashboard',
        element: <LocDashboard />,
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Atmosphere/Dashboard'
            : 'Atmosphere/Dashboard',
        element: (
          <>
            <AtmosphereDashboard />
          </>
        ),
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Location/Live'
            : 'Location/Live',
        element: <LocLive />,
        errorElement: <ErrorPage />,
        children: [
          {
            path: 'checkins',
            element: <Checkins />,
            errorElement: <ErrorPage />,
          },
          {
            path: 'sections',
            element: <Section />,
            errorElement: <ErrorPage />,
          },
          {
            path: 'watchlist',
            element: <Watchlist />,
            errorElement: <ErrorPage />,
          },
        ],
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Location/Report'
            : 'Location/Report',
        element: <LocReport />,
        errorElement: <ErrorPage />,
        children: [
          { path: 'checkins', element: <ReportCheckins /> },
          { path: 'sections', element: <ReportSection /> },
          {
            path: 'sections/:selectedDate/:sectionName',
            element: <ReportSection />,
          },
          { path: 'personnel', element: <ReportPersonnel /> },
        ],
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Location/Report/personnel/:minerId'
            : 'Location/Report/personnel/:minerId',
        element: <LocReport />,
        errorElement: <ErrorPage />,
        children: [
          {
            index: true,
            element: <ReportPersonnel />,
          },
        ],
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Production'
            : 'Production',
        element: <ProdLive />,
        errorElement: <ErrorPage />,
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Production/live'
            : 'Production/live',
        element: <ProdLive />,
        errorElement: <ErrorPage />,
        children: [
          {
            path: 'mine',
            element: <ProdLiveMine />,
            errorElement: <ErrorPage />,
          },
          {
            path: 'sections',
            element: <ProdLiveSection />,
            errorElement: <ErrorPage />,
          },
          {
            path: 'alerts',
            element: <ProdLiveAlerts />,
            errorElement: <ErrorPage />,
          },
        ],
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Production/Report'
            : 'Production/Report',
        element: <ProdReport />,
        errorElement: <ErrorPage />,
        children: [
          {
            path: 'mine',
            element: <ProdReportMine />,
            errorElement: <ErrorPage />,
          },
          {
            path: 'sections',
            element: <ProdReportSection />,
            errorElement: <ErrorPage />,
          },
          {
            path: 'compare',
            element: <ProdReportCompare />,
            errorElement: <ErrorPage />,
          },
          {
            path: 'sections/:sectionId',
            element: <ProdReportSection />,
            errorElement: <ErrorPage />,
          },
        ],
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Atmosphere/report'
            : 'Atmosphere/report',
        element: <AtmosphereReport />,
        errorElement: <ErrorPage />,
        children: [
          {
            path: 'gas',
            element: (
              <>
                <ReportGas />
              </>
            ),
            errorElement: <ErrorPage />,
          },
          {
            path: 'Environmental',
            element: (
              <>
                <ReportEnvironment />
              </>
            ),
            errorElement: <ErrorPage />,
          },
          {
            path: 'ventilation',
            element: (
              <>
                <ReportVentilation />
              </>
            ),
            errorElement: <ErrorPage />,
          },
          {
            path: 'notifications',
            element: (
              <>
                <NotificationComp />
              </>
            ),
            errorElement: <ErrorPage />,
          },
        ],
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Atmosphere/live'
            : 'Atmosphere/live',
        element: <AtmosphereLive />,
        errorElement: <ErrorPage />,
        children: [
          {
            path: 'gas',
            element: (
              <>
                <LiveGas />
              </>
            ),
            errorElement: <ErrorPage />,
          },
          {
            path: 'Environmental',
            element: (
              <>
                <LiveEnvironment />
              </>
            ),
            errorElement: <ErrorPage />,
          },
          {
            path: 'ventilation',
            element: (
              <>
                <LiveVentilation />
              </>
            ),
            errorElement: <ErrorPage />,
          },
          {
            path: 'notifications',
            element: (
              <>
                <NotificationComp />
              </>
            ),
            errorElement: <ErrorPage />,
          },
        ],
      },
      {
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Compliance'
            : 'Compliance',
        element: <Compliance />,
        errorElement: <ErrorPage />,
      },{
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Forms/Templates'
            : 'Forms/Templates',
        element: <FormTemplates />,
        errorElement: <ErrorPage />,
      },{
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Forms/Templates/:formTemplateId'
            : 'Forms/Templates/:formTemplateId',
        element: <FormDesigner />,
        errorElement: <ErrorPage />,
      },{
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Forms/New'
            : 'Forms/New',
        element: <NewForms />,
        errorElement: <ErrorPage />,
        children: [
          {
            path: ':formCategory',
            element: <NewForms />,
            errorElement: <ErrorPage />,
          },
        ],
      },{
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Forms/New/NewForm/:formTemplateId'
            : 'Forms/New/NewForm/:formTemplateId',
        element: <FormPage />,
        errorElement: <ErrorPage />,
      },{
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Forms/New/Form/:formId'
            : 'Forms/New/Form/:formId',
        element: <FormPage />,
        errorElement: <ErrorPage />,
      },{
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Forms/New/NewForm/:formTemplateId/Continuation/:formId'
            : 'Forms/New/NewForm/:formTemplateId/Continuation/:formId',
        element: <FormPage />,
        errorElement: <ErrorPage />,
      },{
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Forms/Completed'
            : 'Forms/Completed',
        element: <CompletedForms />,
        errorElement: <ErrorPage />,
        children: [
          {
            path: 'category/:categoryId',
            element: <CompletedForms />,
            errorElement: <ErrorPage />,
          },
        ],
      },{
        path:
          decodeJWT()?.role === 'superuser'
            ? 'Mines/:mineId/Forms/Completed/:formId'
            : 'Forms/Completed/:formId',
        element: <FormPage />,
        errorElement: <ErrorPage />,
      }
    ]);
  }, []);

  const url = getPageNamesFromUrl(location.pathname);
  useEffect(() => {
    if (url !== 'unknownpage') {
      mixpanel.track('Page View', {
        Start_Time: new Date().toLocaleString(),
        Page_Name: url,
        MshaId: decoded?.mshaId,
      });
    }

    const pageLoadTime = Date.now();
    const handlePageExit = () => {
      const exitTime = Date.now();
      const timeOnPage = (exitTime - pageLoadTime) / 1000;
      if (url !== 'unknownpage') {
        mixpanel.track('Page Exit', {
          Exit_Time: new Date().toLocaleString(),
          OnPage_time: timeOnPage,
          Page_Name: url,
          MshaId: decoded?.mshaId,
        });
      }
    };

    window.addEventListener('beforeunload', handlePageExit);
    return () => {
      handlePageExit();
      window.removeEventListener('beforeunload', handlePageExit);
    };
  }, [location.pathname]);

  if (localStorage.getItem('token') === null) {
    window.location.replace(window.location.origin);
    return <Login />;
  }

  return apiStatus != 'success' ? (
    <div className="h-screen flex flex-col items-center justify-center space-y-2 ">
      <Loader />
      <span className="text-white mx-10">Loading...</span>
    </div>
  ) : (
    <div className="h-screen flex">
      <div
        className={`${
          open ? 'w-[240px]' : 'w-[90px]'
        } bg-[#1A252F] flex flex-col transition-all duration-300`}
      >
        <div className="h-12">
          <NavBar setOpen={setOpen} open={open} />
        </div>

        {/* <div className="flex w-full"> */}
        {/* <div className="h-full h-[calc(100vh-3rem)]"> */}
        <div className="flex-1 overflow-auto">
          <Sidebar setOpen={setOpen} open={open} features={features} />
        </div>
      </div>

      {/* <div className="pb-2 w-full overflow-auto h-[calc(100vh-3rem)]"> */}
      <div className={`flex-1 pb-2 overflow-auto transition-all duration-300`}>
        <div className="min-w-[1051px] w-full mx-auto">
          <Outlet />
        </div>
      </div>
      <ToastContainer
        autoClose={2000}
        hideProgressBar={true}
        newestOnTop={false}
        closeOnClick
        // rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </div>
  );
}
