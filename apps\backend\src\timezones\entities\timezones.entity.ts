import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { Mine } from '../../mines/entities/mine.entity';

@Entity({ name: 'timezones' })
export class Timezones {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column()
  name: string;

  @Column()
  code: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @OneToMany(() => Mine, (mine) => mine.timezone)
  mines: Mine[];
}
