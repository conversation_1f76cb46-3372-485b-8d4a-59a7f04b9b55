import { useEffect, useMemo, useRef, useState } from "react";
import Loader from "../../common/Loader";
import Form from "./Form";
import { useQuery } from "@tanstack/react-query";
import { getPublishedRevision } from "../../../../api/forms/formtemplateapis";
import { getForm } from "../../../../api/forms/formapis";
import { FormInput, Group, Item } from "../types";
import { toast } from "react-toastify";
import { useAddForm, useFindByFormName, useSaveForm, useSubmitForm } from "../../../../services/mutations/formmutations";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { CloseIcon, GenerateIcon } from "../../../../assets/icons/icons";
import { render } from "react-dom";

const FormPage = () => {
  const params = useParams();
  const pageIndexes = params['*']?.split('/') ?? [];
  const continuation = (pageIndexes.length > 0 && pageIndexes.indexOf('Continuation') > -1) ? true : false;
  const inProgress = continuation ? false : params?.formId ? true : false;
  const [currentFormData, setCurrentFormData] = useState<any>();
  const [isLoading, setIsLoading] = useState(false);
	const [saveActive, setSaveActive] = useState(false);
	const [submitActive, setSubmitActive] = useState(false);
	const [editFormNickname, setEditFormNickname] = useState(false);
  const [formNickname, setFormNickname] = useState('');
  const [nicknameError, setNicknameError] = useState('');
  const [currentFormNickname, setCurrentFormNickname] = useState('');
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [readOnly, setReadOnly] = useState(false);
  const [defaults, setDefaults] = useState(false);

  const { state } = useLocation();
  
	const nicknameInputRef = useRef<HTMLInputElement>(null);

  const addForm = useAddForm();
  const saveForm = useSaveForm();
  const submitForm = useSubmitForm();
  const findByFormName = useFindByFormName();

  const { status: templateStatus, data: template, isLoading: templateLoading } = useQuery({
    queryKey: ['form-template'],
    queryFn: () => getPublishedRevision(params?.formTemplateId),
    refetchOnWindowFocus: false,
		enabled: !inProgress || continuation
  });

  const { status: formStatus, data: form, isLoading: formLoading } = useQuery({
    queryKey: ['form'],
    queryFn: () => getForm(Number(params?.formId)),
    refetchOnWindowFocus: false,
		enabled: inProgress || continuation
  });

  useEffect(() => {
    return () => {
      setReadOnly(false);
      setUnsavedChanges(false);
      setCurrentFormNickname('');
      setFormNickname('');
      setNicknameError('');
      setEditFormNickname(false);
      setSubmitActive(false);
      setSaveActive(false);
      setIsLoading(false);
      setCurrentFormData({});
      if(template && template.data) { template.data = null; }
      if(form && form.data) { form.data = null; }
    };
  }, []);

  const navigate = useNavigate();

  useEffect(() => {
    if(templateLoading || formLoading) {
      setIsLoading(true);
    }
    else {
      setIsLoading(false);
    }
  }, [templateLoading, formLoading]);

  useEffect(() => {
    if(unsavedChanges === true) {
      if(currentFormNickname.length > 0) {
        setSaveActive(true);
      }
      else {
        setNicknameError('Document must have a name');
        setSaveActive(false);
      }
      setSubmitActive(false);
    }
    if(unsavedChanges === false) {
      setSaveActive(false);
      if(currentFormNickname.length > 0) {
        setSubmitActive(true);
      }
    }
  }, [unsavedChanges, currentFormNickname]);

  useEffect(() => {
    if(!inProgress && !continuation && template?.data && template?.data?.definition) {
    //if(!inProgress && template?.data && template?.data?.definition) {
      setCurrentFormData(JSON.parse(template?.data?.definition) ?? {});
    }
    if(inProgress && form?.data) {
      if(form?.data?.content) {
        setCurrentFormData(JSON.parse(form?.data?.content) ?? {});
      }
      if(form?.data?.name) {
        setFormNickname(form?.data.name);
        setCurrentFormNickname(form?.data.name ?? '');
        setNicknameError('');
      }
      if(form?.data?.hasOwnProperty('isSubmitted')) {
        setReadOnly(form?.data?.isSubmitted);
        if(!form?.data?.isSubmitted) {
          setSubmitActive(true);
        }
      }
      else {
        setSubmitActive(true);
      }
    }
    if(continuation && template?.data && template?.data?.definition && form?.data) {
      let templateData = JSON.parse(template?.data?.definition) ?? {};
      let formData = JSON.parse(form?.data?.content) ?? {};

      for(let g = 0; g < templateData?.content?.groups.length; g++) {
        let grp = templateData?.content?.groups[g] ?? {};
        for(let i = 0; i < grp?.items.length; i++) {
          let itm = grp.items[i] ?? {};
          if(itm?.properties?.prefill) {
            let itemIndex = formData?.content?.groups[g]?.items.findIndex((ti:any) => ti.id === itm?.properties?.prefill);
            if(formData?.content?.groups[g]?.items[itemIndex]?.answers) {
              let answers = formData?.content?.groups[g]?.items[itemIndex]?.answers;
              let newAnswers:any = {};
              for(const [key, value] of Object.entries(answers)) {
                let tempKey = `question-${itm?.properties?.prefill}`;
                let tail = key.split('-')[key.split('-').length - 1];
                tempKey = tempKey.split('-item-').length > 1 ? `${tempKey.split('-item-')[0]}-${tempKey.split('-item-')[1]}` : tempKey;
                let tempKeyArr = tempKey.split('-');
                tempKeyArr.pop();
                tempKeyArr.push(tail);
                let newKey = tempKeyArr.join('-')
                newAnswers[newKey] = value;
              }
              itm.answers = newAnswers;
            }
            else {
              itm.answer = formData?.content?.groups[g]?.items[itemIndex]?.answer;
            }
          }
        }
      }
      setCurrentFormData(templateData);
    }
  }, [template?.data, form?.data, inProgress, continuation]);
	
	useEffect(() => {
		if(editFormNickname && !readOnly) {
			nicknameInputRef.current?.focus();
		}
	}, [editFormNickname]);

  const lastSaved = useMemo(() => {
    let date = '';
    if(form?.data) {
      date = 'Last Saved On: '
      if(form?.data?.submittedAt) {
        date = 'Submitted On: '
      }
      if(form?.data?.submittedAt || form?.data?.updatedAt || form?.data?.createdAt) {
        let dateStr = form?.data?.submittedAt ?? form?.data?.updatedAt ?? form?.data?.createdAt ?? null;
        if(dateStr) {
          date += `${dateStr.split('T')[0]} ${dateStr.split('T')[1].split('.')[0]}`
        }
      }
    }
    return date;
  }, [form?.data]);

  const formName = useMemo(() => {
    if(!inProgress && template?.data && template?.data.name) { return template?.data.name }
    if(inProgress && form?.data && form?.data?.formTemplate && form?.data?.formTemplate?.name) { return form?.data?.formTemplate?.name }
    return '';
  }, [template?.data, form?.data, inProgress]);

  const formId = useMemo(() => {
    if(inProgress && form?.data && form?.data.id) { return form?.data.id }
    return 'temp-id';
  }, [form?.data, inProgress]);

  const groups = useMemo(() => {
    if(currentFormData && currentFormData?.content && currentFormData?.content?.groups) {
      return JSON.parse(JSON.stringify(currentFormData?.content?.groups)) ?? [];
    }
    return [];
  }, [currentFormData]);

  const items = useMemo(() => {
    return groups.map((grp: Group) => {
      return (
        grp.items.map((item: Item) => {
          return item;
        })
      );
    }).flat();
  }, [groups]);

  useEffect(() => {
    if(!defaults && currentFormData && items && items.length > 0 && state && state.length > 0) {
      const currentItems = JSON.parse(JSON.stringify(items));
      const currentGroups = JSON.parse(JSON.stringify(groups));

      for(let i = 0; i < state.length; i++) {
        let defaultField = state[i];
        let itemIndex = currentItems.findIndex((itm:Item) => itm.id == defaultField.id);
        let itemGroupIndex = currentGroups.findIndex((grp:Group) => grp.id === defaultField.groupId);

        if(itemIndex > -1 && itemGroupIndex > -1) {
          currentItems[itemIndex] = defaultField;
          currentGroups[itemGroupIndex].items = currentItems.filter((itm:Item) => itm.groupId === defaultField.groupId);
        }
      }

      generateDocumentName();
      setDefaults(true);
  
      let newData = JSON.parse(JSON.stringify(currentFormData ?? {}));
      let newContent = newData.content;
      newContent.groups = currentGroups ?? newContent.groups ?? [];
      setCurrentFormData(newData);
      setUnsavedChanges(true);
    }
  }, [currentFormData, items, state]);

  const validationPassed = () => {
    let valid = true;
    let newData = JSON.parse(JSON.stringify(currentFormData ?? {}));
    let newContent = newData.content;

    const newgroups = newContent?.groups ?? [];

    for(let g = 0; g < newgroups.length; g++) {
      let grp = newgroups[g];
      let newitems = grp?.items ?? [];

      for(let i = 0; i < newitems.length; i++) {
        let itm = newitems[i];
        let answer = itm?.answer ?? null;
        let required = itm?.properties?.requiredfield ?? false;

        if(required && !answer) {
          itm.valid = false;
          valid = false;
        }
      }
    }
    if(!valid) {
      setCurrentFormData(newData);
    }
    return valid;
  };

  async function handleSaveButtonClick() {
    const data:any = {
      name: currentFormNickname,
      content: JSON.stringify(currentFormData),
    };

    if(!inProgress && template?.data) {
      data.formTemplateId = template?.data.formTemplateId;
      data.formTemplateDefinitionId = template?.data.formTemplateDefinitionId;
    }

    if(formId == 'temp-id') {
      try {
        const res:any = await addForm.mutateAsync(data);
        if(res?.status == 200 || res?.status == 201) {
          toast.success('Form succesfully saved.');
          setSaveActive(false);
          if(res?.data?.id) {
            setUnsavedChanges(false);
            navigate(
              params?.mineId
              ? `/app/Mines/${params?.mineId}/Forms/New/Form/${res?.data?.id}`
              : `/app/Forms/New/Form/${res?.data?.id}`
            );
          }
        }
      } catch(err: any) {
        toast.success('There was an issue saving the form.');
        console.log(err);
      }
    }
    else {
      data.id = formId;
      try {
        const res:any = await saveForm.mutateAsync(data);
        if(res?.status == 200 || res?.status == 201) {
          toast.success('Form succesfully saved.');
          setUnsavedChanges(false);
        }
      } catch(err: any) {
        toast.success('There was an issue saving the form.');
        console.log(err);
      }
    }
  }

  async function handleSaveFormNickname(nickname:string) {
    const newNickname = nickname.trim();
    if(newNickname.trim() !== '') {
      const resp = await findByFormName.mutateAsync(newNickname);
      if(resp?.data?.id && resp?.data?.id != formId) {
        setNicknameError('Document name already exists');
        setUnsavedChanges(false);
      }
      else {
        setEditFormNickname(false);
        if(newNickname !== formNickname) {
          setFormNickname(newNickname);
          setUnsavedChanges(true);
        }
      }
    }
    else {
      setNicknameError('Document must have a name');
    }
  }

  function saveInputValue(formInput: FormInput) {
    const currentItems = JSON.parse(JSON.stringify(items));
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const activeItemIndex = currentItems.findIndex((itm:Item) => itm.id === formInput.item.id);
    const itemGroupIndex = currentGroups.findIndex((grp:Group) => grp.id === formInput.item.groupId);
    const item = currentItems[activeItemIndex];

    let itemValue:any = formInput.value;
    if(item.properties && item.properties.type == 'DropDown') {
      itemValue = {
        itemId: formInput.value.itemId,
        answer: formInput.value.answer
      }
    }
    
    if(formInput.questionId) {
      if(!item.answers) {
        item.answers = {};
      }
      item.answers[formInput.questionId] = itemValue;
    }
    else {
      item.answer = itemValue;
    }

    delete item.valid;
    currentItems[activeItemIndex] = item;
    currentGroups[itemGroupIndex].items = currentItems.filter((itm:Item) => itm.groupId === item.groupId);
    
    let newData = JSON.parse(JSON.stringify(currentFormData ?? {}));
    let newContent = newData.content;
    newContent.groups = currentGroups ?? newContent.groups ?? [];
    setCurrentFormData(newData);
    setUnsavedChanges(true);
  }

  const generateDocumentName = () => {
    let docName = formName;
    for(let i = 0; i < state.length; i++) {
      const defaultField = state[i];
      if(defaultField?.answer?.answer) {
        docName += ` - ${defaultField.answer.answer}`;
      }
      else if(defaultField?.answer) {
        docName += ` - ${defaultField.answer}`;
      }
    }
    
    setNicknameError('');
    setCurrentFormNickname(docName);
  }

  return (
      <div className="m-auto">
        <div className="m-auto sticky top-0 z-30 agBreakup2">
          <div className="xl:px-10 2xl:px-16 pt-2 flex flex-column justify-between align-center border-b-[1px] border-[#80c2fe]">
            <div className="">
              <h6 className="pt-2 font-bold text-white text-[32px]">
                {formName.length <= 0 ?
                  'New Form' : `${formName}`
                }
              </h6>
            </div>

            <div className="">
              <h6 className="pt-2 font-bold text-white text-[32px] text-right">
                Forms
              </h6>
            </div>
          </div>
        </div>

        <div className="sticky">
          {isLoading ? (
            <div className="w-full h-full flex flex-col ">
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex flex-col ">
              <div className="bg-[#2c5c8bf6] xl:px-10 2xl:px-16 w-full m-auto sm:grid-cols-3 p-2 sticky top-16 z-30">
                <div className="flex justify-between my-2 items-center">
                  {(currentFormNickname.length > 0 && !editFormNickname && !readOnly) &&
                    <h6
                      className="font-semibold text-[20px] text-white cursor-text"
                      onClick={() => setEditFormNickname(true)}
                    >
                      {currentFormNickname}
                    </h6>
                  }
                  {readOnly &&
                    <h6 className="font-semibold text-[20px] text-white">
                      {currentFormNickname}
                    </h6>
                  }
                  {((currentFormNickname.length <= 0 || editFormNickname) && !readOnly) &&
                  <div>
                      <div className="w-96 p-0 m-0 flex items-center">
                        <button
                          title="Click to generate document name"
                          className="w-1/12 bg-gray-300 p-1.5 rounded-l text-black h-8 border-r border-gray-400"
                          onClick={(() => generateDocumentName())}>
                          <GenerateIcon className="h-5 w-5 opacity-60 hover:opacity-100" />
                        </button>
                        <input
                          ref={nicknameInputRef}
                          value={currentFormNickname ?? ''}
                          className='bg-gray-200 p-1.5 text-black w-10/12 h-8'
                          placeholder="Enter Document Name"
                          onChange={(e) => {
                            setNicknameError('');
                            setCurrentFormNickname(e.currentTarget.value ?? '');
                          }}
                          onFocus={() => {
                            setEditFormNickname(true);
                          }}
                          onBlur={(e) => {
                            if(e.currentTarget.value.length <= 0) {
                              setSaveActive(false);
                              setNicknameError('Document must have a name');
                            }
                          }}
                          onKeyDown={(e) => {
                            if(e.key === 'Enter') {
                              handleSaveFormNickname(e.currentTarget.value);
                            }
                            if(e.key === 'Escape') {
                              if(formNickname.length > 0) {
                                setNicknameError('');
                                setEditFormNickname(false);
                                setCurrentFormNickname(formNickname);
                              }
                              else {
                                setNicknameError('Document must have a name');
                              }
                            }
                          }}
                        />
                        <button
                          title="Click to finish editing document name"
                          className={`
                            w-1/12 bg-gray-300 p-1.5 rounded-r text-black h-8 border-l border-gray-400
                            ${currentFormNickname.length > 0 ? 'cursor-pointer' : 'cursor-default'}
                          `}
                          onClick={() => {
                            if(currentFormNickname.length > 0) {
                              handleSaveFormNickname(currentFormNickname)
                            }
                          }}>
                          <CloseIcon stroke="black" className={`h-5 w-5 opacity-40 ${currentFormNickname.length > 0 ? 'hover:opacity-80' : ''}`} />
                        </button>
                      </div>
                      <p className="text-start text-xs text-red-500 font-semibold pt-1">
                        {nicknameError}
                      </p>
                    </div>
                  }
                  <div className="float-end relative">
                    <div className="flex items-center">
                      {lastSaved &&
                        <div className="w-fit text-xs">{lastSaved}</div>
                      }
                      {!readOnly &&
                        <button
                          id="save_form"
                          title="Click to save form"
                          className={`
                            text-white bg-[#29547c]/25 border-[1px] font-medium rounded-lg text-sm py-1 px-3 ml-3 mr-1 text-center items-center
                            ${saveActive ? 'hover:bg-[#29547c]/50 border-[#4AA8FE] hover:border-[#4AA8FE]/75' : 'cursor-default opacity-50 disabled'}
                          `}
                          onClick={() => {
                            if(saveActive) {
                              handleSaveButtonClick();
                            }
                          }}
                        >
                          Save
                        </button>
                      }
                      {!readOnly &&
                        <button
                          id="submit_form"
                          title="Click to submit form"
                          className={`
                            text-white font-medium rounded-lg text-sm bg-[#4AA8FE]
                            py-1 px-3 mx-1 text-center items-center
                            ${submitActive ? ' hover:bg-[#4AA8FE]/75' : 'cursor-default opacity-50 disabled'}
                          `}
                          onClick={async () => {
                            if(submitActive) {
                              try {
                                if(validationPassed()) {
                                  const res:any = await submitForm.mutateAsync(params?.formId);
                                  if(res?.status == 200 || res?.status == 201) {
                                    toast.success('Form succesfully submitted.');
                                    setSubmitActive(false);
                                    navigate(
                                      params?.mineId
                                      ? `/app/Mines/${params?.mineId}/Forms/New`
                                      : `/app/Forms/New`
                                    );
                                  }
                                }
                                else {
                                  toast.error('Please fill out missing fields.');
                                }
                              } catch(err: any) {
                                toast.success('There was an issue submitting the form.');
                                console.log(err);
                              }
                            }
                          }}
                        >
                          Submit
                        </button>
                      }
                    </div>
                  </div>
                </div>
              </div>
              <div className={`max-w-screen w-full overflow-y flex flex-col min-h-96`}>
                {currentFormData &&
                  <Form
                    saveInputValue={saveInputValue}
                    groups={groups}
                    items={items}
                    readOnly={readOnly}
                  />
                }
              </div>
            </div>
          )}
        </div>
      </div>
  );
};

export default FormPage;