import { Test, TestingModule } from '@nestjs/testing';
import { FormTemplatesController } from './form_templates.controller';
import { FormTemplatesService } from './form_templates.service';
import { CreateFormTemplateDto } from './dto/create-form-template.dto';
import { FormTemplate } from './entities/form_template.entity';
import { UpdateFormTemplateDto } from './dto/update-form-template.dto';

describe('FormTemplatesController', () => {
  let controller: FormTemplatesController;

  const mockFormTemplateService = {
    create: jest.fn(),
    findAll: jest.fn(),
    getFormTemplatesByMineId: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FormTemplatesController],
      providers: [
        {
          provide: FormTemplatesService,
          useValue: mockFormTemplateService,
      }]
    }).compile();

    controller = module.get<FormTemplatesController>(FormTemplatesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('create => should create a new form template by the given data', async () => {
    // arrange
    const createFormTemplateDto = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    } as CreateFormTemplateDto;

    const formTemplate = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    } as FormTemplate;

    jest.spyOn(mockFormTemplateService, 'create').mockReturnValue(formTemplate);

    // act
    const result = await controller.create(createFormTemplateDto);

    // assert
    expect(mockFormTemplateService.create).toHaveBeenCalled();
    expect(mockFormTemplateService.create).toHaveBeenCalledWith(createFormTemplateDto);
    expect(result).toEqual(formTemplate);
  });

  it('findAll => should return an array of form templates', async () => {
    // arrange
    const formTemplate = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    };
    let mineId = 1;
    const req = { user: { mineId: mineId } };
    const formTemplates = [formTemplate];
    jest.spyOn(mockFormTemplateService, 'findAll').mockReturnValue(formTemplates);

    // act
    const result = await controller.findAll(req);

    // assert
    expect(mockFormTemplateService.findAll).toHaveBeenCalled();
    expect(result).toEqual(formTemplates);
  });

  it('findOne => should find a form template by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const formTemplate = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    };

    jest.spyOn(mockFormTemplateService, 'findOne').mockReturnValue(formTemplate);

    // act
    const result = await controller.findOne(id);

    // assert
    expect(mockFormTemplateService.findOne).toHaveBeenCalled();
    expect(mockFormTemplateService.findOne).toHaveBeenCalledWith(+id);
    expect(result).toEqual(formTemplate);
  });

  it('update => should find a form template by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateFormTemplateDto = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    } as UpdateFormTemplateDto;
    const formTemplate = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Shift Production Report',
      description: 'Shift Production Report',
    };

    jest.spyOn(mockFormTemplateService, 'update').mockReturnValue(formTemplate);

    // act
    const result = await controller.update(id, updateFormTemplateDto);

    // assert
    expect(mockFormTemplateService.update).toHaveBeenCalled();
    expect(mockFormTemplateService.update).toHaveBeenCalledWith(+id, updateFormTemplateDto);
    expect(result).toEqual(formTemplate);
  });

  it('remove => should find a form template by a given id, remove and then return number of affected rows', async () => {
    // arrange
    const id = 1;
    const formTemplate = {
      mineId: 1,
      formCategoryId: 1,
      name: 'Section Production Report',
      description: 'Section Production Report',
    };

    jest.spyOn(mockFormTemplateService, 'remove').mockReturnValue(formTemplate);

    // act
    const result = await controller.remove(id);

    // assert
    expect(mockFormTemplateService.remove).toHaveBeenCalled();
    expect(mockFormTemplateService.remove).toHaveBeenCalledWith(+id);
    expect(result).toEqual(formTemplate);
  });
});
