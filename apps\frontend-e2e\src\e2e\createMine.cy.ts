describe('frontend-e2e', () => {
  beforeEach(() => {
    cy.on('uncaught:exception', (e) => {
      if (e.message.includes('EOF is not defined')) {
        return false;
      }
    });

    cy.visit('http://localhost:4200/app/');
    cy.wait(1000);
    cy.fixture('loginData').then((loginData) => {
      const data = loginData.users[0];
      cy.get('#username').type(data.username);
      cy.wait(1000);
      cy.get('#password').type(data.password);
      cy.wait(1000);

      // cy.contains('Sign In').click();
      cy.get('#signin').click();
      cy.wait(1000);
      cy.url().should('include', '/Dashboard');
      cy.wait(1000);
      cy.get('#setting_button').click();
      cy.wait(1000);
      cy.url().should('include', '/companies');
      cy.get('#addMineButton').click();
      cy.wait(1000);
    });
  });

  it('login', () => {
    cy.get('#createMineButton').click();
    cy.wait(1000);
    cy.fixture('createMineData').then((createMineData) => {
      const data = createMineData.mine[0];

      // cy.get('#username').type(data.invalidUserName);
      // cy.wait(1000);
      cy.get('#companyName').type(data.invalidComName);
      cy.wait(1000);
      cy.get('#mineName').type(data.mineName);
      cy.wait(1000);
      cy.get('#mineCode').type(data.mineCode);
      cy.wait(1000);
      cy.get('#mineLocation').type(data.mineLocation);
      cy.wait(1000);
      cy.get('#firstName').type(data.firstName);
      cy.wait(1000);
      cy.get('#lastName').type(data.lastName);
      cy.wait(1000);
      cy.get('#emailAddress').type(data.email);
      cy.wait(1000);
      cy.get('#confirmEmailAddress').type(data.cEmail);
      cy.wait(1000);
      cy.get('#createMineButton').click();
      cy.wait(2000);

      cy.get('#companyName').clear().type(data.validComName);
      cy.wait(2000);
      cy.get('#createMineButton').click();
      cy.wait(2000);

      cy.get('#mineName').clear().type(data.validMineName);
      cy.wait(2000);
      cy.get('#createMineButton').click();
      cy.wait(2000);

      cy.get('#mineCode').clear().type(data.validMineCode);
      cy.wait(2000);
      cy.get('#createMineButton').click();
      cy.wait(2000);

      cy.get('#emailAddress').clear().type(data.ValidEmail);
      cy.wait(2000);
      cy.get('#createMineButton').click();
      cy.wait(2000);

      cy.get('#confirmEmailAddress').clear().type(data.valdCEmail);
      cy.wait(2000);
      cy.get('#createMineButton').click();
      cy.wait(3000);

      cy.get('#username').clear().type(data.invalidUserName);
      cy.wait(1000);
      cy.get('#createMineButton').click();
      cy.wait(3000);

      cy.get('#username').clear().type(data.validUserName);
      cy.wait(1000);
      cy.get('#createMineButton').click();
      cy.wait(3000);

      cy.get('#search').type(data.invalidText);
      cy.wait(2000);
      cy.get('#search').should('have.value', data.invalidText);
      cy.wait(2000);
      cy.get('#search').clear();
      cy.get('#search').type(data.validText);
      cy.get('#search').should('have.value', data.validText);
    });
  });
});
