import Plot from 'react-plotly.js';
import ProgressBar from '../../common/ProgressBar';
import { ProdDownArrow } from '../../../../assets/icons/icons';
import { ProdUpArrow } from '../../../../assets/icons/icons';
import { getReportCompareData } from '../../../../api/production/productionReportapi';
import { useQuery } from '@tanstack/react-query';
import decodeJWT from '../../../../utils/jwtdecoder';
import { IwtEnv } from '../../../../api/apiClient';
import { useOutletContext } from 'react-router';
import { useCallback, useEffect, useState } from 'react';
import { getDateRangerForUser } from '../../../../api/users/dateSelectionapis';
import { shortcuts } from '../../../../utils/constant';
import Loader from '../../common/Loader';
import dayjs from 'dayjs';
import { Link, useParams, useSearchParams } from 'react-router-dom';
declare var iwtEnv: IwtEnv;
import GraphSkeleton from '../../common/GraphSkeleton';
import { useSaveDateRange } from '../../../../services/mutations/usermutations';
import { toast } from 'react-toastify';

export default function ProdReportCompare() {
  const [searchText, setLastUpdatedDate, filterData] = useOutletContext() as [
    string,
    any,
    any
  ];

  const saveDateRangeMutation = useSaveDateRange();

  const params = useParams();
  const getLastMonthToPast30Days = () => {
    const today = dayjs();

    const startDate = today.subtract(1, 'month');

    const endDate = startDate.subtract(30, 'days');

    return { startDate, endDate };
  };

  let selectedDateRange = getLastMonthToPast30Days();

  const [dateRangeSelected, setDateRangeSelected] = useState({
    startDate: selectedDateRange.endDate,
    endDate: selectedDateRange.startDate,
  });

  const startDate = dayjs(filterData?.compareFromDate?.startDate).format(
    'YYYY-MM-DD'
  );

  const endDate = dayjs(filterData?.compareFromDate?.endDate).format('YYYY-MM-DD');

  const startDate3 = dayjs(filterData?.compareToDate?.startDate).format('YYYY-MM-DD');

  const endDate3 = dayjs(filterData?.compareToDate?.endDate).format('YYYY-MM-DD');

  function calculateDaysDifference(startDate: any, endDate: any) {
    if (!startDate || !endDate) {
      return 0;
    }

    const start = dayjs(startDate);
    const end = dayjs(endDate);

    return Math.abs(end.diff(start, 'day'));
  }

  const daysDifference = calculateDaysDifference(
    filterData?.compareFromDate?.startDate,
    filterData?.compareFromDate?.endDate
  );

  const daysDifference2 = calculateDaysDifference(
    filterData?.compareToDate?.startDate,
    filterData?.compareToDate?.endDate
  );

  const reportCompareQuery = useQuery({
    queryKey: ['UpdatedComapreData', filterData],
    refetchOnWindowFocus: false,
    queryFn: () =>
      getReportCompareData(
        Number(decodeJWT()?.mineid),
        startDate,
        endDate,
        startDate3,
        endDate3
      ),
  });

  let startDate1 = dayjs(startDate) ? dayjs(startDate).format('MMM DD') : '';
  let endDate1 = dayjs(endDate) ? dayjs(endDate).format('MMM DD') : '';
  let startDate2 = dayjs(startDate3) ? dayjs(startDate3).format('MMM DD') : '';
  let endDate2 = dayjs(endDate3) ? dayjs(endDate3).format('MMM DD') : '';

  const {
    data: reportCompareData,
    refetch,
    status: apiStatus,
  } = reportCompareQuery;

  const sectionNames = reportCompareData?.data?.sectionMinedGraphData?.map(
    (i: any) => i.sectionName
  );

  const yAxisValues1 = reportCompareData?.data?.sectionMinedGraphData?.map(
    (i: any) => (i.feetMinedDataRange1 === 0 ? null : i.feetMinedDataRange1)
  );

  const yAxisValues2 = reportCompareData?.data?.sectionMinedGraphData?.map(
    (i: any) => (i.feetMinedDataRange2 === 0 ? null : i.feetMinedDataRange2)
  );

  const yAxisValues3 = reportCompareData?.data?.sectionMinedGraphData?.map(
    (i: any) => (i.feetMinedDataRange3 === 0 ? null : i.feetMinedDataRange3)
  );

  const yAxisValues4 = reportCompareData?.data?.sectionMinedGraphData?.map(
    (i: any) => (i.feetMinedDataRange4 === 0 ? null : i.feetMinedDataRange4)
  );

  const getHighestSummedShiftData = (graphData: any) => {
    const summedValuesById: any = {};

    graphData?.forEach((sectionData: any) => {
      const {
        sectionId,
        feetMinedDataRange1,
        feetMinedDataRange2,
        feetMinedDataRange3,
        feetMinedDataRange4,
      } = sectionData;

      if (summedValuesById[sectionId]) {
        summedValuesById[sectionId].feetMinedDataRange1 += feetMinedDataRange1;
        summedValuesById[sectionId].feetMinedDataRange2 += feetMinedDataRange2;
        summedValuesById[sectionId].feetMinedDataRange3 += feetMinedDataRange3;
        summedValuesById[sectionId].feetMinedDataRange4 += feetMinedDataRange4;
      } else {
        summedValuesById[sectionId] = {
          feetMinedDataRange1,
          feetMinedDataRange2,
          feetMinedDataRange3,
          feetMinedDataRange4,
        };
      }
    });
    let highestFeetMinedDataRange1 = 0;
    let highestFeetMinedDataRange2 = 0;
    let highestFeetMinedDataRange3 = 0;
    let highestFeetMinedDataRange4 = 0;

    for (const sectionId in summedValuesById) {
      const {
        feetMinedDataRange1,
        feetMinedDataRange2,
        feetMinedDataRange3,
        feetMinedDataRange4,
      } = summedValuesById[sectionId];
      if (feetMinedDataRange1 > highestFeetMinedDataRange1) {
        highestFeetMinedDataRange1 = feetMinedDataRange1;
      }
      if (feetMinedDataRange2 > highestFeetMinedDataRange2) {
        highestFeetMinedDataRange2 = feetMinedDataRange2;
      }
      if (feetMinedDataRange3 > highestFeetMinedDataRange3) {
        highestFeetMinedDataRange3 = feetMinedDataRange3;
      }
      if (feetMinedDataRange4 > highestFeetMinedDataRange4) {
        highestFeetMinedDataRange4 = feetMinedDataRange4;
      }
    }

    const highestValue = Math.max(
      highestFeetMinedDataRange1,
      highestFeetMinedDataRange2,
      highestFeetMinedDataRange3,
      highestFeetMinedDataRange4
    );

    return highestValue;
  };

  const highestSummedShiftValue = getHighestSummedShiftData(
    reportCompareData?.data?.sectionMinedGraphData
  );

  function barGap() {
    const screenWidth = window.innerWidth;
    if (screenWidth > 1536) {
      return -40;
    } else if (screenWidth > 1280) {
      return 0.6;
    }
  }

  function marginRight() {
    const screenWidth = window.innerWidth;
    if (screenWidth > 1536) {
      return 70;
    } else if (screenWidth > 1280) {
      return 70;
    }
  }
  const barGraphLayout = {
    barmode: 'group',
    barcornerradius: 20,
    bargroupgap: barGap(),
    bargap: barGap(),
    xaxis: {
      title: 'Working Section',
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      range:
        !reportCompareData?.data || sectionNames.length == 0 ? [0, 10] : [],
    },
    yaxis: {
      title: {
        text: 'Feet Mined',
        standoff: 25,
      },
      side: 'right',
      range: [-1, highestSummedShiftValue + 900],
      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 0.5)',
      zeroline: true,
      zerolinecolor: 'rgba(128, 194, 254, 1)',
      showgrid: false,
    },
    showlegend: false,
    margin: { t: 30, l: 65, b: 80, r: marginRight(), pad: 10 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
    dragmode: false,
  };

  let trace1 = {
    x: sectionNames,
    y: yAxisValues1 ? yAxisValues1 : '',
    type: 'bar',
    marker: {
      color: 'rgba(255, 194, 94, 1)',
    },
    color: 'rgba(255, 208, 132, 1)',
    width: [0.2, 0.2, 0.2, 0.2, 0.2, 0.2],
    text: yAxisValues1,
    textposition: 'outside',
    textfont: { color: 'white' },
    offsetgroup: '1.5',
    hoverinfo: 'none',
  };

  let trace2 = {
    x: sectionNames,
    y: yAxisValues2 ? yAxisValues2 : '',
    type: 'bar',
    marker: {
      color: 'rgba(38, 31, 82, 1)',
    },
    line: {
      color: 'rgb(255, 177, 50,100%)',
    },
    width: [0.2, 0.2, 0.2, 0.2, 0.2, 0.2],
    text: yAxisValues2,
    textposition: 'outside',
    textfont: { color: 'white' },
    offsetgroup: '3',
    hoverinfo: 'none',
  };

  const compareChartData = [
    {
      x: reportCompareData?.data?.feetMinedGraphData?.feetMinedGraphData2?.coordinates?.map(
        (ele: any) => dayjs(ele?.date).format('DD MMM')
      ),
      y:
        reportCompareData?.data?.feetMinedGraphData?.feetMinedGraphData2?.coordinates?.map(
          (ele: any) => ele?.feetMined
        ) ?? [],

      mode: 'markers+lines',
      fill: 'tozeroy',
      xaxis: 'x2',
      marker: {
        color: 'rgba(56, 134, 206, 0.44)/20',
        size: 6,
      },
      fillcolor: 'rgba(56, 134, 206, 0.44)/20',
      type: 'scatter',
      name: '',
    },
    {
      x: reportCompareData?.data?.feetMinedGraphData?.feetMinedGraphData1?.coordinates?.map(
        (ele: any) => dayjs(ele?.date).format('DD MMM')
      ),
      y:
        reportCompareData?.data?.feetMinedGraphData?.feetMinedGraphData1?.coordinates?.map(
          (ele: any) => ele?.feetMined
        ) ?? [],
      mode: 'markers+lines',
      fill: 'tozeroy',

      name: '',
      fillcolor: 'transparent',
      marker: { color: 'rgba(255,194,94,100%)', size: 6 },
    },
  ];

  const numbers =
    compareChartData[0].y?.length != 0 ? compareChartData[0].y : [];
  const numbers2 =
    compareChartData[1].y?.length != 0 ? compareChartData[1].y : [];
  const greaterNumber = Math.max(
    reportCompareData?.data?.averageGoal1,
    reportCompareData?.data?.averageGoal2,
    ...numbers,
    ...numbers2
  );

  const layoutCompare = {
    xaxis: {
      color: 'rgba(255,194,94,100%)',
      gridcolor: 'rgba(0, 0, 0, 0)',
      type: !reportCompareData?.data ? 'date' : '',
      tickformat: !reportCompareData?.data ? '%b %d' : '',
      tickangle: 45,
      title: {
        text: 'Days',
        standoff: 15,
        font: {
          color: 'white',
        },
      },
      range: !reportCompareData?.data ? [startDate, endDate] : [],
      zeroline: false,
    },
    xaxis2: {
      color: 'white',
      type: !reportCompareData?.data ? 'date' : '',
      tickformat: !reportCompareData?.data ? '%b %d' : '',
      tickangle: 45,
      overlaying: 'x',
      gridcolor: 'rgba(0, 0, 0, 0)',
      side: 'bottom',
      ticklen: 60,
      range: !reportCompareData?.data
        ? [
            filterData?.compareToDate?.startDate,
            filterData?.compareToDate?.endDate,
          ]
        : [],
    },
    yaxis: {
      color: 'white',
      title: {
        text: 'Feet Mined',
        standoff: 20,
      },
      fixedrange: true,
      range: !reportCompareData?.data
        ? [-1, 11]
        : [-1, greaterNumber < 10 ? 11 : greaterNumber + greaterNumber / 4],
    },
    shapes:
      reportCompareData?.data?.averageGoal1 ==
      reportCompareData?.data?.averageGoal2
        ? [
            reportCompareData?.data?.averageGoal1 && {
              type: 'line',
              xref: 'paper',
              x0: 0,
              y0: reportCompareData?.data?.averageGoal1,
              x1: 1,
              y1: reportCompareData?.data?.averageGoal1,
              line: {
                color: 'rgba(255,194,94,100%)',
                width: 3,
                dash: 'dot',
              },
            },
            reportCompareData?.data?.averageGoal2 && {
              type: 'line',
              xref: 'paper',
              x0: 0,
              y0: reportCompareData?.data?.averageGoal2,
              x1: 1,
              y1: reportCompareData?.data?.averageGoal2,
              line: { color: 'rgba(38, 31, 82, 1)', width: 3, dash: 'dash' },
            },
          ]
        : [
            reportCompareData?.data?.averageGoal1 && {
              type: 'line',
              xref: 'paper',
              x0: 0,
              y0: reportCompareData?.data?.averageGoal1,
              x1: 1,
              y1: reportCompareData?.data?.averageGoal1,
              line: {
                color: 'rgba(255,194,94,100%)',
                width: 3,
                dash: 'dash',
              },
            },
            reportCompareData?.data?.averageGoal2 && {
              type: 'line',
              xref: 'paper',
              x0: 0,
              y0: reportCompareData?.data?.averageGoal2,
              x1: 1,
              y1: reportCompareData?.data?.averageGoal2,
              line: { color: 'rgba(38, 31, 82, 1)', width: 3, dash: 'dash' },
            },
          ],
    showlegend: false,
    margin: { t: 0, l: 70, b: 115, r: 30, pad: 10 },
    automargin: true,
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    dragmode: 'pan',
    // height: 310,
    modeBarButtonsToRemove: ['lasso2d', 'select2d'],
    displaylogo: false,
  };

  const barGraphData = [trace1, trace2];

  const reportCompareHeading = [
    { heading: 'Mined' },
    { heading: 'Feet Mined/Hour' },
    { heading: 'Belt Availability' },
    { heading: 'Total Downtime' },
    { heading: 'Most Downtime' },
    { heading: 'Total Alerts' },
  ];

  const lastReport = dayjs(
    reportCompareData?.data?.activityMetrics2?.lastReport
  );

  function calculateAchieveGoal(averageGoal: any, achievedGoal: any) {
    if (
      (achievedGoal >= 10 && achievedGoal < 100) ||
      (achievedGoal <= -10 && achievedGoal > -100)
    ) {
      return achievedGoal;
    } else {
      let percentageGoal = (achievedGoal / averageGoal) * 100;
      return percentageGoal;
    }
  }

  let inspectionPercentage = calculateAchieveGoal(
    reportCompareData?.data?.averageGoal1,
    reportCompareData?.data?.activityMetrics1?.inspectionDowntime?.feets
  );
  let inspectionPercentage2 = calculateAchieveGoal(
    reportCompareData?.data?.averageGoal2,
    reportCompareData?.data?.activityMetrics2?.inspectionDowntime?.feets
  );

  let plannedPercentage = calculateAchieveGoal(
    reportCompareData?.data?.averageGoal2,
    reportCompareData?.data?.activityMetrics1?.plannedDowntime?.feets
  );
  let plannedPercentage2 = calculateAchieveGoal(
    reportCompareData?.data?.averageGoal2,
    reportCompareData?.data?.activityMetrics2?.plannedDowntime?.feets
  );

  let unplannedPercentage = calculateAchieveGoal(
    reportCompareData?.data?.averageGoal1,
    reportCompareData?.data?.activityMetrics1?.unplannedDowntime?.feets
  );

  let unplannedPercentage2 = calculateAchieveGoal(
    reportCompareData?.data?.averageGoal2,
    reportCompareData?.data?.activityMetrics2?.unplannedDowntime?.feets
  );

  let ontimePercentage = calculateAchieveGoal(
    reportCompareData?.data?.averageGoal1,
    reportCompareData?.data?.activityMetrics1?.onTime?.feets
  );

  let ontimePercentage2 = calculateAchieveGoal(
    reportCompareData?.data?.averageGoal2,
    reportCompareData?.data?.activityMetrics2?.onTime?.feets
  );

  return (
    <div>
      <div className="grid  grid-cols-6 gap-4 pt-6 w-full h-full px-[4%] mx-auto bg-[#2c5c8bf6] pb-6">
        {reportCompareHeading?.map((heading: any, index: any) => (
          <div
            className={`h-[110%] border-[#4AA8FE] rounded-lg border-[1px] ${
              heading?.heading == 'Belt Availability' ||
              heading?.heading == 'Total Alerts'
                ? 'border-dashed  border-[1px] '
                : 'border-[1px]'
            } `}
          >
            <div
              className={`${
                heading?.heading == 'Belt Availability' ||
                heading?.heading == 'Total Alerts'
                  ? 'blur-sm'
                  : ''
              }`}
              style={{ overflow: 'hidden' }}
            >
              <div className="text-center text-[14px] text-[#FFD084] font-normal pt-3">
                {heading.heading}
                {index === 0 && (
                  <>
                    <div className="text-center text-[25px] text-white font-bold pt-1">
                      {!reportCompareData?.data?.mined?.delta ||
                      reportCompareData?.data?.mined?.delta === 0
                        ? '0'
                        : reportCompareData?.data?.mined?.delta > 0
                        ? '+' + `${reportCompareData?.data?.mined?.delta}`
                        : `${reportCompareData?.data?.mined?.delta}`}
                      <span className="text-white text-[12px] mx-1 font-normal">
                        {!reportCompareData?.data?.mined?.delta ? '%' : '%'}
                      </span>
                    </div>
                    <div className="text-white text-xs mx-1 font-normal">
                      <span className="text-start ">
                        {!reportCompareData?.data?.mined?.totalFeetMined1 ||
                        reportCompareData?.data?.mined?.totalFeetMined1 === null
                          ? '0'
                          : reportCompareData?.data?.mined?.totalFeetMined1 +
                            ' ' +
                            reportCompareData?.data?.mined?.minedUnit}
                      </span>
                      <span className="ml-2 mr-2 ">
                        {(!reportCompareData?.data?.mined?.totalFeetMined1 &&
                          !reportCompareData?.data?.mined?.totalFeetMined2) ||
                        (reportCompareData?.data?.mined?.totalFeetMined1 ===
                          0 &&
                          reportCompareData?.data?.mined?.totalFeetMined2 === 0)
                          ? 'VS'
                          : 'VS'}
                      </span>
                      <span className="underline">
                        {!reportCompareData?.data?.mined?.totalFeetMined2 ||
                        reportCompareData?.data?.mined?.totalFeetMined2 === 0
                          ? '0'
                          : reportCompareData?.data?.mined?.totalFeetMined2 +
                            ' ' +
                            reportCompareData?.data?.mined?.minedUnit}
                      </span>
                    </div>
                  </>
                )}
                {index === 1 && (
                  <>
                    <div className="text-center text-[25px] text-white font-bold pt-1">
                      {!reportCompareData?.data?.feetMinedPerHour?.delta ||
                      reportCompareData?.data?.feetMinedPerHour?.delta === 0
                        ? '0'
                        : reportCompareData?.data?.feetMinedPerHour?.delta > 0
                        ? '+' +
                          `${reportCompareData?.data?.feetMinedPerHour?.delta}`
                        : `${reportCompareData?.data?.feetMinedPerHour?.delta}`}
                      <span className="text-white text-[12px] mx-1 font-normal">
                        {!reportCompareData?.data?.feetMinedPerHour?.delta
                          ? '%'
                          : '%'}
                      </span>
                    </div>
                    <div className="text-white text-[11px] mx-1 font-normal">
                      <span className="text-start underline">
                        {!reportCompareData?.data?.feetMinedPerHour
                          ?.feetMinedPerHour1 ||
                        reportCompareData?.data?.feetMinedPerHour
                          ?.feetMinedPerHour1 === 0
                          ? '0'
                          : reportCompareData?.data?.feetMinedPerHour
                              ?.feetMinedPerHour1 +
                            ' ' +
                            reportCompareData?.data?.feetMinedPerHour?.feetMinedPerHourUnit.toLowerCase()}
                      </span>
                      <span className="ml-2 mr-2">
                        {(!reportCompareData?.data?.feetMinedPerHour
                          ?.feetMinedPerHour1 &&
                          !reportCompareData?.data?.feetMinedPerHour
                            ?.feetMinedPerHour2) ||
                        (reportCompareData?.data?.feetMinedPerHour
                          ?.feetMinedPerHour1 === 0 &&
                          reportCompareData?.data?.feetMinedPerHour
                            ?.feetMinedPerHour2 === 0)
                          ? 'VS'
                          : 'VS'}
                      </span>
                      <span className="underline">
                        {!reportCompareData?.data?.feetMinedPerHour
                          ?.feetMinedPerHour2 ||
                        reportCompareData?.data?.feetMinedPerHour
                          ?.feetMinedPerHour2 === 0
                          ? '0'
                          : reportCompareData?.data?.feetMinedPerHour
                              ?.feetMinedPerHour2 +
                            ' ' +
                            reportCompareData?.data?.feetMinedPerHour?.feetMinedPerHourUnit.toLowerCase()}
                      </span>
                    </div>
                  </>
                )}
                {index === 2 && (
                  <>
                    <div className="text-center text-[25px] text-white font-bold pt-1 ">
                      {0.0}
                      <span className="text-white text-[12px] mx-1 font-normal">
                        {'%'}
                      </span>
                    </div>
                    <div className="text-white text-[11px] mx-1 font-normal">
                      <span className="text-start ">{'0.0'}</span>
                      <span className="ml-2 mr-2">{'VS'}</span>
                      <span>{'0.0'}</span>
                    </div>
                  </>
                )}
                {index === 3 && (
                  <>
                    <div className="text-center text-[25px] text-white font-bold pt-1">
                      {!reportCompareData?.data?.totalDowntime?.delta ||
                      reportCompareData?.data?.totalDowntime?.delta === null ||
                      reportCompareData?.data?.totalDowntime?.delta === 0
                        ? '0'
                        : reportCompareData?.data?.totalDowntime?.delta > 0
                        ? '+' +
                          `${reportCompareData?.data?.totalDowntime?.delta}`
                        : `${reportCompareData?.data?.totalDowntime?.delta}`}
                      <span className="text-white text-[12px] mx-1 font-normal">
                        {!reportCompareData?.data?.totalDowntime?.delta ||
                        reportCompareData?.data?.totalDowntime?.delta ===
                          null ||
                        reportCompareData?.data?.totalDowntime?.delta === 0
                          ? '%'
                          : '%'}
                      </span>
                    </div>
                    <div className="text-white text-[11px] mx-1 font-normal">
                      <span className="text-start ">
                        {!reportCompareData?.data?.totalDowntime
                          ?.total_downtime1 ||
                        reportCompareData?.data?.totalDowntime
                          ?.total_downtime1 === 0
                          ? '0'
                          : reportCompareData?.data?.totalDowntime
                              ?.total_downtime1 +
                            ' ' +
                            reportCompareData?.data?.totalDowntime
                              ?.totalDowntimeUnit}
                      </span>
                      <span className="pl-2 pr-2">
                        {!reportCompareData?.data?.totalDowntime
                          ?.total_downtime1 ||
                        reportCompareData?.data?.totalDowntime
                          ?.total_downtime1 === 0 ||
                        !reportCompareData?.data?.totalDowntime
                          ?.total_downtime2 ||
                        reportCompareData?.data?.totalDowntime
                          ?.total_downtime2 === 0
                          ? 'VS'
                          : 'VS'}
                      </span>
                      <span className="underline">
                        {!reportCompareData?.data?.totalDowntime
                          ?.total_downtime2 ||
                        reportCompareData?.data?.totalDowntime
                          ?.total_downtime2 === 0
                          ? '0'
                          : reportCompareData?.data?.totalDowntime
                              ?.total_downtime2 +
                            ' ' +
                            reportCompareData?.data?.totalDowntime
                              ?.totalDowntimeUnit}
                      </span>
                    </div>
                  </>
                )}
                {index === 4 && (
                  <>
                    <div className="text-center text-[25px] text-white font-bold pt-1">
                      {!reportCompareData?.data?.mostDowntime?.most_downtime1 ||
                      reportCompareData?.data?.mostDowntime?.most_downtime1 ===
                        null
                        ? '-'
                        : reportCompareData?.data?.mostDowntime?.most_downtime1}
                      <span className="text-white text-[12px] mx-1 font-normal">
                        {!reportCompareData?.data?.mostDowntime
                          ?.most_downtime1 ||
                        reportCompareData?.data?.mostDowntime
                          ?.most_downtime1 === null
                          ? ' '
                          : reportCompareData?.data?.mostDowntime?.most_downtime_meridiem1.toLowerCase()}
                      </span>
                    </div>
                    <div className="text-white text-[11px] mx-1 font-normal">
                      <span className="text-start ">{''}</span>
                      <span className="ml-2 mr-2">
                        {!reportCompareData?.data?.mostDowntime
                          ?.most_downtime2 ||
                        reportCompareData?.data?.mostDowntime
                          ?.most_downtime2 === null
                          ? ''
                          : 'VS'}
                      </span>
                      <span className="underline">
                        {!reportCompareData?.data?.mostDowntime
                          ?.most_downtime2 ||
                        reportCompareData?.data?.mostDowntime
                          ?.most_downtime2 === null
                          ? ''
                          : reportCompareData?.data?.mostDowntime
                              ?.most_downtime2 +
                            ' ' +
                            reportCompareData?.data?.mostDowntime?.most_downtime_meridiem2.toLowerCase()}
                      </span>
                    </div>
                  </>
                )}
                {index === 5 && (
                  <>
                    <div className="text-center text-[25px] text-white font-bold pt-1">
                      {'0'}
                      <span className="text-white text-[12px] mx-1 font-normal">
                        {'%'}
                      </span>
                    </div>
                    <div className="text-white text-[11px] mx-1 font-normal">
                      <span className="text-start ">{0}</span>
                      <span className="ml-2 mr-2">{'VS'}</span>
                      <span>{0}</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex px-[4%] pt-4 ">
        <div className="w-1/2">
          {Object?.keys(
            apiStatus == 'success'
              ? reportCompareData?.data?.feetMinedGraphData
              : {}
          )?.length === 0 ? (
            <GraphSkeleton
              className="2xl:!h-[35.5vh] xl:!h-[45vh] xl:w-[100%]"
              apiStatus={apiStatus}
            />
          ) : (
            <Plot
              data={compareChartData}
              layout={layoutCompare}
              style={{ width: '100%' }}
              className="!h-[55vh]"
              config={{
                displayModeBar: false,
                scrollZoom: true,
                responsive: true,
                dragmode: false,
              }}
            />
          )}
        </div>
        <div className="w-1/2 ">
          <div className="w-full">
            {Object?.keys(
              apiStatus == 'success'
                ? reportCompareData?.data?.sectionMinedGraphData
                : {}
            )?.length === 0 ? (
              <GraphSkeleton
                className="2xl:!h-[35.5vh] xl:!h-[45vh] ml-11 "
                apiStatus={apiStatus}
              />
            ) : (
              <Plot
                style={{ width: '100%' }}
                className="xl:!h-[49vh] 2xl:!h-[51vh]"
                data={barGraphData}
                layout={barGraphLayout}
                config={{
                  displayModeBar: false,
                  responsive: true,
                }}
              />
            )}
          </div>
        </div>
      </div>

      <div className="px-[4%] pt-8 flex">
        <div className=" 2xl:w-[55%] xl:w-[56%]  h-[430px] activityColor rounded-md ">
          <div className="flex">
            <div className="font-bold text-2xl text-white pt-4 pl-4 w-1/2">
              Activity
            </div>
          </div>
          <div className="flex w-full pt-2">
            <div className="w-[37%]"></div>
            <div className="flex  w-[65%] justify-between">
              <span className="text-white text-xs pl-4  2xl:pl-12 font-normal ">{`${
                startDate1 + ' - ' + endDate1
              }`}</span>
              <span>{''}</span>
              <span className="text-white text-xs pr-4 font-normal">{`${
                startDate2 + ' - ' + endDate2
              }`}</span>
            </div>
          </div>

          <div className="pt-3">
            <div className="flex">
              <div className="pl-4 text-white font-normal text-base w-[37%]">
                Inspection
              </div>
              <div className="w-[65%] 2xl:pl-16">
                <div className="flex flex-col">
                  {/* First Set */}
                  <div className="flex items-center w-full pl-2">
                    <div
                      className={`font-normal text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics1
                          ?.inspectionDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics1
                          ?.inspectionDowntime?.feets < 0
                          ? 'text-[#96FB60]'
                          : !reportCompareData?.data?.activityMetrics1
                              ?.inspectionDowntime?.feets ||
                            reportCompareData?.data?.activityMetrics1
                              ?.inspectionDowntime?.feets === 0
                          ? 'text-white'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '58px', textAlign: 'right' }}
                    >
                      {reportCompareData?.data?.activityMetrics1
                        ?.inspectionDowntime?.feets &&
                      reportCompareData?.data?.activityMetrics1
                        ?.inspectionDowntime?.feets > 0
                        ? '-' +
                          reportCompareData?.data?.activityMetrics1
                            ?.inspectionDowntime?.feets +
                          'ft'
                        : !reportCompareData?.data?.activityMetrics1
                            ?.inspectionDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics1
                            ?.inspectionDowntime?.feets === 0
                        ? '0ft'
                        : reportCompareData?.data?.activityMetrics1
                            ?.inspectionDowntime?.feets + 'ft'}
                    </div>
                    <div className="pt-0 pl-2 pr-2">
                      {reportCompareData?.data?.activityMetrics1
                        ?.inspectionDowntime?.feets &&
                      reportCompareData?.data?.activityMetrics1
                        ?.inspectionDowntime?.feets > 0 ? (
                        <ProdDownArrow />
                      ) : !reportCompareData?.data?.activityMetrics1
                          ?.inspectionDowntime?.feets ||
                        reportCompareData?.data?.activityMetrics1
                          ?.inspectionDowntime?.feets === 0 ? (
                        ''
                      ) : (
                        <ProdUpArrow />
                      )}
                    </div>
                    <div
                      className={`${
                        reportCompareData?.data?.activityMetrics1
                          ?.inspectionDowntime?.feets === 0
                          ? 'pt-2 2xl:pl-2'
                          : 'pt-2 pr-4 '
                      }`}
                    >
                      <ProgressBar
                        value={
                          inspectionPercentage > 0
                            ? '-' + inspectionPercentage.toFixed(2)
                            : reportCompareData?.data?.activityMetrics1
                                ?.inspectionDowntime?.feets === 0
                            ? ''
                            : inspectionPercentage.toFixed(2)
                        }
                      />
                    </div>
                  </div>

                  {/* Second Set */}
                  <div className="flex items-center w-full pl-4">
                    <div
                      className={`font-normal text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics1
                          ?.inspectionDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics1
                          ?.inspectionDowntime?.feets < 0
                          ? 'text-[#96FB60]'
                          : reportCompareData?.data?.activityMetrics1
                              ?.inspectionDowntime?.feets === 0
                          ? 'text-white'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'left' }}
                    >
                      {''}
                    </div>
                    <div className="pt-2 pl-2 pr-2">{''}</div>

                    <div className="pt-2 pl-2">
                      <ProgressBar
                        value={
                          inspectionPercentage2 > 0
                            ? '-' + inspectionPercentage2.toFixed(2)
                            : reportCompareData?.data?.activityMetrics2
                                ?.inspectionDowntime?.feets === 0
                            ? ''
                            : inspectionPercentage2.toFixed(2)
                        }
                      />
                    </div>

                    <div
                      className={` flex font-normal text-xs pt-1 pl-2  ${
                        reportCompareData?.data?.activityMetrics2
                          ?.inspectionDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics2
                          ?.inspectionDowntime?.feets < 0
                          ? 'text-[#96FB60]'
                          : !reportCompareData?.data?.activityMetrics2
                              ?.inspectionDowntime?.feets ||
                            reportCompareData?.data?.activityMetrics2
                              ?.inspectionDowntime?.feets === 0
                          ? 'text-white 2xl:pl-3.5'
                          : 'text-[#FE4A6A]'
                      } $`}
                      style={{ width: '50px', textAlign: 'right' }}
                    >
                      {reportCompareData?.data?.activityMetrics2
                        ?.inspectionDowntime?.feets &&
                      reportCompareData?.data?.activityMetrics2
                        ?.inspectionDowntime?.feets > 0
                        ? '-' +
                          reportCompareData?.data?.activityMetrics2
                            ?.inspectionDowntime?.feets +
                          'ft'
                        : !reportCompareData?.data?.activityMetrics2
                            ?.inspectionDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics2
                            ?.inspectionDowntime?.feets === 0
                        ? '0ft'
                        : reportCompareData?.data?.activityMetrics2
                            ?.inspectionDowntime?.feets + 'ft'}

                      <div className="pt-1.5 pl-2">
                        {reportCompareData?.data?.activityMetrics2
                          ?.inspectionDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics2
                          ?.inspectionDowntime?.feets > 0 ? (
                          <ProdDownArrow />
                        ) : !reportCompareData?.data?.activityMetrics2
                            ?.inspectionDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics2
                            ?.inspectionDowntime?.feets === 0 ? (
                          ''
                        ) : (
                          <ProdUpArrow />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <div className="flex">
              <div className="pl-4 text-white font-normal text-base w-[37%]">
                Planned Downtime
              </div>
              <div className="w-[65%] 2xl:pl-16">
                <div className="flex flex-col">
                  {/* First Set */}
                  <div className="flex items-center w-full pl-2">
                    <div
                      className={`font-normal text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics1
                          ?.plannedDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics1
                          ?.plannedDowntime?.feets < 0
                          ? 'text-[#96FB60]'
                          : !reportCompareData?.data?.activityMetrics1
                              ?.plannedDowntime?.feets ||
                            reportCompareData?.data?.activityMetrics1
                              ?.plannedDowntime?.feets === 0
                          ? 'text-white'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '58px', textAlign: 'right' }}
                    >
                      {reportCompareData?.data?.activityMetrics1
                        ?.plannedDowntime?.feets > 0
                        ? '-' +
                          reportCompareData?.data?.activityMetrics1
                            ?.plannedDowntime?.feets +
                          'ft'
                        : !reportCompareData?.data?.activityMetrics1
                            ?.plannedDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics1
                            ?.plannedDowntime?.feets === 0
                        ? '0ft'
                        : reportCompareData?.data?.activityMetrics1
                            ?.plannedDowntime?.feets + 'ft'}
                    </div>
                    <div className="pt-0 pl-2 pr-2">
                      {reportCompareData?.data?.activityMetrics1
                        ?.plannedDowntime?.feets &&
                      reportCompareData?.data?.activityMetrics1?.plannedDowntime
                        ?.feets > 0 ? (
                        <ProdDownArrow />
                      ) : !reportCompareData?.data?.activityMetrics1
                          ?.plannedDowntime?.feets ||
                        reportCompareData?.data?.activityMetrics1
                          ?.plannedDowntime?.feets === 0 ? (
                        ''
                      ) : (
                        <ProdUpArrow />
                      )}
                    </div>
                    <div
                      className={`${
                        reportCompareData?.data?.activityMetrics1
                          ?.plannedDowntime?.feets === 0
                          ? 'pt-2 2xl:pl-2'
                          : 'pt-2 pr-4'
                      }`}
                    >
                      <ProgressBar
                        value={
                          plannedPercentage > 0
                            ? '-' + plannedPercentage.toFixed(2)
                            : reportCompareData?.data?.activityMetrics1
                                ?.plannedDowntime?.feets === 0
                            ? ''
                            : plannedPercentage.toFixed(2)
                        }
                      />
                    </div>
                  </div>

                  {/* Second Set */}
                  <div className="flex items-center w-full pl-4">
                    <div
                      className={`font-normal text-xs pt-1 pl-2  ${
                        (reportCompareData?.data?.activityMetrics2
                          ?.plannedDowntime?.feets &&
                          reportCompareData?.data?.activityMetrics2
                            ?.plannedDowntime?.feets < 0) ||
                        reportCompareData?.data?.activityMetrics2
                          ?.plannedDowntime?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'left' }}
                    >
                      {''}
                    </div>
                    <div className="pt-2 pl-2 pr-2">{''}</div>

                    <div className="pt-2 pl-2">
                      <ProgressBar
                        value={
                          plannedPercentage2 > 0
                            ? '-' + plannedPercentage2.toFixed(2)
                            : reportCompareData?.data?.activityMetrics2
                                ?.plannedDowntime?.feets === 0
                            ? '0'
                            : plannedPercentage2.toFixed(2)
                        }
                      />
                    </div>

                    <div
                      className={`flex font-normal text-xs pl-2 pt-1  ${
                        reportCompareData?.data?.activityMetrics2
                          ?.plannedDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics2
                          ?.plannedDowntime?.feets < 0
                          ? 'text-[#96FB60]'
                          : !reportCompareData?.data?.activityMetrics2
                              ?.plannedDowntime?.feets ||
                            reportCompareData?.data?.activityMetrics2
                              ?.plannedDowntime?.feets === 0
                          ? 'text-white 2xl:pl-3.5'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'right' }}
                    >
                      {reportCompareData?.data?.activityMetrics2
                        ?.plannedDowntime?.feets &&
                      reportCompareData?.data?.activityMetrics2?.plannedDowntime
                        ?.feets > 0
                        ? '-' +
                          reportCompareData?.data?.activityMetrics2
                            ?.plannedDowntime?.feets +
                          'ft'
                        : !reportCompareData?.data?.activityMetrics2
                            ?.plannedDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics2
                            ?.plannedDowntime?.feets === 0
                        ? '0ft'
                        : reportCompareData?.data?.activityMetrics2
                            ?.plannedDowntime?.feets + 'ft'}

                      <div className={'pt-1.5 pl-2'}>
                        {reportCompareData?.data?.activityMetrics2
                          ?.plannedDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics2
                          ?.plannedDowntime?.feets > 0 ? (
                          <ProdDownArrow />
                        ) : !reportCompareData?.data?.activityMetrics2
                            ?.plannedDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics2
                            ?.plannedDowntime?.feets === 0 ? (
                          ''
                        ) : (
                          <ProdUpArrow />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <div className="flex">
              <div className="pl-4 text-white font-normal text-base w-[37%]">
                Unplanned Downtime
              </div>
              <div className="w-[65%]  2xl:pl-16">
                <div className="flex flex-col">
                  {/* First Set */}
                  <div className="flex items-center w-full pl-2">
                    <div
                      className={`font-normal text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics1
                          ?.unplannedDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics1
                          ?.unplannedDowntime?.feets < 0
                          ? 'text-[#96FB60]'
                          : !reportCompareData?.data?.activityMetrics1
                              ?.unplannedDowntime?.feets ||
                            reportCompareData?.data?.activityMetrics1
                              ?.unplannedDowntime?.feets === 0
                          ? 'text-white '
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '58px', textAlign: 'right' }}
                    >
                      {reportCompareData?.data?.activityMetrics1
                        ?.unplannedDowntime?.feets &&
                      reportCompareData?.data?.activityMetrics1
                        ?.unplannedDowntime?.feets > 0
                        ? '-' +
                          reportCompareData?.data?.activityMetrics1
                            ?.unplannedDowntime?.feets +
                          'ft'
                        : !reportCompareData?.data?.activityMetrics1
                            ?.unplannedDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics1
                            ?.unplannedDowntime?.feets === 0
                        ? '0ft'
                        : reportCompareData?.data?.activityMetrics1
                            ?.unplannedDowntime?.feets + 'ft'}
                    </div>
                    <div className="pt-0 pl-2 pr-2">
                      {reportCompareData?.data?.activityMetrics1
                        ?.unplannedDowntime?.feets > 0 ? (
                        <ProdDownArrow />
                      ) : !reportCompareData?.data?.activityMetrics1
                          ?.unplannedDowntime?.feets ||
                        reportCompareData?.data?.activityMetrics1
                          ?.unplannedDowntime?.feets === 0 ? (
                        ''
                      ) : (
                        <ProdUpArrow />
                      )}
                    </div>
                    <div
                      className={`${
                        reportCompareData?.data?.activityMetrics1
                          ?.unplannedDowntime?.feets === 0
                          ? 'pt-2 pl-2'
                          : 'pt-2 pr-4'
                      }`}
                    >
                      <ProgressBar
                        value={
                          unplannedPercentage > 0
                            ? '-' + unplannedPercentage.toFixed(2)
                            : reportCompareData?.data?.activityMetrics1
                                ?.unplannedDowntime?.feets === 0
                            ? ''
                            : unplannedPercentage.toFixed(2)
                        }
                      />
                    </div>
                  </div>

                  {/* Second Set */}
                  <div className="flex items-center w-full pl-4">
                    <div
                      className={`font-normal text-xs pt-1 pl-2  ${
                        (reportCompareData?.data?.activityMetrics1
                          ?.unplannedDowntime?.feets &&
                          reportCompareData?.data?.activityMetrics1
                            ?.unplannedDowntime?.feets < 0) ||
                        reportCompareData?.data?.activityMetrics1
                          ?.unplannedDowntime?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'left' }}
                    >
                      {''}
                    </div>
                    <div className="pt-2 pl-2 pr-2">{''}</div>

                    <div className="pt-2 pl-2">
                      <ProgressBar
                        value={
                          unplannedPercentage2 > 0
                            ? '-' + unplannedPercentage2.toFixed(2)
                            : reportCompareData?.data?.activityMetrics2
                                ?.unplannedDowntime?.feets === 0
                            ? ''
                            : unplannedPercentage2.toFixed(2)
                        }
                      />
                    </div>

                    <div
                      className={`flex font-normal pl-2 text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics2
                          ?.unplannedDowntime?.feets &&
                        reportCompareData?.data?.activityMetrics2
                          ?.unplannedDowntime?.feets < 0
                          ? 'text-[#96FB60]'
                          : !reportCompareData?.data?.activityMetrics2
                              ?.unplannedDowntime?.feets ||
                            reportCompareData?.data?.activityMetrics2
                              ?.unplannedDowntime?.feets === 0
                          ? 'text-white 2xl:pl-3.5'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'right' }}
                    >
                      {reportCompareData?.data?.activityMetrics2
                        ?.unplannedDowntime?.feets &&
                      reportCompareData?.data?.activityMetrics2
                        ?.unplannedDowntime?.feets > 0
                        ? '-' +
                          reportCompareData?.data?.activityMetrics2
                            ?.unplannedDowntime?.feets +
                          'ft'
                        : !reportCompareData?.data?.activityMetrics2
                            ?.unplannedDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics2
                            ?.unplannedDowntime?.feets === 0
                        ? '0ft'
                        : reportCompareData?.data?.activityMetrics2
                            ?.unplannedDowntime?.feets + 'ft'}
                      <div className="pt-1.5 pl-2 ">
                        {reportCompareData?.data?.activityMetrics2
                          ?.unplannedDowntime?.feets > 0 ? (
                          <ProdDownArrow />
                        ) : !reportCompareData?.data?.activityMetrics2
                            ?.unplannedDowntime?.feets ||
                          reportCompareData?.data?.activityMetrics2
                            ?.unplannedDowntime?.feets === 0 ? (
                          ''
                        ) : (
                          <ProdUpArrow />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <div className="flex">
              <div className="pl-4 text-white font-normal text-base w-[37%]">
                On time
              </div>
              <div className="w-[65%]  2xl:pl-16">
                <div className="flex flex-col">
                  {/* First Set */}
                  <div className="flex items-center w-full pl-2">
                    <div
                      className={`font-normal text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets &&
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets > 0
                          ? 'text-[#96FB60]'
                          : !reportCompareData?.data?.activityMetrics1?.onTime
                              ?.feets ||
                            reportCompareData?.data?.activityMetrics1?.onTime
                              ?.feets === 0
                          ? 'text-white'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '58px', textAlign: 'right' }}
                    >
                      {reportCompareData?.data?.activityMetrics1?.onTime
                        ?.feets &&
                      reportCompareData?.data?.activityMetrics1?.onTime?.feets >
                        0
                        ? '+' +
                          reportCompareData?.data?.activityMetrics1?.onTime
                            ?.feets +
                          'ft'
                        : !reportCompareData?.data?.activityMetrics1?.onTime
                            ?.feets ||
                          reportCompareData?.data?.activityMetrics1?.onTime
                            ?.feets === 0
                        ? '0ft'
                        : reportCompareData?.data?.activityMetrics1?.onTime
                            ?.feets + 'ft'}
                    </div>
                    <div className="pt-0 pl-2 pr-2">
                      {reportCompareData?.data?.activityMetrics1?.onTime
                        ?.feets &&
                      reportCompareData?.data?.activityMetrics1?.onTime?.feets <
                        0 ? (
                        <ProdDownArrow />
                      ) : !reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets ||
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets === 0 ? (
                        ''
                      ) : (
                        <ProdUpArrow />
                      )}
                    </div>
                    <div
                      className={`${
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets === 0
                          ? 'pt-2 2xl:pl-2'
                          : 'pt-2 pr-4'
                      }`}
                    >
                      <ProgressBar
                        value={
                          ontimePercentage > 0
                            ? '+' + ontimePercentage.toFixed(2)
                            : reportCompareData?.data?.activityMetrics1?.onTime
                                ?.feets === 0
                            ? ''
                            : ontimePercentage.toFixed(2)
                        }
                      />
                    </div>
                  </div>

                  {/* Second Set */}
                  <div className="flex items-center w-full pl-4 pr-4">
                    <div
                      className={`font-normal text-xs pt-1 pl-2  ${
                        (reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets &&
                          reportCompareData?.data?.activityMetrics1?.onTime
                            ?.feets > 0) ||
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'left' }}
                    >
                      {''}
                    </div>
                    <div className="pt-2 pl-2 pr-2">{''}</div>

                    <div className="pt-2 pl-2">
                      <ProgressBar
                        value={
                          ontimePercentage2 > 0
                            ? '+' + ontimePercentage2.toFixed(2)
                            : reportCompareData?.data?.activityMetrics2?.onTime
                                ?.feets === 0
                            ? ''
                            : ontimePercentage2.toFixed(2)
                        }
                      />
                    </div>

                    <div
                      className={`flex font-normal text-xs pt-1 pl-2  ${
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets &&
                        reportCompareData?.data?.activityMetrics2?.onTime
                          ?.feets > 0
                          ? 'text-[#96FB60]'
                          : !reportCompareData?.data?.activityMetrics2?.onTime
                              ?.feets ||
                            reportCompareData?.data?.activityMetrics2?.onTime
                              ?.feets === 0
                          ? 'text-white 2xl:pl-3.5'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'right' }}
                    >
                      {reportCompareData?.data?.activityMetrics1?.onTime
                        ?.feets &&
                      reportCompareData?.data?.activityMetrics2?.onTime?.feets >
                        0
                        ? '+' +
                          reportCompareData?.data?.activityMetrics2?.onTime
                            ?.feets +
                          'ft'
                        : !reportCompareData?.data?.activityMetrics2?.onTime
                            ?.feets ||
                          reportCompareData?.data?.activityMetrics2?.onTime
                            ?.feets === 0
                        ? '0ft'
                        : reportCompareData?.data?.activityMetrics2?.onTime
                            ?.feets + 'ft'}
                      <div className="pt-1.5 pl-2">
                        {reportCompareData?.data?.activityMetrics2?.onTime
                          ?.feets < 0 ? (
                          <ProdDownArrow />
                        ) : !reportCompareData?.data?.activityMetrics2?.onTime
                            ?.feets ||
                          reportCompareData?.data?.activityMetrics2?.onTime
                            ?.feets === 0 ? (
                          ''
                        ) : (
                          <ProdUpArrow />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="pt-4 blur-sm">
            <div className="flex">
              <div className="pl-4 text-white font-normal text-base w-[37%]">
                Section Equipment
              </div>
              <div className="w-[65%]  2xl:pl-16">
                <div className="flex flex-col">
                  {/* First Set */}
                  <div className="flex items-center w-full pl-2">
                    <div
                      className={`font-normal text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '58px', textAlign: 'right' }}
                    >
                      {'1234.56'}
                    </div>
                    <div className="pt-0 pl-2 pr-2">
                      {reportCompareData?.data?.activityMetrics1?.onTime
                        ?.feets < 0 ? (
                        <ProdDownArrow />
                      ) : (
                        <ProdUpArrow />
                      )}
                    </div>
                    <div className="pt-2 pr-4 ">
                      <ProgressBar value={-23} />
                    </div>
                  </div>

                  {/* Second Set */}
                  <div className="flex items-center w-full pl-4">
                    <div
                      className={` flex font-normal text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets > 0 ||
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'left' }}
                    >
                      {''}
                    </div>
                    <div className="pt-2 pl-2 pr-2">{''}</div>

                    <div className="pt-2 pl-2">
                      <ProgressBar value={-35} />
                    </div>

                    <div
                      className={`flex font-normal text-xs pl-2 pt-1  ${
                        reportCompareData?.data?.activityMetrics2?.onTime
                          ?.feets > 0 ||
                        reportCompareData?.data?.activityMetrics2?.onTime
                          ?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'right' }}
                    >
                      {'1234.56ft'}
                      <div className="pt-1.5 pl-2 ">
                        {reportCompareData?.data?.activityMetrics2?.onTime
                          ?.feets < 0 ? (
                          <ProdDownArrow />
                        ) : (
                          <ProdUpArrow />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className=" pt-4 blur-sm">
            <div className="flex">
              <div className="pl-4 text-white font-normal text-base w-[37%]">
                Belt Availability
              </div>
              <div className="w-[65%]  2xl:pl-16">
                <div className="flex flex-col">
                  {/* First Set */}
                  <div className="flex items-center w-full pl-2">
                    <div
                      className={`font-normal text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets > 0 ||
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '58px', textAlign: 'right' }}
                    >
                      {'12'}
                    </div>
                    <div className="pt-2 pl-2 pr-2">
                      {reportCompareData?.data?.activityMetrics1?.onTime
                        ?.feets < 0 ? (
                        <ProdDownArrow />
                      ) : (
                        <ProdUpArrow />
                      )}
                    </div>
                    <div className="pt-0 pr-4 ">
                      <ProgressBar
                        value={
                          inspectionPercentage > 0
                            ? '-' + inspectionPercentage.toFixed(2)
                            : inspectionPercentage.toFixed(2)
                        }
                      />
                    </div>
                  </div>

                  {/* Second Set */}
                  <div className="flex items-center w-full pl-4">
                    <div
                      className={`font-normal text-xs pt-1 pl-2  ${
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets > 0 ||
                        reportCompareData?.data?.activityMetrics1?.onTime
                          ?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'left' }}
                    >
                      {''}
                    </div>
                    <div className="pt-2 pl-2 pr-2">{''}</div>

                    <div className="pt-2 pl-2">
                      <ProgressBar value={-30} />
                    </div>

                    <div
                      className={`flex font-normal pl-2  text-xs pt-1  ${
                        reportCompareData?.data?.activityMetrics2?.onTime
                          ?.feets > 0 ||
                        reportCompareData?.data?.activityMetrics2?.onTime
                          ?.feets === 0
                          ? 'text-[#96FB60]'
                          : 'text-[#FE4A6A]'
                      }`}
                      style={{ width: '50px', textAlign: 'right' }}
                    >
                      {'10'}
                      <div className="pt-1.5 pl-2">
                        {reportCompareData?.data?.activityMetrics2?.onTime
                          ?.feets < 0 ? (
                          <ProdDownArrow />
                        ) : (
                          <ProdUpArrow />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className=" w-[50%] insightsColor h-[260px] xl:ml-8 2xl:ml-10 rounded-sm border-dashed border-[#4AA8FE] border-[1px] unselectable">
          <div className="blur-sm">
            <div className="font-bold text-2xl text-white pt-4 pl-8">
              Insights
            </div>
            <div className="text-white text-sm px-4 py-2 pl-14">
              <ul className="list-disc">
                <li>
                  Inspections are taking longer than normal. Shorter inspection
                  windows would +32 feet.
                </li>
                <li>Section 2 started 18 minutes late. Production -12 feet.</li>
                <li>
                  Unplanned downtime is 20% higher so far today than normal
                  resulting in -52 feet of lost production.
                </li>
                <li>
                  Belt 7A was down for 32 minutes resulting in 22 lost feet.
                  Consider decreasing inspection intervals to ensure higher
                  up-time.
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
