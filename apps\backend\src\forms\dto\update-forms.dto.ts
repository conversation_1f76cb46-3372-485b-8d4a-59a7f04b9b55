import {
  IsDefined,
  <PERSON>NotEmpty,
  <PERSON>N<PERSON><PERSON>,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateFormDto {
  @IsString({ message: 'Name must be a string' })
  @Length(1, 200, {
    message: 'Name length must be between 1 and 200 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  content: string;

  isProcessed?: boolean;
  isDelete?: boolean;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  updatedBy: number;
}
