import Table, { ColumnDef } from '../common/Table';
import { Hammer, Offline, Sun } from '../../../assets/icons/icons';
import { Link, useOutletContext, useParams } from 'react-router-dom';
import Card from './Card';
import { getLocationLiveCheckInsData } from '../../../api/location/locationdashboardapi';
import decodeJWT from '../../../utils/jwtdecoder';
import { useQueries, useQuery } from '@tanstack/react-query';
import { IwtEnv } from '../../../api/apiClient';
import { useEffect } from 'react';
import Loader from '../common/Loader';
import { escapeRegExp } from '../../../utils/constant';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
declare var iwtEnv: IwtEnv;

export default function Checkins() {
  const [searchText, setLastUpdatedDate] = useOutletContext() as [string, any];
  const params = useParams();

  const regex = new RegExp(`(${escapeRegExp(searchText)})`, 'i');

  const {
    data: checkinsData,
    refetch: refetchLiveCheckinsData,
    isLoading,
  } = useQuery({
    queryKey: ['location-live-checkins'],
    queryFn: () => getLocationLiveCheckInsData(Number(decodeJWT()?.mineid)),
    refetchInterval: Number(iwtEnv?.timeIntervalForApi) * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    setLastUpdatedDate(checkinsData?.data?.lastUpdatedTs);
  }, [checkinsData?.data?.lastUpdatedTs]);

  const AgColumns: ColumnDef[] = [
    {
      key: 'minerName',
      label: 'Miner',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden" title={row?.minerName}>
          <Link
            className="underline"
            to={
              params?.mineId
                ? `/app/Mines/${params?.mineId}/Location/report/personnel/${row?.minerId}`
                : `/app/Location/report/personnel/${row?.minerId}`
            }
            onClick={() => {
              mixpanel.track('Personnel Click', {
                Page_Name: getPageNamesFromUrl(params['*']),
                MshaId: decodeJWT()?.mshaId,
                MinerId: row?.minerId,
              });
            }}
            dangerouslySetInnerHTML={{
              __html: row?.minerName?.replace(
                regex,
                "<span style='color: #FFD084;'>$1</span>"
              ),
            }}
          ></Link>
        </div>
      ),
    },
    {
      key: 'checkedInTime',
      label: 'Checked In',
      type: 'time',
      render: (row: any) => (
        <div
          // className={
          //   row.ugCC == 'R'
          //     ? 'text-[#FE4A6A]'
          //     : row.ugCC == 'G'
          //     ? 'text-[#96FB60]'
          //     : ''
          // }
          title={row?.checkedInTime ? row?.checkedInTime : '-'}
        >
          <p className="inline">{row?.checkedInTime}</p>
          {/* {row.ugCC == 'R' ? <DownArrow /> : row.ugCC == 'G' ? <UpArrow /> : ''} */}
        </div>
      ),
    },
  ];

  const UgColumns: ColumnDef[] = [
    {
      key: 'minerName',
      label: 'Miner',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden" title={row?.minerName}>
          <Link
            className="underline"
            to={
              params?.mineId
                ? `/app/Mines/${params?.mineId}/Location/report/personnel/${row?.minerId}`
                : `/app/Location/report/personnel/${row?.minerId}`
            }
            onClick={() => {
              mixpanel.track('Personnel Click', {
                Page_Name: getPageNamesFromUrl(params['*']),
                MshaId: decodeJWT()?.mshaId,
                MinerId: row?.minerId,
              });
            }}
            dangerouslySetInnerHTML={{
              __html: row?.minerName?.replace(
                regex,
                "<span style='color: #FFD084;'>$1</span>"
              ),
            }}
          ></Link>
        </div>
      ),
    },
    {
      key: 'wentUG',
      label: 'Went UG',
      type: 'time',
      render: (row: any) => (
        <div
          // className={
          //   row.ugCC == 'R'
          //     ? 'text-[#FE4A6A]'
          //     : row.ugCC == 'G'
          //     ? 'text-[#96FB60]'
          //     : ''
          // }
          title={row?.wentUG ? row?.wentUG : '-'}
        >
          <p className="inline">{row.wentUG}</p>
          {/* {row.ugCC == 'R' ? <DownArrow /> : row.ugCC == 'G' ? <UpArrow /> : ''} */}
        </div>
      ),
    },
    {
      key: 'currentSection',
      label: 'Current Section',
      type: 'text',
      render: (row: any) => (
        <div
          className="text-ellipsis overflow-hidden w-24"
          title={row?.currentSection}
        >
          {row?.currentSection ? row?.currentSection : '-'}
        </div>
      ),
    },
  ];

  const maxHeight = (length: number, table: string) => {
    if (length <= 2) {
      if (table === 'agBreakup') {
        return 'max-h-80';
      } else {
        return 'max-h-96';
      }
    } else if (length > 2 && length <= 3) {
      return 'max-h-72';
    } else if (length > 3 && length <= 4) {
      return 'max-h-64';
    } else if (length > 4 && length <= 5) {
      return 'max-h-52';
    } else if (length >= 5) {
      return 'max-h-52';
    }
  };

  return (
    <div
      className={`flex flex-col h-screen ${
        checkinsData?.data?.agBreakup?.length <= 0 &&
        checkinsData?.data?.ugBreakup?.length <= 0
          ? 'xl:min-h-[400px] 2xl:min-h-[600px]'
          : ''
      }`}
    >
      {isLoading ? (
        <div>
          <div>
            <div className="flex flex-col flex-grow justify-center items-center h-full pt-8 white">
              {<Loader />}
            </div>
            <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
              Loading....
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="flex-grow flex flex-col pt-8 h-1/2 xl:pl-10 2xl:pl-16 agBreakup">
            <div className="flex flex-grow overflow-hidden h-full pb-5">
              <div className="xl:w-[10%] 2xl:w-[7%]">
                <Card
                  title={checkinsData?.data?.totalAG}
                  subtitle="TOTAL"
                  svgIcon={<Sun />}
                  type="Above Ground"
                />
              </div>
              {(checkinsData?.data?.agBreakup?.length <= 0 ||
                !checkinsData?.data?.totalAG) && (
                <p className="w-full c flex justify-center items-center font-[600] text-white text-[24px] ">
                  No miners are currently above ground
                </p>
              )}
              {checkinsData?.data?.agBreakup?.length > 0 && (
                <div className="flex flex-col flex-grow w-[652px] p-[20px] rounded-[8px] ml-10 bg-[#29588733]">
                  <div className="flex-grow overflow-auto">
                    <Table
                      columns={AgColumns}
                      data={checkinsData?.data?.agBreakup ?? []}
                      searchText={searchText}
                      searchOnColumn="minerName"
                      sortable={true}
                      scrollable={true}
                      backgroundColor={false}
                      dataRenderLimitMdScreen={4}
                      dataRenderLimitLgScreen={5}
                      tableHeightClassMd="max-h-48"
                      tableHeightClassLg={`${maxHeight(
                        checkinsData?.data?.ugBreakup?.length,
                        'agBreakup'
                      )}`}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="flex-grow flex flex-col h-1/2 pt-8 pb-8 xl:pl-10 2xl:pl-16">
            <div className="h-full flex flex-grow overflow-hidden">
              <div className="xl:w-[10%] 2xl:w-[7%]">
                <div className="flex flex-col gap-4">
                  <Card
                    title={checkinsData?.data?.totalUG}
                    subtitle="TOTAL"
                    svgIcon={<Hammer />}
                    type="Below Ground"
                  />
                  <Card
                    title={checkinsData?.data?.totalOffline}
                    subtitle="TOTAL"
                    svgIcon={<Offline />}
                    type="Offline"
                  />
                </div>
              </div>
              {(checkinsData?.data?.ugBreakup?.length <= 0 ||
                !checkinsData?.data?.totalUG) && (
                <p className="w-full h-full flex justify-center items-center font-[600] text-white text-[24px]">
                  No miners are currently underground
                </p>
              )}

              {checkinsData?.data?.ugBreakup?.length > 0 && (
                <div className="flex flex-col flex-grow w-[652px] p-[20px] rounded-[8px] ml-10 bg-[#21496E]">
                  <div className="flex-grow overflow-auto">
                    <Table
                      columns={UgColumns}
                      data={checkinsData?.data?.ugBreakup ?? []}
                      searchText={searchText}
                      searchOnColumn="minerName"
                      sortable={true}
                      scrollable={true}
                      backgroundColor={false}
                      dataRenderLimitMdScreen={4}
                      dataRenderLimitLgScreen={5}
                      tableHeightClassMd="max-h-48"
                      tableHeightClassLg={`${maxHeight(
                        checkinsData?.data?.agBreakup?.length,
                        'ugBreakup'
                      )}`}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
