import dayjs from 'dayjs';
import Plot from 'react-plotly.js';
import Blank<PERSON>hart from '../../../../assets/BlankChart.png';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useState, useEffect } from 'react';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { generateWeeklyTickVals } from '../../../../utils/constant';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isSameOrBefore);

function convertTimeToDecimal(timeString: string): number | undefined {
  const parts: string[] = timeString?.split(' ');
  if (parts?.length !== 4 || parts[1] !== 'hrs' || parts[3] !== 'min') {
    return undefined;
  }
  const hours: number = parseInt(parts[0]);
  const minutes: number = parseInt(parts[2]);
  if (isNaN(hours) || isNaN(minutes)) {
    return undefined;
  }
  const abc = `${hours}.${minutes}`;
  return parseFloat(abc);
}

function createStartDate(dateString: string): Date {
  const date = new Date(dateString);
  date.setHours(0, 0, 0, 0);
  return date;
}

const TotalTimeUG = (props: any) => {
  function calculateIncrementDays(daysDifference: number): number {
    if (daysDifference <= 30) {
      return 1;
    } else if (daysDifference <= 60) {
      return 2;
    } else if (daysDifference <= 90) {
      return 3;
    } else if (daysDifference <= 120) {
      return 4;
    } else if (daysDifference <= 150) {
      return 5;
    } else if (daysDifference <= 180) {
      return 7;
    } else if (daysDifference <= 210) {
      return 9;
    } else if (daysDifference <= 240) {
      return 11;
    } else if (daysDifference <= 270) {
      return 13;
    } else {
      return 15;
    }
  }

  let totalTimeData =
    props.reportData
      ?.map((ele: any) => ({
        x: ele?.date,
        y: convertTimeToDecimal(ele.totalTimeUG),
        exactHours: ele.totalTimeUG,
      }))
      .filter((data: any) => data.y !== undefined) || [];

  totalTimeData.sort((a: any, b: any) => a.x - b.x);

  function generateHourlyArray(maxHour: number) {
    let hoursArray = [];
    let hour = 0;

    while (hour <= maxHour) {
      if (hour % 2 === 0) hoursArray.push(`${hour.toFixed(1)}HR`);
      hour += 1.0;
    }

    return hoursArray;
  }

  function generateNumberArray(maxNumber) {
    let numbersArray = [];
    for (let i = 0; i < maxNumber; i++) {
      if (i % 2 === 0) numbersArray.push(i);
    }
    return numbersArray;
  }

  const [timeZone, setTimeZone] = useState('');
  const [maxUgHour, setMaxUgHour] = useState<number>();

  useEffect(() => {
    const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    setTimeZone(browserTimeZone);
    setMaxUgHour(
      Math.round(Math.max(...totalTimeData.map((o: any) => o.y)) + 2) <= 12
        ? 13
        : Math.round(Math.max(...totalTimeData.map((o: any) => o.y)) + 2)
    );
  }, []);

  const tickValues: any[] = [];
  const startDateStr = props?.dateRange?.startDate || '';
  const endDateStr = props?.dateRange?.endDate || '';

  const startDate: any = startDateStr ? dayjs.tz(startDateStr, timeZone) : null;
  const endDate: any = endDateStr ? dayjs.tz(endDateStr, timeZone) : null;
  let currentDate = startDate;
  const daysDifference = endDate.diff(startDate, 'day');

  if (startDate && endDate && startDate.isBefore(endDate)) {
    let currentDate = startDate.startOf('day');
    const endOfRange = endDate.endOf('day');

    while (
      currentDate.isBefore(endOfRange) ||
      currentDate.isSame(endOfRange, 'day')
    ) {
      tickValues.push(currentDate.format());
      currentDate = currentDate.add(1, 'day');
    }
  } else {
    console.error('Invalid date range or start date is after end date.');
  }

  const avgTimeUG = parseFloat(props?.data?.avgTimeUG);

  let xAxisRange = generateWeeklyTickVals(
    props?.dateRange?.startDate,
    props?.dateRange?.endDate
  );

  const data = [
    {
      type: 'scatter',
      x:
        totalTimeData.map((point: any) => new Date(point?.x)).length == 1 &&
        tickValues.length == 1
          ? totalTimeData?.map((point: any) => dayjs(point?.x)).format('MMM-DD')
          : totalTimeData.map((point: any) => new Date(point?.x)),
      y: totalTimeData?.map((point: any) => point?.y),
      mode: 'lines+markers',
      line: {
        color: 'rgb(255, 177, 50,100%)',
        width: 3,
      },
      hoverinfo: 'text',
      text: totalTimeData?.map(
        (point: any) => `${dayjs(point.x).format('DD-MMM')},${point.exactHours}`
      ),
    },
    {
      x: avgTimeUG ? [tickValues[0], tickValues[tickValues?.length - 1]] : [],
      y: [avgTimeUG, avgTimeUG],
      mode: 'lines',
      name: '',
      line: {
        dash: 'dot',
        width: 0,
        color: 'rgba(0, 0, 0, 0)',
      },
      hoverinfo: 'none',
    },
  ];

  const layout = {
    xaxis: {
      color: 'white',
      tickformat: '%b-%d',
      gridcolor: 'rgba(0, 0, 0, 0)',
      automargin: true,
      ticklen: 0,
      range:
        tickValues?.length == 1
          ? []
          : [
              props?.dateRange?.startDate,
              dayjs(props?.dateRange?.endDate)
                .add(1, 'day')
                .format('YYYY-MM-DD'),
            ],
      tickvals: xAxisRange ? xAxisRange : tickValues,
      tickangle: -45,
      autorange: true,
    },
    yaxis: {
      type: 'linear',
      range: [-1, maxUgHour],
      tickvals: generateNumberArray(maxUgHour),
      ticktext: generateHourlyArray(maxUgHour),
      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 50%)',
      gridwidth: '0.5px',
      fixedrange: true,
    },
    shapes: avgTimeUG
      ? [
          {
            type: 'line',
            xref: 'paper',
            x0: 0,
            y0: avgTimeUG,
            x1: 1,
            y1: avgTimeUG,
            line: { color: 'white', width: 2, dash: 'dot' },
          },
        ]
      : [],
    annotations: [
      {
        x: dayjs(props?.dateRange?.endDate).add(1, 'day').format('YYYY-MM-DD'),
        y: avgTimeUG,
        xref: 'x',
        yref: 'y',
        text: `${avgTimeUG} Hours`,
        showarrow: false,
        xanchor: 'right',
        yanchor: 'bottom',
        xshift: 30,
        font: {
          color: 'white',
          size: 14,
        },
      },
    ],
    showlegend: false,
    margin: { t: 30, l: 60, b: 45, r: 105, pad: 5 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    dragmode: 'pan',
  };

  return (
    <div className="w-[90%] m-auto">
      {props.reportData.length != 0 && avgTimeUG ? (
        <>
          <Plot
            data={data}
            layout={layout}
            className="!w-[100%] !h-[60vh]"
            config={{
              displayModeBar: false,
              scrollZoom: true,
              responsive: true,
              dragmode: false,
            }}
          />
          <div className="w-full text-[20px] text-white">
            {' '}
            <p className="flex justify-center">Total Time UG</p>
          </div>
        </>
      ) : (
        <div className="mt-10">
          <img src={BlankChart} alt="" className="w-full h-[40vh]" />
        </div>
      )}
    </div>
  );
};

export default TotalTimeUG;
