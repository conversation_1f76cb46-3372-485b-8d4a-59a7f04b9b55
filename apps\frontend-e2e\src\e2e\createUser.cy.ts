import { getGreeting } from '../support/app.po';

describe('frontend-e2e', () => {
  beforeEach(() => {
    cy.on('uncaught:exception', (e) => {
      if (e.message.includes('EOF is not defined')) {
        return false;
      }
    });

    cy.visit('http://localhost:4200/app');
  });

  it('should successfully login with valid credentials', () => {
    cy.fixture('AdminLoginData').then((AdminLoginData) => {
      const data = AdminLoginData.users[0];
      cy.get('#username').type(data.username);
      cy.wait(1000);
      cy.get('#password').type(data.password);
      cy.wait(1000);
      cy.get('#signin').click();
      cy.wait(3000);
      cy.url().should('include', '/Dashboard');
      cy.wait(1000);
      cy.get('#setting_button').click();
      cy.wait(3000);
      cy.url().should('include', '/user');
      cy.get('#add_new_user').click();
      cy.wait(2000);
      cy.get('#create_user').click();
      cy.wait(2000);
      cy.fixture('AdminLoginData').then((AdminLoginData) => {
        const data = AdminLoginData.users[5];
        cy.get('#firstName').type(data.firstName);
        cy.get('#lastName').type(data.lastName);
        cy.wait(2000);
        cy.get('#username').clear().type(data.userName);
        cy.wait(2000);
        const emails = AdminLoginData.users[6];
        cy.get('#email').type(emails.invalidEmail);
        cy.wait(1000);
        cy.get('#cEmail').type(emails.invalidEmail);
        cy.get('#role').select(2);
        cy.wait(2000);
        cy.get('#create_user').click();
        cy.wait(2000);
        const validData = AdminLoginData.users[4];
        cy.get('#email').clear().type(validData.validEmail);
        cy.get('#create_user').click();
        cy.wait(3000);
        cy.get('#cEmail').clear().type(validData.validEmail);
        cy.get('#create_user').click();
        cy.wait(3000);
        const newEmails = AdminLoginData.users[7];
        cy.get('#email').clear().type(newEmails.newEmail);
        cy.get('#create_user').click();
        cy.wait(3000);
        cy.get('#cEmail').clear().type(newEmails.newEmail);
        cy.get('#create_user').click();
        cy.wait(1000);
        cy.get('#username').clear().type(newEmails.newUserName);
      });
      cy.get('#create_user').click();
    });
  });
});
