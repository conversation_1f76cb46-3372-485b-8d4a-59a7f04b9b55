import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';
import { toast } from 'react-toastify';

export interface IwtEnv {
  apiUrl: string;
  engineApiUrl: string;
  timeIntervalForApi: number;
  showVersionflag: boolean;
  X_API_KEY: string;
  timeIntervalForProd: number;
  mixpanelProjectToken: string;
}

declare var iwtEnv: IwtEnv;

export const ApiClient = axios.create({
  baseURL: iwtEnv.apiUrl,
  timeout: 0,
  headers: {
    'Content-Type': 'application/json',
  },
});

ApiClient.interceptors.request.use(
  async (request: InternalAxiosRequestConfig) => {
    const token = `Bearer ${localStorage.getItem('token')}` || '';
    request.headers.Authorization = token;
    return request;
  }
);

ApiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    if (
      error.response &&
      error.response.status === 403 &&
      error.response.data.message == 'Role changed'
    ) {
      try {
        const data = {
          refreshToken: `${localStorage.getItem('token')}` || '',
        };
        const res = await ApiClient.post(
          '/api/portal/v1/auth/refresh-token',
          data
        );

        if (res?.data) {
          localStorage.removeItem('token');
          localStorage.setItem('token', res.data.jwtToken);
          localStorage.setItem('refreshingPage', 'true');
          window.location.replace(window.location.origin);
        }
      } catch (err: any) {
        console.error(err);
        localStorage.removeItem('token');
        window.location.replace(window.location.origin);
      }
    }
    if (
      error.response &&
      error.response.status === 401 &&
      !error.response.config.url.includes('login')
    ) {
      // Handle unauthorized error, for example, redirect to login page
      console.error('Unauthorized error:', error);
      localStorage.removeItem('token');
      window.location.replace(window.location.origin);
      // Redirect to login page
    }

    return Promise.reject(error);
  }
);
