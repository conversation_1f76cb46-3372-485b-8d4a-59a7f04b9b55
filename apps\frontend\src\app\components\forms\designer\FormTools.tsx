import { UndoIcon, RedoIcon } from '../../../../assets/icons/icons';
import FormToolButton from './tools/FormToolButton';

const FormTools = (props: {canUndoButton: boolean, canRedoButton: boolean, undoAction: any, redoAction:any}) => {
	return (
		<div className="">
			<div className="flex justify-evenly">
				<FormToolButton label="Undo" icon={<UndoIcon />} isActive={props.canUndoButton} handleClick={props.undoAction} />
				<FormToolButton label="Redo" icon={<RedoIcon />} isActive={props.canRedoButton} handleClick={props.redoAction} />
			</div>
		</div>
	);
};

export default FormTools;