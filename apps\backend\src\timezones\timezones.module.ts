import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Timezones } from './entities/timezones.entity';
import { TimezonesController } from './timezones.controller';
import { TimezonesService } from './timezones.service';

@Module({
  imports: [TypeOrmModule.forFeature([Timezones])],
  controllers: [TimezonesController],
  providers: [TimezonesService],
  exports: [TimezonesService],
})
export class TimezonesModule {}
