import { Test, TestingModule } from '@nestjs/testing';
import { FeaturesService } from './features.service';
import typeorm = require('typeorm');
import { getRepositoryToken } from '@nestjs/typeorm';
import { Feature } from './entities/feature.entity';
import { CreateFeatureDto } from './dto/create-feature.dto';
import { UpdateFeatureDto } from './dto/update-feature.dto';
describe('FeaturesService', () => {
  let service: FeaturesService;
  let featureRepository: typeorm.Repository<Feature>;
  const FEATURE_REPOSITORY_TOKEN = getRepositoryToken(Feature);

  const mockFeatureRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOneBy: jest.fn(),
    remove: jest.fn(),
  };

  const expectedFeatures = [
    {
      id: 1,
      name: 'Dashboard123',
      code: 'DASH',
      createdAt: '2024-02-14T05:46:09.713Z',
      createdBy: null,
      updatedAt: '2024-02-14T05:46:09.713Z',
      updatedBy: null,
    },
    {
      id: 2,
      name: 'Location',
      code: 'LOC',
      createdAt: '2024-02-14T05:46:28.793Z',
      createdBy: null,
      updatedAt: '2024-02-14T05:46:28.793Z',
      updatedBy: null,
    },
    {
      id: 3,
      name: 'Settings123',
      code: 'SET',
      createdAt: '2024-02-14T05:46:52.020Z',
      createdBy: null,
      updatedAt: '2024-02-14T05:46:52.020Z',
      updatedBy: null,
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FeaturesService,
        {
          provide: FEATURE_REPOSITORY_TOKEN,
          useValue: mockFeatureRepository
        },
      ],
    }).compile();

    service = module.get<FeaturesService>(FeaturesService);
    featureRepository = module.get<typeorm.Repository<Feature>>(
      FEATURE_REPOSITORY_TOKEN
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  describe('fetchData', () => {
    it('should return "Feature array"', async () => {
      const result = await service.findAll();
      expect(result).toEqual(expectedFeatures);
    });
  });

  it('create => Should create a new feature and return its data', async () => {
    // arrange
    const createFeatureDto = {
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    } as CreateFeatureDto;

    const feature = {
      id: 1,
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    } as Feature;

    jest.spyOn(mockFeatureRepository, 'save').mockReturnValue(feature);

    // act
    const result = await service.create(createFeatureDto);

    // assert
    expect(mockFeatureRepository.save).toHaveBeenCalled();
    expect(mockFeatureRepository.save).toHaveBeenCalledWith(createFeatureDto);

    expect(result).toEqual(feature);
  });

  it('findAll => should return an array of features', async () => {
    // arrange
    const feature = {
      id: 1,
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    }
    const features = [feature];
    jest.spyOn(mockFeatureRepository, 'find').mockReturnValue(features);

    // act
    const result = await service.findAll();

    // assert
    expect(mockFeatureRepository.find).toHaveBeenCalled();
    expect(result).toEqual(features);
  });

  it('findOne => should find a feature by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const feature = {
      id: 1,
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    }

    jest.spyOn(mockFeatureRepository, 'findOneBy').mockReturnValue(feature);

    // act
    const result = await service.findOne(id);

    // assert
    expect(mockFeatureRepository.findOneBy).toHaveBeenCalled();
    expect(mockFeatureRepository.findOneBy).toHaveBeenCalledWith({id});
    expect(result).toEqual(feature);
  });

  it('update => should find a feature by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateFeatureDto = {
      name: 'test feature 1',
      code: 'abc'
    } as UpdateFeatureDto;
    const feature = {
      name: 'test feature 2',
      code: 'abc'
    };
    
    const updatedFeature = Object.assign(feature, UpdateFeatureDto);
    jest.spyOn(mockFeatureRepository, 'save').mockReturnValue(updatedFeature);

    // act
    const result = await service.update(id, feature);

    // assert
    expect(mockFeatureRepository.findOneBy).toHaveBeenCalled();
    expect(mockFeatureRepository.findOneBy).toHaveBeenCalledWith({id});
    expect(mockFeatureRepository.save).toHaveBeenCalled();
    expect(mockFeatureRepository.save).toHaveBeenCalledWith(updatedFeature);
    expect(result).toEqual(updatedFeature);
  });

  it('remove => should find a feature by a given id, remove and then return Number of affected rows', async () => {
    // arrange
    const id = 1;
    const feature = {
      name: 'test feature 1',
      code: 'abc'
    };

    jest.spyOn(mockFeatureRepository, 'remove').mockReturnValue(feature);

    // act
    const result = await service.delete(id);

    // assert
    expect(mockFeatureRepository.findOneBy).toHaveBeenCalled();
    expect(mockFeatureRepository.findOneBy).toHaveBeenCalledWith({id});
    expect(mockFeatureRepository.remove).toHaveBeenCalled();
    expect(mockFeatureRepository.remove).toHaveBeenCalledWith(id);
    expect(result).toEqual(feature);
  });
});
