import { useEffect, useState } from 'react';

const Modal = (props: {
  title?: string;
  size?: string;
  noScroll?: boolean;
  setModelOpen?: (flag: boolean) => void;
  Content: JSX.Element;
  ShowPDFButton?: boolean;
  backBg?: string;
  alignment?: string
}) => {
  return (
    <div
      className={`fixed z-[999] inset-0 ${props.alignment ? 'flex items-center justify-center' : ''}`}
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div className="flex items-end justify-center min-h-screen pt-2 px-4 pb-20 text-center sm:block sm:p-0">
        <div
          className={`fixed inset-0  ${
            props?.backBg ? 'backdrop-blur-sm' : ''
          }  bg-opacity-75 transition-opacity`}
          aria-hidden="true"
        ></div>

        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>
        <div
          className={`inline-block align-bottom text-left  shadow-lg  ${
            props?.backBg ? props?.backBg : 'border-black bg-black'
          } border ${props?.backBg} transform   sm:my-8 sm:align-middle ${
            props?.size
          }`}
        >
          <div className="py-4 px-3">
            <div
              className={!props?.noScroll ? 'overflow-y-auto' : 'overflow-none'}
              style={{ maxHeight: `calc(100vh - 10rem)` }}
            >
              <div>{props?.Content}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Modal;
