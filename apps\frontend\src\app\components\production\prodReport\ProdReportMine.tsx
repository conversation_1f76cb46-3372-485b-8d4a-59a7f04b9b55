import ProgressBar from '../../common/ProgressBar';
import { ProdDownArrow } from '../../../../assets/icons/icons';
import { ProdUpArrow } from '../../../../assets/icons/icons';
import MultiLineGraph from '../../common/MultiLineGraph';
import { useParams } from 'react-router-dom';
import FillAreaGraph from '../../common/FillAreaGraph';
import Plot from 'react-plotly.js';
import { useQuery } from '@tanstack/react-query';
import { useOutletContext } from 'react-router';
import decodeJWT from '../../../../utils/jwtdecoder';
import { getReportMineData } from '../../../../api/production/productionReportapi';
import { IwtEnv } from '../../../../api/apiClient';
declare var iwtEnv: IwtEnv;
import dayjs from 'dayjs';
import { useCallback, useEffect } from 'react';
import { format } from 'path';
import Loader from '../../common/Loader';
import GraphSkeleton from '../../common/GraphSkeleton';

export default function ProdLiveMine() {
  const params = useParams();

  const [searchText, setLastUpdatedDate, filterData] = useOutletContext() as [
    string,
    any,
    any
  ];

  const startDate = filterData.startDate
    ? String(dayjs(filterData.startDate).format('YYYY-MM-DD'))
    : '';
  const endDate = filterData.endDate
    ? String(dayjs(filterData.endDate).format('YYYY-MM-DD'))
    : '';

  const reportMineQuery = useQuery({
    queryKey: ['UpdatedReportMineData', filterData],
    refetchOnWindowFocus: false,
    queryFn: () =>
      getReportMineData(Number(decodeJWT()?.mineid), startDate, endDate),
  });

  const { data: reportMineData, refetch, status: apiStatus } = reportMineQuery;

  const prodReportMineData = {
    mineId: 1,
    totalFeetMined: 123,
    totalFeetMinedUnit: 'ft',
    feetMinedPerHour: 12.4,
    feetMinedPerHourUnit: 'ft',
    totalDowntime: 52.4,
    totalDowntimeUnit: 'mins',
    feetMinedGraphData: {
      coordinates: [
        {
          date: '2024-04-23',
          feetMined: 550,
        },
        {
          date: '2024-04-24',
          feetMined: 400,
        },
      ],
    },
    sectionsMined: [
      {
        sectionId: 1,
        sectionName: '5YN',
        shiftName1: 90,
        shiftName2: 70,
        totalMined: 160,
      },
      {
        sectionId: 2,
        sectionName: '5YN',
        shiftName1: 60,
        shiftName2: 70,
        totalMined: 130,
      },
    ],
    mostDowntime: '4-6',
    mostDowntimeMeridiem: 'AM',
    sectionwiseGraphData: {
      xAxisPoints: ['5YN', '6YE', '8YE', '7YN', '6YN'],
      coordinates: [
        {
          sectionID: 1,
          sectionName: '5YN',
          totalMined: 160,
          'Morning shift': 80,
          'Evening shift': 80,
        },
        {
          sectionID: 2,
          sectionName: '6YE',
          totalMined: 150,
          'Morning shift': 110,
          'Evening shift': 40,
        },
        {
          sectionID: 3,
          sectionName: '8YE',
          totalMined: 170,
          'Morning shift': 50,
          'Evening shift': 120,
        },
        {
          sectionID: 1,
          sectionName: '7YN',
          totalMined: 200,
          'Morning shift': 100,
          'Evening shift': 100,
        },
        {
          sectionID: 1,
          sectionName: '6YN',
          totalMined: 2000,
          'Morning shift': 400,
          'Evening shift': 1600,
        },
      ],
    },
    activity: {
      lastReport: '6:07pm',
      inspection: {
        prefixSign: '-',
        feets: 42,
        feetsUnit: 'ft',
        inspectionColor: 'red',
        goal: 1600,
      },
      plannedDowntime: {
        prefixSign: '-',
        feets: 27,
        feetsUnit: 'ft',
        plannedDowntimeColor: 'red',
        goal: 1600,
      },
      unplannedDowntime: {
        prefixSign: '-',
        feets: 67,
        feetsUnit: 'ft',
        unplannedDowntimeColor: 'red',
        goal: 1600,
      },
      onTime: {
        prefixSign: '+',
        feets: 42,
        feetsUnit: 'ft',
        onTimeColor: 'green',
        goal: 800,
      },
      sectionEquipment: {
        prefixSign: '+',
        feets: 12,
        feetsUnit: 'ft',
        onTimeColor: 'green',
        goal: 800,
      },
      beltAvailability: {
        prefixSign: '-',
        feets: 22,
        feetsUnit: 'ft',
        onTimeColor: 'green',
        goal: 800,
      },
    },
  };

  const shiftNames: any =
    reportMineData?.data?.sectionwiseGraphData?.shiftNameList;

  const colorArray = [
    'rgba(255, 194, 94, 1)',
    'rgba(38, 31, 82, 1)',
    'rgba(254, 74, 106, 1)',
    'rgba(74, 168, 254, 1)',
  ];

  const repeatCount = shiftNames?.length;

  const repeatedColorArray = Array.from(
    { length: repeatCount },
    (_, i) => colorArray[i % colorArray.length]
  );

  const getHighestSummedShiftData = (graphData: any) => {
    const summedValuesBySection: { [key: number]: number } = {};

    graphData?.coordinates?.forEach((coordinate: any) => {
      if (summedValuesBySection[coordinate.sectionId]) {
        summedValuesBySection[coordinate.sectionId] += coordinate.totalMined;
      } else {
        summedValuesBySection[coordinate.sectionId] = coordinate.totalMined;
      }
    });
    const highestValue = Math.max(...Object.values(summedValuesBySection));

    return highestValue;
  };

  const highestSummedShiftValue = getHighestSummedShiftData(
    reportMineData?.data?.sectionwiseGraphData
  );

  const getHighestFeetMinedData = (graphData: any) => {
    const summedValuesBySection: { [key: number]: number } = {};
    graphData?.coordinates?.forEach((coordinate: any) => {
      summedValuesBySection[coordinate.feetMined] = coordinate.feetMined;
    });
    const highestValue = Math.max(...Object.values(summedValuesBySection));
    return highestValue;
  };

  const highestFeetMinedValue = getHighestFeetMinedData(
    reportMineData?.data?.feetMinedGraphData
  );

  let sectionNames = reportMineData?.data.sectionwiseGraphData?.xAxisPoints;

  const shiftTrace = shiftNames?.map((shift: any, index: number) => {
    return {
      x: sectionNames,
      y: getShiftDataById(shift.shiftId),
      type: 'bar',
      name: shift.shiftName,
      marker: {
        color: repeatedColorArray[index],
      },
      width: [0.2, 0.2, 0.2, 0.2, 0.2, 0.2],
      barcornerradius: 10,
      text: '',
      textposition: 'outside',
      textfont: { color: 'white' },
      hoverinfo: 'text',
      hovertext: getShiftDataHandoverText(shift),
      showlegend: shift.shiftName == 'OWL_Shift' ? false : true,
    };
  });

  function getShiftDataById(shiftId: number) {
    return reportMineData?.data?.sectionwiseGraphData?.coordinates?.map(
      (section: any) => {
        const shiftData = section.data.find((d: any) => d.shiftId === shiftId);
        return shiftData ? shiftData.shiftData : 0;
      }
    );
  }
  function getShiftDataHandoverText(shift: any) {
    return reportMineData?.data?.sectionwiseGraphData?.coordinates?.map(
      (section: any) => {
        const shiftData = section.data.find(
          (d: any) => d.shiftId === shift.shiftId
        );
        return shift.shiftName + ' : ' + (shiftData ? shiftData.shiftData : 0);
      }
    );
  }

  let totalMined = reportMineData?.data?.sectionwiseGraphData?.coordinates?.map(
    (i: any) => i.totalMined
  );

  const day = reportMineData?.data?.feetMinedGraphData?.coordinates?.map(
    (i: any) => i.date
  );

  const xAxisValues = day?.map((dateString: string) =>
    dayjs(dateString).format('D MMM')
  );

  const yAxisValues =
    reportMineData?.data?.feetMinedGraphData?.coordinates?.map(
      (i: any) => i.feetMined
    );

  const reportMineHeading = [
    { heading: 'Mined' },
    { heading: 'Feet Mined/Hour' },
    { heading: 'Belt Availability' },
    { heading: 'Total Downtime' },
    // { heading: 'Most Downtime' },
    { heading: 'Total Alerts' },
  ];

  const barGraphLayout = {
    barmode: 'stack',
    barcornerradius: 20,
    xaxis: {
      title: 'Working Section',
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      range: reportMineData?.data ? [] : [-1, 11],
    },
    yaxis: {
      title: {
        text: 'Feet Mined',
        standoff: 20,
      },
      side: 'right',
      // type: 'linear',
      range: reportMineData?.data
        ? [-1, highestSummedShiftValue + 800]
        : [0, 14],
      color: 'white',
      showgrid: false,
    },
    showlegend: true,
    // width: getPlotWidth(),
    margin: { t: 20, l: 20, b: 75, r: marginBarRight(), pad: 5 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
    bargap: 0.1,
    dragmode: false,
  };
  const averageGoal = reportMineData?.data?.averageGoal;

  function marginRight() {
    const screenWidth = window.innerWidth;
    if (screenWidth > 1536) {
      return 60;
    } else if (screenWidth > 1280) {
      return 50;
    }
  }

  function marginBarRight() {
    const screenWidth = window.innerWidth;
    if (screenWidth > 1536) {
      return 80;
    } else if (screenWidth > 1280) {
      return 75;
    }
  }

  const showDot =
    yAxisValues?.length === 1 ||
    yAxisValues?.filter((value: any) => value > 0).length === 1;

  const averageGoalValue = reportMineData?.data?.averageGoal;

  const fillAreaLayout = {
    barmode: 'stack',
    xaxis: {
      title: 'Day',
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      tickangle: 40,
      range: reportMineData?.data ? [] : [0, 12],
      zeroline: false,
    },
    yaxis: {
      title: {
        text: 'Feet Mined',
        standoff: 50,
      },
      type: 'linear',
      range: reportMineData?.data
        ? [
            -1,
            highestFeetMinedValue == 0
              ? averageGoalValue + 100
              : highestFeetMinedValue + 100,
          ]
        : [-1, 11],
      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 0.5)',
      fixedrange: true,
    },
    showlegend: false,
    margin: { t: 20, l: 60, b: 75, r: marginRight(), pad: 5 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
    zeroline: true, // Show zero line on y-axis
    zerolinecolor: 'rgba(56, 134, 206, 0.44)/20', // Customize zero line color if needed
    dragmode: 'pan',
    modeBarButtonsToRemove: ['lasso2d', 'select2d'],
    displaylogo: false,
    automargin: true,
    shapes: averageGoalValue
      ? [
          {
            type: 'line',
            xref: 'paper',
            x0: 0,
            y0: averageGoalValue,
            x1: 1,
            y1: averageGoalValue,
            line: { color: 'white', width: 2, dash: 'dot' },
            hovertemplate: 'averageGoalValue: <b>%{y}</b><extra></extra>', // Custom hover template for average goal
          },
        ]
      : [],
  };

  const fillAreaData = [
    {
      x: xAxisValues,
      y: yAxisValues,
      type: 'scatter',
      fill: 'tozeroy',
      fillcolor: 'filledAreaColor',
      // mode: 'lines',
      hovertemplate: '<b>%{x}</b>: %{y}<extra></extra>',
      mode: showDot ? 'markers' : 'lines',
      marker: {
        size: showDot ? 5 : 0, // Adjust size if showing dot
        color: showDot ? 'white' : 'rgba(56, 134, 206, 0.44)/20', // dot color
      },
    },
  ];

  const barGraphData = shiftTrace;

  const lastReport = dayjs(reportMineData?.data?.activityMetrics?.lastReport);

  function calculateAchieveGoal(averageGoal: any, achievedGoal: any) {
    if (
      (achievedGoal >= 10 && achievedGoal < 100) ||
      (achievedGoal <= -10 && achievedGoal > -100)
    ) {
      return achievedGoal;
    } else {
      let percentageGoal = (achievedGoal / averageGoal) * 100;
      return percentageGoal;
    }
  }

  let inspectionPercentage = calculateAchieveGoal(
    reportMineData?.data?.averageGoal,
    reportMineData?.data?.activityMetrics?.inspectionDowntime?.feets
  );

  let plannedPercentage = calculateAchieveGoal(
    reportMineData?.data?.averageGoal,
    reportMineData?.data?.activityMetrics?.plannedDowntime?.feets
  );

  let unplannedPercentage = calculateAchieveGoal(
    reportMineData?.data?.averageGoal,
    reportMineData?.data?.activityMetrics?.unplannedDowntime?.feets
  );

  let ontimePercentage = calculateAchieveGoal(
    reportMineData?.data?.averageGoal,
    reportMineData?.data?.activityMetrics?.onTime?.feets
  );
  return (
    <div>
      <div className="grid grid-cols-5 gap-4 pt-6 w-full h-full px-[4%] mx-auto bg-[#2c5c8bf6] pb-6">
        {reportMineHeading.map((heading, index) => (
          <div key={index}
            className={`h-[100px] border-[#4AA8FE] rounded-lg border-[1px] ${
              heading?.heading == 'Belt Availability' ||
              heading?.heading == 'Total Alerts'
                ? 'border-dashed  border-[1px] opacity-40'
                : 'border-[1px]'
            } `}
          >
            <div
              className={`${
                heading?.heading == 'Belt Availability' ||
                heading?.heading == 'Total Alerts'
                  ? 'opacity-40'
                  : ''
              }`}
              style={{ overflow: 'hidden' }}
            >
              <div className="text-center text-[14px] text-[#FFD084] font-normal pt-3">
                {heading.heading}
                {index === 0 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {reportMineData?.data?.totalFeetMined
                      ? reportMineData?.data?.totalFeetMined
                      : '0'}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'ft'}
                    </span>
                  </div>
                )}
                {index === 1 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {reportMineData?.data?.feetMinedPerHour
                      ? reportMineData?.data?.feetMinedPerHour
                      : '0'}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'fmph'}
                    </span>
                  </div>
                )}
                {index === 2 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3 ">
                    {0.0}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'%'}
                    </span>
                  </div>
                )}
                {index === 3 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {reportMineData?.data?.totalDowntime
                      ? reportMineData?.data?.totalDowntime
                      : '0'}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {'hrs'}
                    </span>
                  </div>
                )}
                {index === 4 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {!reportMineData?.data?.mostDowntime ||
                    reportMineData?.data?.mostDowntime === null
                      ? '-'
                      : reportMineData?.data?.mostDowntime}
                    <span className="text-white text-[12px] mx-1 font-normal">
                      {reportMineData?.data?.mostDowntimeMeridiem
                        ? reportMineData?.data?.mostDowntimeMeridiem.toLowerCase()
                        : ''}
                    </span>
                  </div>
                )}
                {index === 5 && (
                  <div className="text-center text-[32px] text-white font-bold pt-3">
                    {0}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex px-[4%] pt-4">
        <div className="w-1/2 !h-[80%]">
          <div className="!w-[100%] !h-80 overflow-hidden">
            {Object?.keys(
              apiStatus == 'success'
                ? reportMineData?.data?.feetMinedGraphData
                : {}
            )?.length === 0 ? (
              <GraphSkeleton
                className="2xl:!h-[35.5vh] xl:!h-[56vh]  mr-4"
                apiStatus={apiStatus}
              />
            ) : (
              <Plot
                className="!w-[100%] !h-80 bg-transparent overflow-hidden"
                data={fillAreaData}
                layout={fillAreaLayout}
                config={{
                  displayModeBar: false,
                  scrollZoom: true,
                  responsive: true,
                  dragmode: false,
                }}
              />
            )}
          </div>
        </div>
        <div className="w-1/2 h-80 .js-plotly-plot">
          <div className="w-full h-80 overflow-hidden ">
            {Object?.keys(
              apiStatus == 'success'
                ? reportMineData?.data?.sectionwiseGraphData.shiftNameList
                : {}
            )?.length === 0 ? (
              <GraphSkeleton
                className="2xl:!h-[35.5vh] xl:!h-[56vh] ml-4 "
                apiStatus={apiStatus}
              />
            ) : (
              <Plot
                className="bg-transparent w-full h-80 overflow-hidden"
                data={barGraphData}
                // layout={barGraphLayout}
                layout={{
                  ...barGraphLayout,
                  legend: {
                    orientation: 'h',
                    x: 0.5,
                    xanchor: 'center',
                    y: 1.15,
                    font: { color: 'white' },
                  },
                }}
                config={{
                  displayModeBar: false,
                  responsive: true,
                }}
              />
            )}
          </div>
        </div>
      </div>

      <div className="px-[4%] pt-8 flex">
        <div className=" w-1/2  h-[260px] activityColor rounded-md ">
          <div className="flex">
            <div className="font-bold text-2xl text-white pt-4 pl-4 w-1/2">
              Activity
            </div>
          </div>
          <div className="flex pt-2 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Inspection
            </div>
            <div className=" flex w-1/2 justify-end">
              <div
                className={`${
                  !reportMineData?.data?.activityMetrics?.inspectionDowntime
                    ?.feets ||
                  reportMineData?.data?.activityMetrics?.inspectionDowntime
                    ?.feets === 0
                    ? 'flex items-center 2xl:pl-4 xl:pl-3'
                    : 'flex items-center'
                }`}
              >
                <div
                  className={`font-normal text-xs pt-1 text-right w-[65px] ${
                    reportMineData?.data?.activityMetrics?.inspectionDowntime
                      ?.feets < 0
                      ? 'text-[#96FB60]'
                      : !reportMineData?.data?.activityMetrics
                          ?.inspectionDowntime?.feets ||
                        reportMineData?.data?.activityMetrics
                          ?.inspectionDowntime?.feets === 0
                      ? 'text-white pr-2'
                      : 'text-[#FE4A6A]'
                  }`}
                >
                  {reportMineData?.data?.activityMetrics?.inspectionDowntime
                    ?.feets > 0
                    ? '-' +
                      reportMineData?.data?.activityMetrics?.inspectionDowntime
                        ?.feets +
                      ' ' +
                      'ft'
                    : reportMineData?.data?.activityMetrics?.inspectionDowntime
                        ?.feets === 0 ||
                      !reportMineData?.data?.activityMetrics?.inspectionDowntime
                        ?.feets
                    ? '0 ft'
                    : reportMineData?.data?.activityMetrics?.inspectionDowntime
                        ?.feets + ' ft'}
                </div>
                <div className="pt-2 pl-2 pr-2">
                  {reportMineData?.data?.activityMetrics?.inspectionDowntime
                    ?.feets > 0 ? (
                    <ProdDownArrow />
                  ) : !reportMineData?.data?.activityMetrics?.inspectionDowntime
                      ?.feets ||
                    reportMineData?.data?.activityMetrics?.inspectionDowntime
                      ?.feets === 0 ? (
                    ''
                  ) : (
                    <ProdUpArrow />
                  )}
                </div>
                <div className="pt-2  pr-4 ">
                  <ProgressBar
                    value={
                      inspectionPercentage > 0
                        ? '-' + inspectionPercentage.toFixed(2)
                        : !reportMineData?.data?.activityMetrics
                            ?.inspectionDowntime?.feets ||
                          reportMineData?.data?.activityMetrics
                            ?.inspectionDowntime?.feets === 0
                        ? ''
                        : inspectionPercentage.toFixed(2)
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex pt-2 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Planned Downtime
            </div>
            <div className=" flex w-1/2 justify-end">
              <div
                className={`${
                  !reportMineData?.data?.activityMetrics?.plannedDowntime
                    ?.feets ||
                  reportMineData?.data?.activityMetrics?.plannedDowntime
                    ?.feets === 0
                    ? 'flex items-center pl-4'
                    : 'flex items-center'
                }`}
              >
                <div
                  className={`font-normal text-xs pt-1 text-right w-[65px] ${
                    reportMineData?.data?.activityMetrics?.plannedDowntime
                      ?.feets < 0
                      ? 'text-[#96FB60]'
                      : !reportMineData?.data?.activityMetrics?.plannedDowntime
                          ?.feets ||
                        reportMineData?.data?.activityMetrics?.plannedDowntime
                          ?.feets === 0
                      ? 'text-white pr-2'
                      : 'text-[#FE4A6A]'
                  }`}
                  // style={{ width: '100px', textAlign: 'right' }}
                >
                  {reportMineData?.data?.activityMetrics?.plannedDowntime
                    ?.feets > 0
                    ? '-' +
                      reportMineData?.data?.activityMetrics?.plannedDowntime
                        ?.feets +
                      '' +
                      'ft'
                    : reportMineData?.data?.activityMetrics?.plannedDowntime
                        ?.feets === 0 ||
                      !reportMineData?.data?.activityMetrics?.plannedDowntime
                        ?.feets
                    ? '0 ft'
                    : reportMineData?.data?.activityMetrics?.plannedDowntime
                        ?.feets + ' ft'}
                </div>
                <div className="pt-2 pl-2 pr-2">
                  {reportMineData?.data?.activityMetrics?.plannedDowntime
                    ?.feets > 0 ? (
                    <ProdDownArrow />
                  ) : !reportMineData?.data?.activityMetrics?.plannedDowntime
                      ?.feets ||
                    reportMineData?.data?.activityMetrics?.plannedDowntime
                      ?.feets === 0 ? (
                    ''
                  ) : (
                    <ProdUpArrow />
                  )}
                </div>
                <div className="pt-2 pr-4">
                  <ProgressBar
                    value={
                      plannedPercentage > 0
                        ? '-' + plannedPercentage.toFixed(2)
                        : !reportMineData?.data?.activityMetrics
                            ?.plannedDowntime?.feets ||
                          reportMineData?.data?.activityMetrics?.plannedDowntime
                            ?.feets === 0
                        ? ''
                        : plannedPercentage.toFixed()
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex pt-2 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Unplanned Downtime
            </div>
            <div className=" flex w-1/2 justify-end">
              <div
                className={`${
                  !reportMineData?.data?.activityMetrics?.unplannedDowntime
                    ?.feets ||
                  reportMineData?.data?.activityMetrics?.unplannedDowntime
                    ?.feets === 0
                    ? 'flex items-center pl-4'
                    : 'flex items-center'
                }`}
              >
                <div
                  className={`font-normal text-xs pt-1 text-right w-[65px] ${
                    reportMineData?.data?.activityMetrics?.unplannedDowntime
                      ?.feets < 0
                      ? 'text-[#96FB60]'
                      : !reportMineData?.data?.activityMetrics
                          ?.unplannedDowntime?.feets
                      ? 'text-white pr-2'
                      : !reportMineData?.data?.activityMetrics
                          ?.unplannedDowntime?.feets ||
                        reportMineData?.data?.activityMetrics?.unplannedDowntime
                          ?.feets === 0
                      ? 'text-white pr-2'
                      : 'text-[#FE4A6A]'
                  }`}
                >
                  {reportMineData?.data?.activityMetrics?.unplannedDowntime
                    ?.feets > 0
                    ? '-' +
                      reportMineData?.data?.activityMetrics?.unplannedDowntime
                        ?.feets +
                      ' ' +
                      'ft'
                    : reportMineData?.data?.activityMetrics?.unplannedDowntime
                        ?.feets === 0 ||
                      !reportMineData?.data?.activityMetrics?.unplannedDowntime
                        ?.feets
                    ? '0 ft'
                    : reportMineData?.data?.activityMetrics?.unplannedDowntime
                        ?.feets + ' ft'}
                </div>
                <div className="pt-2 pl-2 pr-2">
                  {reportMineData?.data?.activityMetrics?.unplannedDowntime
                    ?.feets > 0 ? (
                    <ProdDownArrow />
                  ) : !reportMineData?.data?.activityMetrics?.unplannedDowntime
                      ?.feets ||
                    reportMineData?.data?.activityMetrics?.unplannedDowntime
                      ?.feets === 0 ? (
                    ''
                  ) : (
                    <ProdUpArrow />
                  )}
                </div>
                <div className="pt-2 pr-4">
                  <ProgressBar
                    value={
                      unplannedPercentage > 0
                        ? '-' + unplannedPercentage.toFixed(2)
                        : !reportMineData?.data?.activityMetrics
                            ?.unplannedDowntime?.feets ||
                          reportMineData?.data?.activityMetrics
                            ?.unplannedDowntime?.feets === 0
                        ? ''
                        : unplannedPercentage.toFixed()
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex pt-2 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              On Time
            </div>
            <div className=" flex w-1/2 justify-end">
              <div
                className={`${
                  !reportMineData?.data?.activityMetrics?.onTime?.feets ||
                  reportMineData?.data?.activityMetrics?.onTime?.feets === 0
                    ? 'flex items-center pl-4'
                    : 'flex items-center'
                }`}
              >
                <div
                  className={`font-normal text-xs pt-1 text-right w-[65px] ${
                    reportMineData?.data?.activityMetrics?.onTime?.feets > 0
                      ? 'text-[#96FB60]'
                      : !reportMineData?.data?.activityMetrics?.onTime?.feets ||
                        reportMineData?.data?.activityMetrics?.onTime?.feets ===
                          0
                      ? 'text-white pr-2'
                      : 'text-[#FE4A6A]'
                  }`}
                >
                  {reportMineData?.data?.activityMetrics?.onTime?.feets > 0
                    ? '+' +
                      reportMineData?.data?.activityMetrics?.onTime?.feets +
                      ' ' +
                      'ft'
                    : reportMineData?.data?.activityMetrics?.onTime?.feets ===
                        0 ||
                      !reportMineData?.data?.activityMetrics?.onTime?.feets
                    ? '0 ft'
                    : +reportMineData?.data?.activityMetrics?.onTime?.feets +
                      ' ft'}
                </div>
                <div className="pt-2 pl-2 pr-2">
                  {reportMineData?.data?.activityMetrics?.onTime?.feets < 0 ? (
                    <ProdDownArrow />
                  ) : reportMineData?.data?.activityMetrics?.onTime?.feets ===
                      0 ||
                    !reportMineData?.data?.activityMetrics?.onTime?.feets ? (
                    ''
                  ) : (
                    <ProdUpArrow />
                  )}
                </div>
                <div className="pt-2 pr-4 ">
                  <ProgressBar
                    value={
                      ontimePercentage > 0
                        ? '+' + ontimePercentage.toFixed(2)
                        : reportMineData?.data?.activityMetrics?.onTime
                            ?.feets === 0 ||
                          !reportMineData?.data?.activityMetrics?.onTime?.feets
                        ? ''
                        : ontimePercentage.toFixed()
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex pt-2 opacity-40 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Section Equipment
            </div>
            <div className=" flex w-1/2 justify-end">
              <div className="font-normal text-xs pt-1 xl:pl-6 2xl:pl-12 text-[#96FB60] ">
                {prodReportMineData.activity.sectionEquipment.prefixSign +
                  prodReportMineData.activity.sectionEquipment.feets +
                  prodReportMineData.activity.sectionEquipment.feetsUnit}
              </div>
              <div className="pt-2 pl-2">
                {prodReportMineData.activity.sectionEquipment.prefixSign ===
                '-' ? (
                  <ProdDownArrow />
                ) : (
                  <ProdUpArrow />
                )}
              </div>
              <div className="pt-2 pr-4">
                <ProgressBar
                  value={
                    prodReportMineData.activity.sectionEquipment.prefixSign +
                    prodReportMineData.activity.sectionEquipment.feets
                  }
                />
              </div>
            </div>
          </div>
          <div className="flex pt-2 opacity-40 w-full">
            <div className="pl-4 text-white font-normal text-base w-1/2">
              Belt Availability
            </div>
            <div className=" flex w-1/2 justify-end">
              <div className="font-normal text-xs pt-1 xl:pl-6 2xl:pl-12 text-[#FE4A6A] ">
                {prodReportMineData.activity.beltAvailability.prefixSign +
                  prodReportMineData.activity.beltAvailability.feets +
                  prodReportMineData.activity.beltAvailability.feetsUnit}
              </div>
              <div className="pt-2 pl-2">
                {prodReportMineData.activity.beltAvailability.prefixSign ===
                '-' ? (
                  <ProdDownArrow />
                ) : (
                  <ProdUpArrow />
                )}
              </div>
              <div className="pt-2 pr-4">
                <ProgressBar
                  value={
                    prodReportMineData.activity.beltAvailability.prefixSign +
                    prodReportMineData.activity.beltAvailability.feets
                  }
                />
              </div>
            </div>
          </div>
        </div>
        <div className=" w-1/2 insightsColor h-[260px] xl:ml-8 2xl:ml-12 rounded-sm border-dashed border-[#4AA8FE] border-[1px] unselectable opacity-40">
          <div className="opacity-40">
            <div className="font-bold text-2xl text-white pt-4 pl-8">
              Insights
            </div>
            <div className="text-white text-sm px-4 py-2 pl-14">
              <ul className="list-disc">
                <li>
                  Inspections are taking longer than normal. Shorter inspection
                  windows would +32 feet.
                </li>
                <li>Section 2 started 18 minutes late. Production -12 feet.</li>
                <li>
                  Unplanned downtime is 20% higher so far today than normal
                  resulting in -52 feet of lost production.
                </li>
                <li>
                  Belt 7A was down for 32 minutes resulting in 22 lost feet.
                  Consider decreasing inspection intervals to ensure higher
                  up-time.
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
