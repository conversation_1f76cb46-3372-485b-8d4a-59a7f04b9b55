import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';

import { CreateCompanyDto } from './dto/create-company.dto';
import { CompaniesService } from './companies.service';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { ApiTags } from '@nestjs/swagger';
import { Roles } from '../auth/gurads/roles.decorator';
import { Features } from '../auth/gurads/feature.decorator';
import { RolesGuard } from '../auth/gurads/roles.guard';

@Controller('portal/v1/companies')
@ApiTags('companies')
export class CompaniesController {
  constructor(private CompaniesService: CompaniesService) {}

  @Post('/checkcompanyname')
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async checkCompanyName(@Body() companyBody: any) {
    const companies = await this.CompaniesService.checkCompanyName(companyBody);
    return companies;
  }

  @Post()
  @Roles(['admin'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async create(@Body() companyBody: CreateCompanyDto) {
    const companies = await this.CompaniesService.create(companyBody);
    return companies;
  }

  @Get('/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  findOne(@Param('id') id: number) {
    return this.CompaniesService.findOne(id);
  }

  @Patch('/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  update(@Param('id') id: number, @Body() body: UpdateCompanyDto, @Req() req) {
    return this.CompaniesService.update(id, body, req.user);
  }

  @Delete('/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  delete(@Param('id') id: number) {
    return this.CompaniesService.delete(id);
  }

  @Get()
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  GetAllCompanies() {
    return this.CompaniesService.getAllCompanyMines();
  }
}
