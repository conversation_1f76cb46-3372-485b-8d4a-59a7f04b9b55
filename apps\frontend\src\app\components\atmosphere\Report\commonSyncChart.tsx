import React from 'react';
import Plot from 'react-plotly.js';
import ChartSkeletonAtmosphere from '../../../../assets/ChartSkeletonAtmosphere.png';

type LocationData = {
  label: string;
  gases: string[];
  values: number[][];
};

type GasChartProps = {
  data: {
    row: LocationData[];
  };
  gases: string[];
};

const COLORS = ['#fff700', '#ffe135', '#ffd700', '#fcd116', '#ffea00'];
const COLORS2 = ['#e5b800', '#f6c700', '#f5e050', '#c9a400', '#bfa000'];

const unitObj: any = {
  CO: 'ppm',
  H2: 'ppm',
  'Abs. Pressure': 'in. H2O',
  Humidity: '%',
  Temperature: '℉',
  'Air Velocity': 'ft/min',
  'Comp. Pressure': 'in. H2O',
  'Diff. Pressure': 'in. H2O',
  Airflow: 'CFM',
};

const CommonChartSync: React.FC<GasChartProps> = ({ data, gases }) => {
  const generateTimeLabels = (count: number): string[] => {
    return Array.from({ length: count }, (_, i) => `${i + 1}am`);
  };

  const getTracesForGas = (
    gas: string,
    yAxis: 'y' | 'y2' = 'y',
    COLORSARR: any
  ) => {
    return data.row
      .map((location, locIndex) => {
        const gasIndex = location.gases.findIndex((g) => g === gas);

        if (gasIndex !== -1 && location.values[gasIndex]) {
          return {
            x: generateTimeLabels(location.values[gasIndex].length),
            y: location.values[gasIndex],
            name: `${location.label} (${gas})`,
            type: 'scatter',
            mode: 'lines+markers',
            yaxis: yAxis,
            line: { color: COLORSARR[locIndex % COLORSARR.length] },
            marker: { color: COLORSARR[locIndex % COLORSARR.length] },
          };
        }
        return null;
      })
      .filter((trace): trace is any => trace !== null);
  };

  const getDualGasLayout = (gas1: string, gas2: string) => ({
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    margin: {
      t: 40,
      b: 80,
    },
    xaxis: {
      color: 'white',
      tickfont: {
        color: 'white',
      },
      showgrid: false,
    },
    yaxis: {
      title: {
        text: `${gas1}(${unitObj[gas1]})`,
        font: {
          color: 'white',
        },
      },
      tickfont: {
        color: 'white',
      },
      range:
        gas1 == 'Temperature'
          ? [10, 120]
          : gas1 == 'Humidity'
          ? [10, 120]
          : gas1 == 'Airflow'
          ? [2000, 3200]
          : gas1 == 'Air Velocity'
          ? [100, 220]
          : [1, 12],
    },
    yaxis2: {
      title: {
        text: `${gas2}(${unitObj[gas2]})`,
        font: {
          color: 'white',
        },
      },
      side: 'right',
      overlaying: 'y',
      range:
        gas2 == 'Temperature'
          ? [10, 120]
          : gas1 == 'Humidity'
          ? [10, 120]
          : gas1 == 'Airflow'
          ? [2000, 3200]
          : gas1 == 'Air Velocity'
          ? [100, 220]
          : [1, 12],
      position: 1,
      tickfont: {
        color: 'white',
      },
    },
    legend: {
      orientation: 'h',
      y: -0.3,
      font: {
        color: 'white',
      },
    },
  });

  const renderCharts = () => {
    const chartElements: JSX.Element[] = [];

    if (gases.length === 2) {
      chartElements.push(
        <div key="" className="" style={{ height: '350px' }}>
          <Plot
            data={[
              ...getTracesForGas(gases[0], 'y', COLORS),
              ...getTracesForGas(gases[1], 'y2', COLORS2),
            ]}
            layout={getDualGasLayout(gases[0], gases[1])}
            style={{ width: '100%', height: '100%' }}
            config={{ displayModeBar: false }}
          />
        </div>
      );
    }

    return <div className="w-full">{chartElements}</div>;
  };

  if (gases?.length > 0) {
    return <div>{renderCharts()}</div>;
  } else {
    return (
      <div className="flex flex-col gap-6 w-full">
        <img src={ChartSkeletonAtmosphere} className="w-full" alt="skeleton" />
      </div>
    );
  }
};

export default CommonChartSync;
