import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '../../common/Table';
import ChildScreenForLiveAtomsphere from '../commonLiveChildern';
import { useParams } from 'react-router-dom';
import Loader from '../../common/Loader';
import decodeJWT from '../../../../../src/utils/jwtdecoder';
import { getATMLiveVentilation } from '../../../../../src/api/atmosphere/atmoshpere';
import dayjs from 'dayjs';

const LiveVentilation = () => {
  const params = useParams();
  const path = params['*'];
  const lastSegment = path?.split('/').pop() || '';
  const decoded = decodeJWT();

  const { data: liveVentilationData, isLoading,isFetching } = useQuery({
    queryKey: ['live-ventilation'],
    queryFn: () => getATMLiveVentilation(decoded?.mineid),
    refetchOnWindowFocus: true,
  });

  const baseColumns: ColumnDef[] = [
    {
      key: 'label',
      label: 'Label',
      type: 'text',
      render: (row) => <span className="underline">{row.label}</span>,
    },
    { key: 'id', label: 'ID', type: 'text' },
    {
      key: 'lastUpdate',
      label: 'Last Update',
      type: 'text',
      render: (row) => (
        <div className="w-max">
          <div className="text-[14px] font-medium">{row?.lastUpdate}</div>
        </div>
      ),
    },
  ];

  const allModuleKeysSet = new Set<string>();
  liveVentilationData?.data?.sections?.forEach((section: any) => {
    section.sensors?.forEach((sensor: any) => {
      Object.keys(sensor.moduleData || {}).forEach((key) => {
        allModuleKeysSet.add(key);
      });
    });
  });

  const moduleKeys = Array.from(allModuleKeysSet);

  const moduleColumns: ColumnDef[] = moduleKeys.map((key) => ({
    key: key.replace(/\s+/g, '').toLowerCase(),
    label: key,
    type: 'text',
  }));

  const columns: ColumnDef[] = [
    ...baseColumns.slice(0, 2),
    ...moduleColumns,
    ...baseColumns.slice(2),
  ];

  const dummySections = liveVentilationData?.data?.sections
    ?.map((section: any) => ({
      name: section.sectionName.replace('Section ', ''),
      count: section.sensors.length,
    }))
    .filter((ele: any) => ele.count !== 0);

  const dummyData = {
    sectionBreakup: liveVentilationData?.data?.sections
      ?.filter((section: any) => section.sensors.length > 0)
      ?.map((section: any) => ({
        sectionName: section.sectionName.replace('Section ', ''),
        totalNodes: section.sensors.length,
        totalNodesBreakup: section.sensors.map((sensor: any) => {
          const moduleDataFormatted: Record<string, string> = {};

          moduleKeys.forEach((key) => {
            const data = sensor.moduleData?.[key];
            const normalizedKey = key.replace(/\s+/g, '').toLowerCase();
            moduleDataFormatted[normalizedKey] = data
              ? `${data.value}${data.unit}`
              : '-';
          });

          return {
            label: sensor.label,
            id: sensor.nid,
            status: sensor.status,
            ...moduleDataFormatted,
            lastUpdate: sensor.moduleData?.DifferentialPressure?.timestamp || '-',
          };
        }),
      })),
  };

  return (
    <>
      {isLoading || isFetching? (
        <div className="h-[50vh] flex items-center w-full justify-center">
          <Loader />
        </div>
      ) : (
        <ChildScreenForLiveAtomsphere
          selectedMenu={lastSegment}
          columns={columns}
          dummyData={dummyData}
          dummySections={dummySections}
        />
      )}
    </>
  );
};

export default LiveVentilation;
