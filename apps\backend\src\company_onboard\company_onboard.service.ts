import { Injectable } from '@nestjs/common';
import { OnBoardCompanyDto } from './dto/onboard-compnay.dto';
import { CompaniesService } from '../companies/companies.service';
import { MinesService } from '../mines/mines.service';
import { UsersService } from '../users/users.service';
import { CreateCompanyDto } from '../companies/dto/create-company.dto';
import { CreateMineDto } from '../mines/dtos/create-mine.dto';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { RolesService } from '../roles/roles.service';
import { FormCategoriesService } from '../form_categories/form_categories.service';
import { CreateFormCategoryDto } from '../form_categories/dto/create-form-category.dto';
import { CreateFormTemplateDto } from '../form_templates/dto/create-form-template.dto';
import { FormTemplatesService } from '../form_templates/form_templates.service';
import { FormTemplateDefinitionsService } from '../form_template_definitions/form_template_definitions.service';
import { CreateFormTemplateDefinitionDto } from '../form_template_definitions/dto/create-form-template-definitions.dto';
import sectionProductionFormDesign from '../common/formdesigns/section-production-report';
import { FormCategory } from '../form_categories/entities/form_category.entity';

@Injectable()
export class CompanyOnboardService {
  constructor(
    private companyService: CompaniesService,
    private mineService: MinesService,
    private userService: UsersService,
    private roleService: RolesService,
    private formCategoryService: FormCategoriesService,
    private formTemplateService: FormTemplatesService,
    private formTemplateDefinitionService: FormTemplateDefinitionsService,
  ) {}
  async onBoardCompany(onboardCompanyDto: OnBoardCompanyDto, user: any) {
    const companyDto = new CreateCompanyDto();
    companyDto.name = companyDto.code = onboardCompanyDto.companyName;
    companyDto.createdBy = onboardCompanyDto.createdBy;
    const company = await this.companyService.create(companyDto);

    const mineDto = new CreateMineDto();
    mineDto.name = onboardCompanyDto.mineName;
    mineDto.code = onboardCompanyDto.mineCode;
    mineDto.location = onboardCompanyDto.mineLocation;
    mineDto.companyId = company.id;
    mineDto.timezoneId = onboardCompanyDto?.timezone;
    const mine = await this.mineService.create(mineDto, user);

    const companyRole = await this.roleService.findAll(company.id);
    const adminRole = companyRole.find((role) => role.name == 'admin');

    let newUser: any;
    if (onboardCompanyDto.email && onboardCompanyDto.username) {
      const userDto = new CreateUserDto();
      userDto.firstName = onboardCompanyDto.firstName;
      userDto.lastName = onboardCompanyDto.lastName;
      userDto.username = onboardCompanyDto.username;
      userDto.email = onboardCompanyDto.email;
      userDto.companyId = company.id;
      userDto.roleId = adminRole.id;
      userDto.mineId = mine.id;
      newUser = await this.userService.createUser(userDto, user);
    }

    const formCategoryDto = new CreateFormCategoryDto();
    formCategoryDto.name = 'Production';
    formCategoryDto.mineId = mine.id;
    formCategoryDto.createdBy = onboardCompanyDto.createdBy;
    let formCategory = await this.formCategoryService.create(formCategoryDto);

    const formTemplateDto = new CreateFormTemplateDto();
    formTemplateDto.formCategoryId = formCategory.id;
    formTemplateDto.name = 'Section Production Report';
    formTemplateDto.description = 'Section Production Report';
    formTemplateDto.mineId = mine.id;
    formTemplateDto.createdBy = onboardCompanyDto.createdBy;
    const formTemplate = await this.formTemplateService.create(formTemplateDto, null, true);

    const formTemplateDefinitionDto = new CreateFormTemplateDefinitionDto();
    formTemplateDefinitionDto.formTemplateId = formTemplate.id;
    formTemplateDefinitionDto.major = 1;
    formTemplateDefinitionDto.minor = 0;
    formTemplateDefinitionDto.revision = 0;
    formTemplateDefinitionDto.definition = JSON.stringify(sectionProductionFormDesign);
    formTemplateDefinitionDto.mineId = mine.id;
    formTemplateDefinitionDto.createdBy = onboardCompanyDto.createdBy;
    const definition = await this.formTemplateDefinitionService.create(formTemplateDefinitionDto);
    await this.formTemplateDefinitionService.publish(definition.id, newUser);

    onboardCompanyDto.companyId = company.id;
    return onboardCompanyDto;
  }
}
