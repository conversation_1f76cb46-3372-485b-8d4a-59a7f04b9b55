import React, { useRef, useState } from 'react';
import Table, { ColumnDef } from '../common/Table';
import {
  useDeleteUserWatchlistItem,
  useAddUserWatchlist,
} from '../../../services/mutations/watchlistmutations';
import { useOutletContext } from 'react-router';
import { Link, useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  getLocationLiveSectionData,
  getLocationReportSectionData,
} from '../../../api/location/locationdashboardapi';
import decodeJWT from '../../../utils/jwtdecoder';
import { useEffect } from 'react';
import { IwtEnv } from '../../../api/apiClient';
import Loader from '../common/Loader';
import { escapeRegExp } from '../../../utils/constant';
import { getPageNamesFromUrl } from '../PageName';
import mixpanel from 'mixpanel-browser';
declare var iwtEnv: IwtEnv;

const Section: React.FC = () => {
  const [searchText, setLastUpdatedDate, filterData] = useOutletContext() as [
    string,
    any,
    any
  ];
  
  const regex = new RegExp(`(${escapeRegExp(searchText)})`, 'i');
  const params = useParams();
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [watchlistItem, setWatchlistItem] = useState<any>();
  const addMinersToWatchlist = useAddUserWatchlist();
  const deleteWatchlistItem = useDeleteUserWatchlistItem();
  const [sectionName, setSectionName] = useState('');
  const [filteredSection, setFilteredSection] = useState<any[]>();

  const url = getPageNamesFromUrl(params['*']);
  const decoded = decodeJWT();

  const { selectedDate }: any = useParams();

  const userTokenData = decodeJWT();
  const {
    data: updatedRepSecData,
    refetch: refetchRepSecData,
    isLoading: reportLoading,
  } = useQuery({
    queryKey: [
      'updatedRepSecData',
      Number(userTokenData?.mineid),
      Number(userTokenData?.userId),
      filterData?.startDate,
    ],
    refetchOnWindowFocus: false,
    queryFn: () =>
      getLocationReportSectionData(
        Number(userTokenData?.mineid),
        Number(userTokenData?.userId),
        filterData?.startDate
      ),
    enabled:
      !!params['*']?.includes('/report/sections') &&
      filterData?.startDate != null,
  });
  const {
    data: updatedLocLiveData,
    refetch: refetchLiveSecData,
    isLoading: liveLoading,
  } = useQuery({
    queryKey: ['updatedLocLiveData', filterData],
    refetchOnWindowFocus: false,
    queryFn: () =>
      getLocationLiveSectionData(
        Number(userTokenData?.mineid),
        Number(userTokenData?.userId),
        filterData
      ),
    refetchInterval: Number(iwtEnv?.timeIntervalForApi) * 60 * 1000,
    enabled: !!params['*']?.includes('/live/sections'),
  });

  let data: any;
  if (params[`*`]?.includes('Location/report/sections')) {
    data = updatedRepSecData?.data;
  } else {
    data = updatedLocLiveData?.data;
  }

  useEffect(() => {
    if (searchText.trim() !== '') {
      const regex = new RegExp(`(${escapeRegExp(searchText.trim())})`, 'i');
      const filtered = data?.sectionBreakup?.filter((section: any) => {
        if (params['*']?.includes('/live/sections')) {
          return section.miners.some((miner: any) =>
            regex.test(miner.minerName)
          );
        } else {
          return section.totalNodesBreakup.some((miner: any) =>
            regex.test(miner.minerName)
          );
        }
      });
      setFilteredSection(filtered ?? []);
    } else {
      setFilteredSection(data?.sectionBreakup ?? []);
    }
  }, [searchText, data?.sectionBreakup]);

  useEffect(() => {
    if (params[`*`]?.includes('Location/report/sections')) {
      // refetchRepSecData();
    } else {
      refetchLiveSecData();
    }
  }, [filterData]);

  const savedSectionName = sessionStorage.getItem('reportSectionName');
  const liveSavedSectionName = sessionStorage.getItem('liveSectionName');

  const sectionList = data?.sectionBreakup.map(
    (section: any) => section?.sectionName
  );

  useEffect(() => {
    if (params[`*`]?.includes('Location/report/sections')) {
      const isSectionExist = sectionList?.includes(sectionName);

      if (sectionName && isSectionExist) {
        setSelectedSection(sectionName);
        sessionStorage.setItem('reportSectionName', sectionName);
      } else if (sectionName && !isSectionExist) {
        sessionStorage.setItem('reportSectionName', '');
        setSelectedSection(savedSectionName);
      } else if (
        !sectionName &&
        savedSectionName &&
        sectionList?.includes(savedSectionName)
      ) {
        setSelectedSection(savedSectionName);
      }
    }
  }, [
    params[`*`]?.includes('Location/report/sections'),
    selectedDate,
    sectionList,
  ]);

  useEffect(() => {
    const sectionName: string | undefined = params?.sectionName;
    if (sectionName !== undefined) {
      sessionStorage.setItem('reportSectionName', sectionName);
    }
    const savedSectionName = sessionStorage.getItem('reportSectionName');
    savedSectionName && setSelectedSection(savedSectionName);
    savedSectionName && setSectionName(savedSectionName);

    if (selectedDate) {
      sessionStorage.setItem(
        'locationReportDateRange',
        JSON.stringify(selectedDate)
      );
    }
  }, []);

  useEffect(() => {
    if (params[`*`]?.includes('Location/live/sections')) {
      liveSavedSectionName && setSelectedSection(liveSavedSectionName);
    }
  }, [liveSavedSectionName, params[`*`]?.includes('Location/live/sections')]);

  useEffect(() => {
    setLastUpdatedDate(data?.lastUpdatedTs);
  }, [data?.lastUpdatedTs]);

  const SWcolumns: ColumnDef[] = [
    {
      key: 'minerName',
      label: 'Miner',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden" title={row?.minerName}>
          <Link
            className="underline"
            to={
              params?.mineId
                ? `/app/Mines/${params?.mineId}/Location/report/personnel/${row?.minerId}`
                : `/app/Location/report/personnel/${row?.minerId}`
            }
            onClick={() => {
              mixpanel.track('Personnel Click', {
                Page_Name: getPageNamesFromUrl(params['*']),
                MshaId: decodeJWT()?.mshaId,
                MinerId: row?.minerId,
              });
            }}
            dangerouslySetInnerHTML={{
              __html: row?.minerName?.replace(
                regex,
                "<span style='color: #FFD084;'>$1</span>"
              ),
            }}
          ></Link>
        </div>
      ),
    },
    {
      key: 'arriveTime',
      label: 'Arrived',
      type: 'time',
      render: (row: any) => (
        <div
          className={
            row.arriveTimeCC == 'R'
              ? 'text-[#FE4A6A]'
              : row.arriveTimeCC == 'G'
              ? 'text-[#96FB60]'
              : ''
          }
        >
          {row.arriveTime ? row.arriveTime : '-'}
        </div>
      ),
    },
    {
      key: 'leftTime',
      label: 'Left',
      type: 'time',
      render: (row: any) => (
        <div
          className={
            row.leftTimeCC == 'R'
              ? 'text-[#FE4A6A]'
              : row.leftTimeCC == 'G'
              ? 'text-[#96FB60]'
              : ''
          }
        >
          {row.leftTime ? row.leftTime : '-'}
        </div>
      ),
    },
    {
      key: 'travelTo',
      label: 'Travel To',
      type: 'text',
      render: (row: any) => (
        <div
          className={
            row.travelToCC == 'R'
              ? 'text-[#FE4A6A]'
              : row.travelToCC == 'G'
              ? 'text-[#96FB60]'
              : ''
          }
        >
          {row.travelTo ? row.travelTo : '-'}
        </div>
      ),
    },
    {
      key: 'travelFrom',
      label: 'Travel From',
      type: 'text',
      render: (row: any) => (
        <div
          className={
            row.travelFromCC == 'R'
              ? 'text-[#FE4A6A]'
              : row.travelFromCC == 'G'
              ? 'text-[#96FB60]'
              : ''
          }
        >
          {row.travelFrom ? row.travelFrom : '-'}
        </div>
      ),
    },
    {
      key: 'onSection',
      label: 'On- Section',
      type: 'text',
      render: (row: any) => (
        <div
          className={
            row.onSectionCC == 'R'
              ? 'text-[#FE4A6A]'
              : row.onSectionCC == 'G'
              ? 'text-[#96FB60]'
              : ''
          }
        >
          {row.onSection ? row.onSection : '-'}
        </div>
      ),
    },
    {
      key: '',
      label: 'Watch',
      type: 'text',
      render: (row: any) => (
        <div
          className=""
          title={`${
            row.isWatched
              ? 'Click to remove from watchlist'
              : 'Click to add to watchlist'
          } `}
        >
          <button
            className="bg-transparent hover:bg-blue-300 text-white-300 font-semibold  py-2 px-2 border-[1px] border-[#4AA8FE]  rounded-lg mr-10 w-20"
            onClick={async () => {
              let minerId = row.mineid;
              const userTokenData = decodeJWT();
              try {
                if (row.isWatched) {
                  const res = await deleteWatchlistItem.mutateAsync(
                    row.watchlistId
                  );
                  if (res?.status === 200 || res?.status === 201) {
                    if (params[`*`]?.includes('Location/report/sections')) {
                      refetchRepSecData();
                      if (row.isWatched) {
                        mixpanel.track('Watchlist Remove', {
                          Page_Name: url,
                          MshaId: decoded?.mshaId,
                        });
                      } else {
                        mixpanel.track('Watchlist Add', {
                          Page_Name: url,
                          MshaId: decoded?.mshaId,
                        });
                      }
                    } else {
                      refetchLiveSecData();
                    }
                  }
                } else {
                  const res = await addMinersToWatchlist.mutateAsync({
                    userId: userTokenData?.userId,
                    minerId: row.minerId,
                  });
                  if (res?.status === 200 || res?.status === 201) {
                    if (params[`*`]?.includes('Location/report/sections')) {
                      refetchRepSecData();
                      if (row.isWatched) {
                        mixpanel.track('Watchlist Remove', {
                          Page_Name: url,
                          MshaId: decoded?.mshaId,
                        });
                      } else {
                        mixpanel.track('Watchlist Add', {
                          Page_Name: url,
                          MshaId: decoded?.mshaId,
                        });
                      }
                    } else {
                      refetchLiveSecData();
                    }
                  }
                }
              } catch (error) {
                console.error('Error:', error);
              }
            }}
          >
            {row.isWatched ? 'Remove' : 'Watch'}
          </button>
        </div>
      ),
    },
  ];

  const sectionRef = useRef<HTMLDivElement>(null);
  const tableContainerRef = useRef<HTMLDivElement>(null);

  let count = document?.getElementsByClassName(sectionName)?.length;

  const handleSectionClick = (sectionName: string) => {
    setFilteredSection(data?.sectionBreakup ?? []);
    setSelectedSection((prevSection) =>
      prevSection === sectionName ? null : sectionName
    );
    setSectionName(sectionName);

    if (selectedSection === sectionName) {
      setSectionName('');
      if (tableContainerRef.current) {
      }
    }
    if (params[`*`]?.includes('Location/report/sections')) {
      if (selectedSection === sectionName) {
        sessionStorage.setItem('reportSectionName', '');
        setSectionName('');
      } else {
        sessionStorage.setItem('reportSectionName', sectionName);
        setSectionName(sectionName);
      }
    } else if (params[`*`]?.includes('Location/live/sections')) {
      if (selectedSection === sectionName) {
        sessionStorage.setItem('liveSectionName', '');
        setSectionName('');
      } else {
        sessionStorage.setItem('liveSectionName', sectionName);
        setSectionName(sectionName);
      }
    }

    if (sectionName && sectionName.trim() !== '') {
      mixpanel.track('Section Click', {
        Page_Name: url,
        MshaId: decoded?.mshaId,
        Section_Name: sectionName,
      });
    }
  };

  const filteredSectionLength = filteredSection ? filteredSection.length : 0;

  return (
    <div className="w-full h-full flex flex-col  ">
      <div
        className={`${
          data?.sectionBreakup?.length > 0
            ? `agBreakup  grid xl:grid-cols-6 gap-4   px-[4%] w-full  sm:grid-cols-3 p-6 sticky top-28 z-30`
            : ' bg-[#2c5c8bf6] px-[4%] w-full sm:grid-cols-3 p-6 sticky top-28 z-30'
        }`}
      >
        {data?.sectionBreakup?.length > 0 ? (
          data.sectionBreakup.map((data: any, index: any) => (
            <div
              key={index}
              className={`h-[100px] cursor-pointer border border-[#4AA8FE] rounded-md flex justify-center items-center
         ${
           selectedSection === data.sectionName
             ? 'bg-[#21303C] shadow-section'
             : 'bg-transparent'
         }
        `}
              onClick={() => handleSectionClick(data.sectionName)}
            >
              <div className="flex flex-col">
                <span className="text-[#FFD084] text-center text-base py-0">
                  {data.sectionName}
                </span>
                <span className="text-[#FFFFFF] text-center text- font-bold text-5xl py-0">
                  {params[`*`]?.includes('/live/sections')
                    ? data.minersCount
                    : data.totalNodes}
                </span>
              </div>
            </div>
          ))
        ) : reportLoading || liveLoading ? (
          ''
        ) : (
          <div
            className="text-white font-semibold text-2xl text-center"
            dangerouslySetInnerHTML={{
              __html: `${
                params[`*`]?.includes('/live/sections')
                  ? 'Live data for section is currently unavailable'
                  : 'No report to display for selected date<br />Please choose a different date'
              }`,
            }}
          />
        )}
      </div>

      <div
        ref={tableContainerRef}
        className="  max-w-screen w-full overflow-y flex flex-col min-h-96"
      >
        <div className="text-white max-w-screen pt-6 px-[4%] w-full m-auto gap-1 ">
          {filteredSectionLength > 0 ? (
            filteredSection?.map((section: any, index: any) => (
              <div
                key={index}
                ref={
                  section.sectionName === selectedSection ? sectionRef : null
                }
                id={section.sectionName}
                className={`mb-4 bg-[#25354354] 
                ${
                  selectedSection === section.sectionName || !selectedSection
                    ? ''
                    : 'hidden '
                }
                 rounded-sm ${section.sectionName}`}
              >
                <div className="flex mb-2 ">
                  <h2 className=" flex text-xl font-bold mt-3 2xl:ml-6 xl:ml-4 w-1/2">
                    {section.sectionName}
                  </h2>
                  <span className="text-[#FFD084] text-sm text-end mt-4 w-full ">
                    {params[`*`]?.includes('/live/sections')
                      ? 'FIRST ARRIVAL'
                      : ' '}
                  </span>
                  <span className="text-[#FFD084] text-sm mt-4 ml-3 ">
                    {params[`*`]?.includes('/live/sections') ? (
                      <svg
                        width="14"
                        height="16"
                        viewBox="0 0 14 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M5.5 0H7H8.5C9.03125 0 9.5 0.46875 9.5 1C9.5 1.5625 9.03125 2 8.5 2H8V3.09375C9.15625 3.28125 10.2188 3.75 11.0938 4.46875L11.7812 3.8125C12.1562 3.40625 12.8125 3.40625 13.1875 3.8125C13.5938 4.1875 13.5938 4.84375 13.1875 5.21875L12.4375 5.96875C13.0938 7 13.5 8.21875 13.5 9.5C13.5 13.0938 10.5625 16 7 16C3.40625 16 0.5 13.0938 0.5 9.5C0.5 6.25 2.875 3.5625 6 3.09375V2H5.5C4.9375 2 4.5 1.5625 4.5 1C4.5 0.46875 4.9375 0 5.5 0ZM7.75 6C7.75 5.59375 7.40625 5.25 7 5.25C6.5625 5.25 6.25 5.59375 6.25 6V10C6.25 10.4375 6.5625 10.75 7 10.75C7.40625 10.75 7.75 10.4375 7.75 10V6Z"
                          fill="#FFD084"
                        />
                      </svg>
                    ) : (
                      ''
                    )}
                  </span>
                  <span className="text-[#FFD084] text-sm  mt-4 ml-4 mr-10 flex  2xl:w-28 xl:w-32">
                    {params[`*`]?.includes('/live/sections')
                      ? section.firstArrival
                      : ''}
                  </span>
                </div>
                <div className="w-[97%] m-auto h-full pt-0">
                  <Table
                    columns={SWcolumns}
                    data={
                      params[`*`]?.includes('/live/sections')
                        ? section?.miners ?? []
                        : section?.totalNodesBreakup ?? []
                    }
                    searchText={searchText}
                    searchOnColumn="minerName"
                    backgroundColor={false}
                    scrollable={true}
                    sortable={true}
                    dataRenderLimitMdScreen={4}
                    dataRenderLimitLgScreen={5}
                    tableHeightClassLg={`${
                      selectedSection ? 'h-[240px]' : 'h-[240px]'
                    }`}
                    tableHeightClassMd={`${
                      selectedSection ? 'h-full' : 'h-[240px]'
                    }`}
                  />
                </div>
              </div>
            ))
          ) : reportLoading || liveLoading ? (
            <div>
              <div className="flex justify-center items-center h-full">
                {<Loader />}
              </div>
              <div className="flex justify-center items-center h-full text-xl pt-2">
                Loading....
              </div>
            </div>
          ) : (
            ''
          )}
        </div>
      </div>
    </div>
  );
};
export default Section;
