import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from '../auth.service';
import {
  ForbiddenException,
  HttpException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { jwtConstants } from '../constant';
import { UsersService } from '../../users/users.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private userService: UsersService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      igonreExpiration: false,
      secretOrKey: jwtConstants.accessTokenSecret,
    });
  }
  async validate(payload: any) {
    const existingUser = await this.userService.findByUsername(
      payload.username
    );

    if (!existingUser || !existingUser.isActive || existingUser.isDelete) {
      throw new UnauthorizedException();
    }

    const mineStatus = await this.userService.findMineIsDeleted(
      payload.username
    );
    if (mineStatus.isMineDeleted == true || !mineStatus.isActive) {
      throw new UnauthorizedException();
    }

    const role = await this.userService.getUsersWithRole(payload.userId);

    //Added check for role if the role is not undefiend and role change then we should tell user that
    // role has bee changed for the user
    if (role[0]?.RoleName && payload.role != role[0]?.RoleName) {
      throw new ForbiddenException('Role changed');
    }

    // If role name is undefiend that means user is not assigned any role features so we will log out the user once he refresh the screen or try to navigate.
    if (!role[0]?.RoleName) {
      throw new UnauthorizedException();
    }
    return payload;
  }
}
