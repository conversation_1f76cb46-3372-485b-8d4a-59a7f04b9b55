import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { RoleFeatureAccessService } from './role_feature_access.service';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { CreateRoleFeatureAccessDto } from './dto/create-role-feature-access.dto';

@Controller('portal/v1/rolefeature')
@ApiTags('roleFeatures')
export class RoleFeatureAccessController {
  constructor(private readonly roleFeatureService: RoleFeatureAccessService) {}
  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Req() req) {
    let companyId =
      req.user.role == 'superuser'
        ? req.user.mineCompanyId
        : req.user.companyid;
    return this.roleFeatureService.findAll(companyId);
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(
    @Body() createRoleFeatureAccessDto: CreateRoleFeatureAccessDto,
    @Req() req
  ) {
    return this.roleFeatureService.create(createRoleFeatureAccessDto, req.user);
  }

  @Delete('/:id')
  @UseGuards(JwtAuthGuard)
  async delete(@Param('id') id: number, @Req() req) {
    return this.roleFeatureService.delete(id, req.user);
  }
}
