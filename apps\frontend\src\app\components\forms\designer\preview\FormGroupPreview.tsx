import { Id, Group, Item, SelectedItem, Question } from '../../types';
import { PlusIcon, TrashIcon, DragIcon, CloneIcon, LeftAlign, SingleArrow, DoubleArrow } from '../../../../../assets/icons/icons';
import FormItemPreview from "./FormItemPreview";
import FormItemHeaderPreview from "./FormItemHeaderPreview";
import { useState } from "react";

interface Props {
	showPreview: Boolean;
	group: Group;
	selectedItem: SelectedItem;
	createNewItem: (groupId: Id) => void;
	setSelectedItem: (item: SelectedItem) => void;
	moveGroup: (fromIndex: number, direction: string) => void;
	moveItem: (groupId: Id, fromIndex: number, direction: string) => void;
	deleteGroup: (id: Id) => void;
	cloneGroup: (group: Group) => void;
	cloneItem: (item: Item) => void;
	deleteItem: (item: Item) => void;
	items: Item[];
	questions: Question[];
}

interface Style {
	backgroundColor?: string,
	color?: string,
	margin?: string,
	marginTop?: string,
	marginBottom?: string,
	marginLeft?: string,
	marginRight?: string,
	padding?: string,
	paddingTop?: string,
	paddingBottom?: string,
	paddingLeft?: string,
	paddingRight?: string,
	width?: string,
	fontSize?: string,
}

const FormGroupPreview = ({showPreview, group, selectedItem, createNewItem,
		setSelectedItem, moveGroup, moveItem, deleteGroup, cloneGroup, cloneItem, deleteItem, items, questions
}: Props) => {
	const [mouseIsOver, setMouseIsOver] = useState(false);
	const properties = group.properties;

	const header = properties?.questionheader ?? '';
	const headerdisplay = properties?.questionheaderdisplay ?? null;
	const positiongroup = properties?.questionpositiongroup ?? null;
	const fUnitValueField = properties?.questionfontsizegroup?.fontsizeunitvaluefield ?? null;
	const fUnitTypeField = properties?.questionfontsizegroup?.fontsizeunittypefield ?? null;

	const textcolor = properties?.colorgroup?.color ?? null;
	const bgcolor = properties?.bgcolorgroup?.bgcolor ?? null;

	const wSizeValueField = properties?.questionwidthgroup?.sizeunitvaluefield ?? null;
	const wSizeUnitTypeField = properties?.questionwidthgroup?.widthunittypefield ?? null;
	const mUnitValueField = properties?.margingroup?.unitvaluefield ?? null;
	const mUnitTypeField = properties?.margingroup?.unittypefield ?? null;
	const mtUnitValueField = properties?.margintop?.unitvaluefield ?? null;
	const mtUnitTypeField = properties?.margintop?.unittypefield ?? null;
	const mbUnitValueField = properties?.marginbottom?.unitvaluefield ?? null;
	const mbUnitTypeField = properties?.marginbottom?.unittypefield ?? null;
	const mlUnitValueField = properties?.marginleft?.unitvaluefield ?? null;
	const mlUnitTypeField = properties?.marginleft?.unittypefield ?? null;
	const mrUnitValueField = properties?.marginright?.unitvaluefield ?? null;
	const mrUnitTypeField = properties?.marginright?.unittypefield ?? null;
	const pUnitValueField = properties?.paddinggroup?.unitvaluefield ?? null;
	const pUnitTypeField = properties?.paddinggroup?.unittypefield ?? null;
	const ptUnitValueField = properties?.paddingtop?.unitvaluefield ?? null;
	const ptUnitTypeField = properties?.paddingtop?.unittypefield ?? null;
	const pbUnitValueField = properties?.paddingbottom?.unitvaluefield ?? null;
	const pbUnitTypeField = properties?.paddingbottom?.unittypefield ?? null;
	const plUnitValueField = properties?.paddingleft?.unitvaluefield ?? null;
	const plUnitTypeField = properties?.paddingleft?.unittypefield ?? null;
	const prUnitValueField = properties?.paddingright?.unitvaluefield ?? null;
	const prUnitTypeField = properties?.paddingright?.unittypefield ?? null;
	const justifytypefield = properties?.rowgroup?.justifytypefield ?? null;
	const flextypefield = properties?.rowgroup?.flextypefield ?? null;
	
	const containerStyle:Style = {};
	const style:Style = {};
	const questionStyle:Style = {};
	let margin, marginTop, marginBottom, marginLeft, marginRight;
	if(mUnitTypeField !== 'individual') {
		margin = `${mUnitValueField ?? ''}${mUnitTypeField ?? ''}`;
	}
	else {
		marginTop = `${mtUnitValueField ?? ''}${mtUnitTypeField ?? ''}`;
		marginBottom = `${mbUnitValueField ?? ''}${mbUnitTypeField ?? ''}`;
		marginLeft = `${mlUnitValueField ?? ''}${mlUnitTypeField ?? ''}`;
		marginRight = `${mrUnitValueField ?? ''}${mrUnitTypeField ?? ''}`;
	}

	let padding, paddingTop, paddingBottom, paddingLeft, paddingRight;
	if(pUnitTypeField !== 'individual') {
		padding = `${pUnitValueField ?? ''}${pUnitTypeField ?? ''}`;
	}
	else {
		paddingTop = `${ptUnitValueField ?? ''}${ptUnitTypeField ?? ''}`;
		paddingBottom = `${pbUnitValueField ?? ''}${pbUnitTypeField ?? ''}`;
		paddingLeft = `${plUnitValueField ?? ''}${plUnitTypeField ?? ''}`;
		paddingRight = `${prUnitValueField ?? ''}${prUnitTypeField ?? ''}`;
	}
	
	let width = '';
	let widthClass = '';
	if(wSizeUnitTypeField) {
		let type = wSizeUnitTypeField ?? 'w-fit';
		let value = wSizeValueField ?? 0;
		if(type == 'px' || type == '%') {
			width = `${value}${type}`;
		}
		else {
			widthClass = type;
		}
	}
	
	let fontSize = '';
	let fontSizeClass = '';
	if(fUnitTypeField) {
		let type = fUnitTypeField ?? 'base';
		let value = fUnitValueField ?? 1;
		if(type == 'px' || type == 'rem') {
			fontSize = `${value}${type}`;
		}
		else {
			fontSizeClass = type;
		}
	}

	let justifyClass = '';
	if(justifytypefield) {
		justifyClass = justifytypefield ?? 'justify-normal';
	}

	let flexClass = '';
	if(flextypefield) {
		flexClass = flextypefield ?? 'flex-nowrap';
	}
	
	const backgroundColor = `${bgcolor ?? ''}`;
	const color = `${textcolor ?? ''}`;

	if(fontSize && fontSize.length > 0) {style.fontSize = fontSize;}
	if(backgroundColor && backgroundColor.length > 0) {containerStyle.backgroundColor = backgroundColor;}
	if(color && color.length > 0) {style.color = color;}
	if(margin && margin.length > 0) {style.margin = margin;}
	if(marginTop && marginTop.length > 0) {style.marginTop = marginTop;}
	if(marginBottom && marginBottom.length > 0) {style.marginBottom = marginBottom;}
	if(marginLeft && marginLeft.length > 0) {style.marginLeft = marginLeft;}
	if(marginRight && marginRight.length > 0) {style.marginRight = marginRight;}
	if(padding && padding.length > 0) {style.padding = padding;}
	if(paddingTop && paddingTop.length > 0) {style.paddingTop = paddingTop;}
	if(paddingBottom && paddingBottom.length > 0) {style.paddingBottom = paddingBottom;}
	if(paddingLeft && paddingLeft.length > 0) {style.paddingLeft = paddingLeft;}
	if(paddingRight && paddingRight.length > 0) {style.paddingRight = paddingRight;}
	if(width && width.length > 0) {questionStyle.width = width;}

	function handleCreateNewItemButtonClick(e: React.MouseEvent<HTMLButtonElement>, id: Id) {
		e.stopPropagation();
		createNewItem(group.id);
	}

	function handleCloneGroupButtonClick(e: React.MouseEvent<HTMLButtonElement>, group: Group) {
		e.stopPropagation();
		cloneGroup(group);
	}

	function handleDeleteGroupButtonClick (e: React.MouseEvent<HTMLButtonElement>, id: Id) {
		e.stopPropagation();
		deleteGroup(id);
	}

	function handleMoveGroupButtonClick (e: React.MouseEvent<HTMLButtonElement>, fromIndex: number, direction: string) {
		e.stopPropagation();
		moveGroup(fromIndex, direction);
	}

	function handleFormGroupSelect(e: React.MouseEvent<HTMLDivElement>, id: Id) {
		e.stopPropagation();
		const selectedItem = { id: id, group: group };
		setSelectedItem(selectedItem);
	}

	return (
		<div
			onMouseEnter={() => setMouseIsOver(true)}
			onMouseLeave={() => setMouseIsOver(false)}
			style={containerStyle}
			onClick={(e) => handleFormGroupSelect(e, group.id)}
			className={`
				relative my-2 w-full flex flex-col p-1
				${selectedItem.id == group.id && !showPreview ? 'selected' : '' }
				${!showPreview ? 'template-group hover:ring cursor-pointer' : 'template-group-preview'}
			`}
			key={group.name}
		>
			{!showPreview &&
				<div className={`item-label w-full flex flex-wrap justify-between h-fit`}>
					<div className={`flex justify-start`}>
						<div
							className={`py-1 px-0 mr-2 flex gap-1 ${mouseIsOver ? 'text-white/100' : 'text-white/60'}`}>
							{group.name}
						</div>
						<button
							title="Click to move group to top"
							onClick={(e) => handleMoveGroupButtonClick(e, group.index, 'top')}
							className="opacity-40 hover:opacity-100 py-1 px-0.5">
							<DoubleArrow className="rotate-90 w-4 h-4" />
						</button>
						<button
							title="Click to move group up"
							onClick={(e) => handleMoveGroupButtonClick(e, group.index, 'up')}
							className="opacity-40 hover:opacity-100 py-1 px-0.5">
							<SingleArrow className="rotate-90 w-4 h-4" />
						</button>
						<button
							title="Click to move group down"
							onClick={(e) => handleMoveGroupButtonClick(e, group.index, 'down')}
							className="opacity-40 hover:opacity-100 py-1 px-0.5">
							<SingleArrow className="rotate-270 w-4 h-4" />
						</button>
						<button
							title="Click to move group to bottom"
							onClick={(e) => handleMoveGroupButtonClick(e, group.index, 'bottom')}
							className="opacity-40 hover:opacity-100 py-1 px-0.5">
							<DoubleArrow className="rotate-270 w-4 h-4" />
						</button>
					</div>
					<div className={`flex justify-end`} >
						<button
							title="Click to clone group"
							onClick={(e) => handleCloneGroupButtonClick(e, group)}
							className="opacity-40 hover:opacity-100 py-1 px-0.5">
							<CloneIcon />
						</button>
						<button
							title="Click to remove group"
							onClick={(e) => handleDeleteGroupButtonClick(e, group.id)}
							className="opacity-40 hover:opacity-100 py-1 px-0.5">
							<TrashIcon className="text-gray-500 font-black text-[14px] h-4 w-4" />
						</button>
					</div>
				</div>
			}
			<div className={`${!showPreview ? 'my-2' : ''}`}>
				{headerdisplay && headerdisplay === 'Header' &&
					<div
						style={style} className={`
							flex flex-grow overflow-x-hidden overflow-y-auto items-top
							${justifyClass}
							${flexClass}
						`}>
						{header && questions.length > 0 &&
							<div
								style={questionStyle}
								className={`
									p-2 flex items-top flex-wrap h-fit
									text-${fontSizeClass ?? ''}
									text-${positiongroup ?? 'left'}
									${justifyClass}
									${widthClass ?? ''}
								`}
							>
								<label className={`w-full block`}>{header}</label>	
							</div>
						}
						{items.map(item => (
							<FormItemHeaderPreview
								key={`${item.id}-label`}
								item={item}
							/>
						))}
					</div>
				}
				{questions.length > 0 && questions.map((question,index) => {
					return (
						<div key={question.id} className={`w-full p-0 ${!showPreview && index == 0 ? 'mb-4' : 'm-0'}`}>
							<div
								style={style}
								key={question.id}
								className={`
									flex flex-grow overflow-x-hidden overflow-y-auto items-start
									${!showPreview ? 'cursor-pointer' : ''}
									${justifyClass}
									${flexClass}
								`}>
								<div
									style={questionStyle}
									className={`
										text-${fontSizeClass ?? ''}
										text-${positiongroup ?? 'left'}
										${widthClass ?? ''}
										px-2 py-4 form-question
									`}
								>
									{question.name}
								</div>
								{items.map(item => (
									<FormItemPreview
										key={`${question.id}-${item.id}`}
										itemGroup={`${group.id}-${question.id}`}
										showPreview={showPreview}
										item={item}
										selectedItem={selectedItem}
										setSelectedItem={setSelectedItem}
										cloneItem={cloneItem}
										moveItem={moveItem}
										deleteItem={deleteItem}
										questionIndex={index}
									/>
								))}
							</div>
						</div>
					)
				})}
				{questions.length <= 0 &&
					<div
						style={style} className={`
							flex flex-grow overflow-x-hidden overflow-y-auto
							${justifyClass}
							${flexClass}
						`}>
						{items.map(item => (
							<FormItemPreview
								key={item.id}
								itemGroup={`${group.id}`}
								showPreview={showPreview}
								item={item}
								selectedItem={selectedItem}
								setSelectedItem={setSelectedItem}
								cloneItem={cloneItem}
								moveItem={moveItem}
								deleteItem={deleteItem}
								questionIndex={0}
							/>
						))}
					</div>
				}
			</div>
			{!showPreview &&
				// <div className="flex w-full justify-center mb-2">
				// 	<button
				// 		onClick={(e) => handleCreateNewItemButtonClick(e, group.id)}
				// 		className="flex w-fit gap-2 items-center cursor-pointer
				// 		border-columnBackgroundColor border rounded-md p-4
				// 		border-x-columnBackgroundColor hover:bg-mainBackgroundColor
				// 		hover:ring"><PlusIcon />Add Item</button>
				// </div>
				// <div className="flex w-full justify-center">
				// 	<button
				// 		title="Click to add item"
				// 		onClick={(e) => handleCreateNewItemButtonClick(e, group.id)}
				// 		className=" h-fit w-fit cursor-pointer justify-center
				// 		bg-mainBackgroundColor flex">
				// 		<PlusIcon className="w-6 h-6 opacity-80 hover:opacity-100" />
				// 	</button>
				// </div>
				<div className="flex w-full justify-center">
					<button
						title="Click to add item"
						onClick={(e) => handleCreateNewItemButtonClick(e, group.id)}
						className=" h-fit w-fit cursor-pointer justify-center px-2
						border-columnBackgroundColor border rounded-md border-x-columnBackgroundColor
						bg-mainBackgroundColor flex opacity-40 hover:opacity-100">
						Add Item
					</button>
				</div>
			}
		</div>
	);
};

export default FormGroupPreview;