import { ApiClient } from '../apiClient';

export interface CompanyData {
  id?: number;
  companyName: string;
  companyCode: string;
  mineName: string;
  mineCode: string;
  location: string;
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  createdBy: number;
}

export const addCompany = async (data: CompanyData) =>
  await ApiClient.post('api/portal/v1/company-onboard', data);

export const editCompany = async (id: number, data: CompanyData) =>
  await ApiClient.patch(`api/portal/v1/companies/${id}`, data);

export const getCompanies = async () =>
  await ApiClient.get('api/portal/v1/companies');

export const checkCompanyName = async (data: any) =>
  await ApiClient.post('api/portal/v1/companies/checkcompanyname', data);

export const getMine = async (data: any) =>
  await ApiClient.post('api/portal/v1/mines/checkminename', data);

export const getMineName = async (id: any) =>
  await ApiClient.get(`api/portal/v1/mines/${id}`);

export const deleteMine = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/mines/isDelete/${id}`);
};

export const statusChange = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/mines/status/${id}`);
};
