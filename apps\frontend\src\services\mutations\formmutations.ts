import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  addForm,
  getForms,
  checkFormByName,
  getFile,
  getForm,
  editForm,
  submitForm,
  revokeSubmission,
  deleteForm,
  FormData,
} from '../../api/forms/formapis';

export function useFindByFormName() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (name: string) => checkFormByName(name),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['check-form-name'] });
      }
    },
  });
}

export function useAddForm() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: FormData) => addForm(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-form'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form','in-progress-forms'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useSaveForm() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: FormData) => {
      const id: any = data?.id;
      delete data.id;
      return editForm(id, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['save-form'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form','in-progress-forms'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useSubmitForm() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: any) => submitForm(id),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['submit-form'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form','in-progress-forms', 'completed-forms', 'form-categories-completed'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useRevokeForm() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: any) => revokeSubmission(id),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['revoke-form'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form','in-progress-forms', 'completed-forms'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useDeleteForm() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteForm(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['delete-form'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['form','in-progress-forms'].includes(query.queryKey[0]);
        }
      });
    },
  });
}