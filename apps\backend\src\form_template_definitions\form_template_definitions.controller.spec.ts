import { Test, TestingModule } from '@nestjs/testing';
import { FormTemplateDefinitionsController } from './form_template_definitions.controller';
import { FormTemplateDefinitionsService } from './form_template_definitions.service';
import { CreateFormTemplateDefinitionDto } from './dto/create-form-template-definitions.dto';
import { FormTemplateDefinition } from './entities/form_template_definitions.entity';
import { UpdateFormTemplateDefinitionDto } from './dto/update-form-template-definitions.dto';

describe('FormTemplateDefinitionsController', () => {
  let controller: FormTemplateDefinitionsController;

  const mockFormTemplateDefinitionService = {
    create: jest.fn(),
    findAll: jest.fn(),
    getFormTemplateDefinitionsByMineId: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FormTemplateDefinitionsController],
      providers: [
        {
          provide: FormTemplateDefinitionsService,
          useValue: mockFormTemplateDefinitionService,
      }]
    }).compile();

    controller = module.get<FormTemplateDefinitionsController>(FormTemplateDefinitionsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('create => should create a new form template definition by the given data', async () => {
    // arrange
    let now = new Date().toISOString();
    const createFormTemplateDefinitionDto = {
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
      createdBy: 1,
    } as CreateFormTemplateDefinitionDto;

    const formTemplateDefinition = {
      id: 1,
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
      createdBy: 1,
    } as FormTemplateDefinition;

    jest.spyOn(mockFormTemplateDefinitionService, 'create').mockReturnValue(formTemplateDefinition);

    // act
    const result = await controller.create(createFormTemplateDefinitionDto);

    // assert
    expect(mockFormTemplateDefinitionService.create).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionService.create).toHaveBeenCalledWith(createFormTemplateDefinitionDto);
    expect(result).toEqual(formTemplateDefinition);
  });

  it('findAll => should return an array of form template definitions', async () => {
    // arrange
    const formTemplateDefinition = {
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    };
    let mineId = 1;
    const req = { user: { mineId: mineId } };
    const formTemplateDefinitions = [formTemplateDefinition];
    jest.spyOn(mockFormTemplateDefinitionService, 'findAll').mockReturnValue(formTemplateDefinitions);

    // act
    const result = await controller.findAll(req);

    // assert
    expect(mockFormTemplateDefinitionService.findAll).toHaveBeenCalled();
    expect(result).toEqual(formTemplateDefinitions);
  });

  it('findOne => should find a form template definition by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const formTemplateDefinition = {
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    };

    jest.spyOn(mockFormTemplateDefinitionService, 'findOne').mockReturnValue(formTemplateDefinition);

    // act
    const result = await controller.findOne(id);

    // assert
    expect(mockFormTemplateDefinitionService.findOne).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionService.findOne).toHaveBeenCalledWith(+id);
    expect(result).toEqual(formTemplateDefinition);
  });

  it('update => should find a form template definition by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateFormTemplateDefinitionDto = {
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
      isPublished: false,
      publishedBy: 1,
    } as UpdateFormTemplateDefinitionDto;
    const formTemplateDefinition = {
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
      isPublished: false,
      publishedBy: 1,
    };

    jest.spyOn(mockFormTemplateDefinitionService, 'update').mockReturnValue(formTemplateDefinition);

    // act
    const result = await controller.update(id, updateFormTemplateDefinitionDto);

    // assert
    expect(mockFormTemplateDefinitionService.update).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionService.update).toHaveBeenCalledWith(+id, updateFormTemplateDefinitionDto);
    expect(result).toEqual(formTemplateDefinition);
  });

  it('remove => should find a form template definition by a given id, remove and then return number of affected rows', async () => {
    // arrange
    const id = 1;
    const formTemplateDefinition = {
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    };

    jest.spyOn(mockFormTemplateDefinitionService, 'remove').mockReturnValue(formTemplateDefinition);

    // act
    const result = await controller.remove(id);

    // assert
    expect(mockFormTemplateDefinitionService.remove).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionService.remove).toHaveBeenCalledWith(+id);
    expect(result).toEqual(formTemplateDefinition);
  });
});
