import { Module } from '@nestjs/common';
import { CompaniesController } from './companies.controller';
import { CompaniesService } from './companies.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Company } from './entities/company.entity';
import { MinesModule } from '../mines/mines.module';
import { RolesModule } from '../roles/roles.module';
import { FeaturesModule } from '../features/features.module';
import { RoleFeatureAccessModule } from '../role_feature_access/role_feature_access.module';
import { UsersModule } from '../users/users.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([Company]),
    MinesModule,
    RolesModule,
    FeaturesModule,
    RoleFeatureAccessModule,
    UsersModule,
  ],
  controllers: [CompaniesController],
  providers: [CompaniesService],
  exports: [CompaniesService],
})
export class CompaniesModule {}
