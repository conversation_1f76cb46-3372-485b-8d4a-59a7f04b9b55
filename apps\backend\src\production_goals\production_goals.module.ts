import { Module } from '@nestjs/common';
import { ProductionGoalsController } from './production_goals.controller';
import { ProductionGoalsService } from './production_goals.service';
import { ProductionGoal } from './entities/production_goal.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditModule } from '../audit/audit.module';
import { SectionsModule } from '../sections/sections.module';
import { ShiftsModule } from '../shifts/shifts.module';
import { MinesModule } from '../mines/mines.module';
//import { SectionsModule } from '../sections/sections.module';
//import { ShiftsModule } from '../shifts/shifts.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductionGoal]),
    AuditModule,
    SectionsModule,
    ShiftsModule,
    MinesModule,
    //SectionsModule, ShiftsModule
  ],
  controllers: [ProductionGoalsController],
  providers: [ProductionGoalsService],
  exports: [ProductionGoalsService],
})
export class ProductionGoalsModule {}
