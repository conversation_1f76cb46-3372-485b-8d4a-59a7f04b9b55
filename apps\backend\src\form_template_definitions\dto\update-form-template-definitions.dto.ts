import {
  IsDefined,
  IsNotEmpty,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateFormTemplateDefinitionDto {  
  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  definition: string;
  
  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  major: number;
  
  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  minor: number;
  
  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  revision: number;

  isDelete?: boolean;
  isPublished?: boolean;
}
