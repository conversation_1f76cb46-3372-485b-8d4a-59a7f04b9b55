import { useQuery } from '@tanstack/react-query';
import { AlertTableCompliance } from '../../../../src/assets/icons/icons';
import Table, { ColumnDef } from '../common/Table';
import {
  getATMDashboard,
  getATMLiveNotification,
} from '../../../../src/api/atmosphere/atmoshpere';
import decodeJWT from '../../../../src/utils/jwtdecoder';
import { Link } from 'react-router-dom';
import { useState } from 'react';
import Loader from '../common/Loader';
import { UserMenu } from '../common/UserMenu';
import dayjs from 'dayjs';
import mixpanel from 'mixpanel-browser';

const AtmosphereDashboard = () => {
  const [selectedCard, setSelectedCard] = useState('');

  const decoded = decodeJWT();

  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ['dashboardData'],
    queryFn: () => getATMDashboard(decoded?.mineid),
    retry: false,
    refetchOnWindowFocus: false,
  });

  const lastUpdatedDate = dashboardData?.data?.sensorData?.lastUpdatedTs;
  // Parse input datetime string
  const datetime = dayjs(lastUpdatedDate);

  // Format the date
  const formattedDate = ` Today ` + datetime.format('[@] hh:mm a');

  const { data: notificationData, isLoading: isLoadingNotification } = useQuery(
    {
      queryKey: ['notificationData'],
      queryFn: () => getATMLiveNotification(decoded?.mineid),
      refetchOnWindowFocus: false,
    }
  );
  let dashboardCardArr = [
    { heading: 'Alarms', value: 0 },
    { heading: 'Alerts', value: 0 },
    { heading: 'Faults', value: 0 },
    { heading: 'Ventilation', value: 0 },
  ];

  dashboardCardArr = dashboardCardArr.map((card) => {
    const matchedState = dashboardData?.data?.sensorData.stateData.find(
      (state: any) =>
        state.moduleStateName.toLowerCase() === card.heading.toLowerCase()
    );
    return {
      ...card,
      value: matchedState ? matchedState.count : 0,
    };
  });

  const columns: ColumnDef[] = [
    {
      key: 'timeLogged',
      label: 'Time Logged',
      type: 'text',
    },
    {
      key: 'type',
      label: 'Type',
      type: 'text',
    },
    {
      key: 'section',
      label: 'Section',
      type: 'text',
    },
    {
      key: 'label',
      label: 'Label',
      type: 'text',
    },
    {
      key: 'id',
      label: 'ID',
      type: 'text',
    },
    {
      key: 'compliance',
      label: 'Compliance',
      type: 'element',
      render: (row) => {
        return (
          <div className={`${row?.isCompliance ? '' : 'invisible'}`}>
            <AlertTableCompliance className="m-auto" />
          </div>
        );
      },
    },
  ];

  const data = dashboardData?.data?.sensorData?.timeSeriesData?.map(
    (ele: any) => {
      return {
        timeLogged: ele?.timestamp,
        type: ele?.moduleType,
        section: ele?.sectionName,
        label: ele?.sensorLabel,
        id: ele?.nid,
        isCompliance: ele?.isCompliance,
        moduleStateId: ele?.moduleStateId,
      };
    }
  );

  const systemStats = [
    {
      label: 'Total Monitors',
      value: dashboardData?.data.systemStats.totalMonitors ?? '-',
    },
    { label: 'WGM', value: dashboardData?.data.systemStats.WGM ?? '-' },
    {
      label: 'Offlines',
      value: dashboardData?.data.systemStats.offline ?? '-',
    },
    { label: 'WGM+', value: dashboardData?.data.systemStats.WGMPlus ?? '-' },
    { label: 'Onlines', value: dashboardData?.data.systemStats.online ?? '-' },
    {
      label: 'Ventilation Enabled',
      value: dashboardData?.data.systemStats.ventilationEnabled ?? '-',
    },
  ];

  // Populate maintenanceStats array
  const maintenanceStats = [
    {
      label: 'Calibration Overdue',
      value: dashboardData?.data.maintenanceStats.calibrationsDue || '-',
    },
    {
      label: 'Calibration in <5 days',
      value: dashboardData?.data.maintenanceStats.calibrationsNext7Days || '-',
    },
    {
      label: 'Battery Life <10%',
      value: dashboardData?.data.maintenanceStats.maintenanceDue || '-',
    },
  ];

  const selectedStateId = dashboardData?.data?.sensorData?.stateData?.find(
    (ele: any) => ele?.moduleStateName == selectedCard
  )?.moduleStateId;

  return (
    <>
      {isLoading && isLoadingNotification ? (
        <>
          <div className="h-[50vh] flex items-center w-full justify-center">
            <Loader />
          </div>
        </>
      ) : (
        <>
          <div className="sticky w-full top-0 z-30  agBreakup2  bg-top px-10 2xl:px-16">
            <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe]  pb-1 pt-4 ">
              <div className="">
                <h6 className="font-bold text-white text-[32px]">Dashboard</h6>
              </div>

              <div className="">
                <div className="flex justify-end items-center -mr-10 relative">
                  <h6 className="font-bold text-white text-[32px]">
                    {' '}
                    Atmosphere
                  </h6>
                  <span className="ml-4">
                    <UserMenu />
                  </span>
                </div>
                <div className="mb-2 text-right">
                  <span className="text-[#ffb132] text-[12px]">
                    Last Updated:{' '}
                  </span>
                  <span className="text-white text-[12px] mr-2">
                    {formattedDate}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* cards */}
          <div className="grid grid-cols-4 gap-4 pt-6 w-full  px-[4%] mx-auto bg-[#2c5c8bf6] pb-6">
            {dashboardCardArr?.map((ele: any, index: number) => (
              <div
                key={index}
                className={`h-[100px] border-[#4AA8FE] rounded-lg border-[1px] cursor-pointer ${
                  selectedCard === ele?.heading
                    ? 'bg-[#21303C] shadow-section'
                    : 'bg-transparent'
                }`}
                onClick={() => {
                  setSelectedCard((prev) =>
                    prev === ele?.heading ? null : ele?.heading
                  );
                  mixpanel.track('State Filter', {
                    State_Name: ele.heading,
                    MshaId: decoded?.mshaId,
                    MineName: decoded?.minename,
                  });
                }}
              >
                <div style={{ overflow: 'hidden' }}>
                  <div className="text-center text-[14px] text-[#FFD084] font-normal pt-3 overflow-hidden">
                    {ele?.heading}
                    <div className="text-center text-[32px] text-white font-bold pt-3">
                      {ele?.value}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Table */}
          <div className=" pb-2  px-[4%]  bg-[#2c5c8bf6]">
            <div className="flex justify-between">
              <div className="block">
                <h6 className="font-[700] text-[24px] text-white">
                  Active Notifications (
                  {data?.length || 0})
                </h6>
              </div>
              <div
                onClick={() => {
                  mixpanel.track('View All Notification', {
                    MshaId: decoded?.mshaId,
                    MineName: decoded?.minename,
                  });
                }}
              >
                <Link
                  to={`/app/Mines/${decoded?.mineid}/Atmosphere/live/Notifications`}
                  id="View all from today"
                  title="View all from today"
                  className="text-[#FFD084]  underline  text-sm px-10 py-2 text-center  items-center  me-2 mb-2"
                >
                  View all from today
                </Link>
              </div>
            </div>
            <div className="mt-5 h-[50vh] overflow-auto">
              {data?.length > 0 ? (
                <div>
                  <Table
                    columns={columns}
                    data={
                      selectedCard
                        ? data.filter(
                            (ele: any) => ele?.moduleStateId == selectedStateId
                          )
                        : data ?? []
                    }
                    searchText={''}
                    sortable={false}
                    searchOnColumn={''}
                    separateLine={false}
                    scrollable={true}
                    dataRenderLimitMdScreen={8}
                    dataRenderLimitLgScreen={15}
                    tableHeightClassLg="tableheightForlg"
                    tableHeightClassMd="tableheightFormd"
                  />
                </div>
              ) : (
                <div className="text-[26px] font-bold  text-center text-white">
                  There are currently no active notifications data
                </div>
              )}
            </div>
          </div>

          <div className="flex px-[4%]  justify-between text-white bg-[#2c5c8bf6] py-6">
            {/* System Section */}
            <div className="w-[40%]">
              <h2 className="text-xl font-semibold mb-4">System</h2>
              <div className="grid grid-cols-2 gap-4">
                {systemStats.map((stat, idx) => (
                  <div key={idx} className="border rounded-md p-4 shadow-sm">
                    <p className="text-sm text-[#FFD084]">{stat.label}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Maintenance Section */}
            <div className="opacity-30 w-[40%]">
              <h2 className="text-xl font-semibold mb-4">Maintenance</h2>
              <div className="grid grid-cols-2 gap-4">
                {maintenanceStats.map((stat, idx) => (
                  <div key={idx} className="border rounded-md p-4 shadow-sm">
                    <p className="text-sm text-[#FFD084]">{stat.label}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default AtmosphereDashboard;
