import {
  Column,
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
} from 'typeorm';
import { ProductionGoal } from '../../production_goals/entities/production_goal.entity';

@Entity('sections')
export class Section {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'mine_id' })
  mineId: number;

  @Column({ name: 'ext_section_name' })
  sectionName: string;

  @Column({ name: 'is_active' })
  isActive: number;

  @OneToMany(() => ProductionGoal, (goal) => goal.section)
  sections: Section[];
}
