import { Test, TestingModule } from '@nestjs/testing';
import { DateSelectionController } from './date_selection.controller';
import { DateSelectionService } from './date_selection.service';

describe('DateSelectionController', () => {
  let controller: DateSelectionController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DateSelectionController],
      providers: [DateSelectionService],
    }).compile();

    controller = module.get<DateSelectionController>(DateSelectionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
