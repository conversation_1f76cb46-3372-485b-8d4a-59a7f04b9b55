import { ConfigModule, ConfigService } from '@nestjs/config';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';
import { FeaturesModule } from '../features/features.module';
import { RolesModule } from '../roles/roles.module';
import { CompaniesModule } from '../companies/companies.module';
import { CategoriesModule } from '../categories/categories.module';
import { WatchlistsModule } from '../watchlists/watchlists.module';
import { MinersModule } from '../miners/miners.module';
import { MinesModule } from '../mines/mines.module';
import { CompanyOnboardModule } from '../company_onboard/company_onboard.module';
import { DateSelectionModule } from '../date_selection/date_selection.module';
import { ProductionGoalsModule } from '../production_goals/production_goals.module';
import { ShiftsModule } from '../shifts/shifts.module';
import { SectionsModule } from '../sections/sections.module';
import { SendgridService } from '../common/sendgrid/sendgrid.service';
import { EngineApiModule } from '../engineapi/engineapi.module';
import { ComplianceModule } from '../compliance/compliance.module';
import { RoleFeatureAccessModule } from '../role_feature_access/role_feature_access.module';
import { AuditModule } from '../audit/audit.module';
import { TimezonesModule } from '../timezones/timezones.module';
import { FormCategoriesModule } from '../form_categories/form_categories.module';
import { FormTemplatesModule } from '../form_templates/form_templates.module';
import { FormTemplateDefinitionsModule } from '../form_template_definitions/form_template_definitions.module';
import { FormsModule } from '../forms/forms.module';
import { ServeStaticModule } from '@nestjs/serve-static/dist/serve-static.module';
import { join } from 'node:path';
@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRoot({
      type: 'mssql',
      host: process.env.MSSQL_HOST,
      port: parseInt(process.env.MSSQL_PORT),
      username: process.env.MSSQL_USERNAME,
      password: process.env.MSSQL_PASSWORD,
      database: process.env.MSSQL_DATABASE,
      options: {
        encrypt: process.env.SSL_ENCRYPT === 'true', // MSSQL-specific option
      },
      synchronize: false, //use this with development environment
      autoLoadEntities: true,
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
      exclude: ['/api/{*test}'],
      serveStaticOptions: {
        fallthrough: false,
      }
    }),
    AuthModule,
    UsersModule,
    FeaturesModule,
    RolesModule,
    CompaniesModule,
    MinesModule,
    CategoriesModule,
    FeaturesModule,
    WatchlistsModule,
    MinersModule,
    CompanyOnboardModule,
    DateSelectionModule,
    ProductionGoalsModule,
    ShiftsModule,
    SectionsModule,
    EngineApiModule,
    ComplianceModule,
    RoleFeatureAccessModule,
    AuditModule,
    TimezonesModule,
    FormCategoriesModule,
    FormTemplatesModule,
    FormTemplateDefinitionsModule,
    FormsModule,
  ],
  controllers: [AppController],
  providers: [AppService, SendgridService, ConfigService],
})
export class AppModule {}
