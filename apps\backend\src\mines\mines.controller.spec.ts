import { Test, TestingModule } from '@nestjs/testing';
import { MinesController } from './mines.controller';
import { MinesService } from './mines.service';
import { Mine } from './entities/mine.entity';
import { CreateMineDto } from './dtos/create-mine.dto';
import { UpdateMineDto } from './dtos/update-mine.dto';
import { ChangeStatusDto } from './dtos/change-status.dto';

describe('MinesController', () => {
  let minesController: MinesController;
  const mockMineService = {
    create: jest.fn(),
    findAll: jest.fn(),
    getMine: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    changeStatus: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MinesController],
      providers: [
        {
          provide: MinesService,
          useValue: mockMineService,
        },
      ],
    }).compile();

    minesController = module.get<MinesController>(MinesController);
  });

  it('should be defined', () => {
    expect(minesController).toBeDefined();
  });
  it('createMine => should create a new mine by the given data', async () => {
    // arrange
    const createMineDto = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    } as CreateMineDto;

    const mine = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    } as Mine;

    jest.spyOn(mockMineService, 'create').mockReturnValue(mine);

    // act
    const result = await minesController.createMine(createMineDto);

    // assert
    expect(mockMineService.create).toHaveBeenCalled();
    expect(mockMineService.create).toHaveBeenCalledWith(createMineDto);
    expect(result).toMatchObject(mine);
  });
  it('findAll => should return an array of mines', async () => {
    // arrange
    const companyId = '1';
    const mine = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    };

    const mines = [mine];
    jest.spyOn(mockMineService, 'findAll').mockReturnValue(mines);

    // act
    const result = await minesController.findAll(companyId);

    // assert
    expect(mockMineService.findAll).toHaveBeenCalled();
    expect(result).toEqual(mines);
  });
  it('getMine => should find a mine by a given id and return its data', async () => {
    // arrange
    const id = '1';
    const mine = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    };

    jest.spyOn(mockMineService, 'getMine').mockReturnValue(mine);

    // act
    const result = await minesController.getMine(id);

    // assert
    expect(mockMineService.getMine).toHaveBeenCalled();
    expect(mockMineService.getMine).toHaveBeenCalledWith(+id);
    expect(result).toEqual(mine);
  });
  it('updateMine => should find a mine by a given id and update its data', async () => {
    // arrange
    const id = '1';
    const updateMineDto = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    } as UpdateMineDto;

    const mine = {
      companyId: 1,
      name: 'new test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    } as Mine;

    jest.spyOn(mockMineService, 'update').mockReturnValue(mine);

    // act
    const result = await minesController.updateMine(id, updateMineDto);

    // assert
    expect(mockMineService.update).toHaveBeenCalled();
    expect(mockMineService.update).toHaveBeenCalledWith(+id, updateMineDto);
    expect(result).toMatchObject(mine);
  });
  it('deleteMine => should find a mine by a given id, remove and then return number of affected rows', async () => {
    // arrange
    const id = '1';
    const mine = {
      companyId: 1,
      name: 'new test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    };

    jest.spyOn(mockMineService, 'delete').mockReturnValue(mine);

    // act
    const result = await minesController.deleteMine(id);

    // assert
    expect(mockMineService.delete).toHaveBeenCalled();
    expect(mockMineService.delete).toHaveBeenCalledWith(+id);
    expect(result).toEqual(mine);
  });
  it('changeStatus => should find a mine by a given id and update its status', async () => {
    // arrange
    const id = '1';
    const changeStatusDto = {
      isActive: false,
    } as ChangeStatusDto;

    const mine = {
      companyId: 1,
      name: 'new test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: false,
    } as Mine;

    jest.spyOn(mockMineService, 'changeStatus').mockReturnValue(mine);

    // act
    const result = await minesController.changeStatus(id, changeStatusDto);

    // assert
    expect(mockMineService.changeStatus).toHaveBeenCalled();
    expect(mockMineService.changeStatus).toHaveBeenCalledWith(
      +id,
      changeStatusDto.isActive
    );
    expect(result).toMatchObject(mine);
  });
});
