import { ApiClient } from '../apiClient';

export interface AuthenticationData {
  username: string;
  password: string;
}

export interface UserData {
  username: string;
  companyId: number;
  password: string;
  firstName: string;
  lastName: string;
  email: string;
  roleId: number;
  isActive?: boolean;
  contactNumber?: string;
  id?: number;
}

export const login = async (data: AuthenticationData) => {
  return await ApiClient.post('/api/portal/v1/auth/login', data);
};

export const checkUsername = async (username: string) => {
  return await ApiClient.get(`/api/portal/v1/users/username/${username}`);
};

export const checkUserByEmail = async (email: string) => {
  return await ApiClient.get(`/api/portal/v1/users/email/${email}`);
};

export const getUserById = async (userId: number) => {
  return await ApiClient.get(`/api/portal/v1/users/${userId}`);
};
export const addUser = async (data: UserData) => {
  return await ApiClient.post(`/api/portal/v1/users`, data);
};

export const getUsers = async () => {
  return await ApiClient.get(`/api/portal/v1/users`);
};

export const editUser = async (id: number, data: UserData) => {
  return await ApiClient.patch(`/api/portal/v1/users/${id}`, data);
};

export const autogenerateUsername = async (
  firstname: string,
  lastname: string
) => {
  return await ApiClient.get(
    `/api/portal/v1/users/autogenerateUsername/${firstname}/${lastname}`
  );
};

export const deleteUser = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/users/isDelete/${id}`);
};

export const resetPassword = async (data: { email: string }) => {
  return await ApiClient.post(`/api/portal/v1/users/resetPassword`, data);
};

export const statusChange = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/users/status/${id}`);
};

export const changeTokenForSuperUser = async (id: number) => {
  const data = {
    refreshToken: `${localStorage.getItem('token')}` || '',
  };
  return await ApiClient.post(`/api/portal/v1/auth/refresh-token/${id}`, data);
};

export const getFeatures = async () => {
  return await ApiClient.get('/api/portal/v1/users/features');
};
