import { ApiClient } from '../apiClient';

export const getReportMineData = async (
  mineId: number,
  fromDate: string,
  toDate: string
) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/production/reports/mine/${mineId}/${fromDate}/${toDate}`
  );
};

export const getReportCompareData = async (
  mineId: number,
  fromDate1: string,
  toDate1: string,
  fromDate2: string,
  toDate2: string
) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/production/reports/compare/${mineId}/${fromDate1}/${toDate1}/${fromDate2}/${toDate2}`
  );
};

export const getProdLiveSectionData = async (mineId: number) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/production/live/sections/${mineId}`
  );
};
export const getLiveMineData = async (mineId: number) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/production/live/overview/${mineId}`
  );
};

export const getReportSectionShiftData = async (
  mineId: number,
  date: string
) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/production/reports/shifts/${mineId}/${date}`
  );
};
