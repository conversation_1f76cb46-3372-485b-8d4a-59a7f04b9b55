import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Company } from '../../companies/entities/company.entity';
import { UserMine } from '../../users/entities/user-mine.entity';

import { Miner } from '../../miners/entities/miner.entity';
import { Timezones } from '../../timezones/entities/timezones.entity';
@Entity({ name: 'mines' })
export class Mine {
  @PrimaryGeneratedColumn('increment')
  id: number;
  @Column()
  name: string;
  @Column()
  location: string;
  @Column({ unique: true })
  code: string;
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'is_delete', default: false, nullable: true })
  isDelete: boolean;

  @Column({ name: 'company_id' })
  companyId: number;

  @ManyToOne(() => Company, (company) => company.mines)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @OneToMany(() => UserMine, (userMine) => userMine.mine)
  userMines: UserMine;
  @OneToMany(() => Miner, (miner) => miner.mine)
  miner: Miner[];

  @Column({ name: 'timezone_id', nullable: false })
  timezoneId: number;

  @ManyToOne(() => Timezones, (timezone) => timezone.mines, { nullable: false })
  @JoinColumn({ name: 'timezone_id' })
  timezone: Timezones;
}
