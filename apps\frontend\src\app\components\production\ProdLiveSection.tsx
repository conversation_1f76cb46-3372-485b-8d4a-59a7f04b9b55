import { useEffect, useState } from 'react';
import { ProdActiveTabIcon } from '../../../assets/icons/icons';
import Table, { ColumnDef } from '../common/Table';
import { useQuery } from '@tanstack/react-query';
import { getProdLiveSectionData } from '../../../../src/api/production/productionReportapi';
import decodeJWT from '../../../../src/utils/jwtdecoder';
import { Link, useOutletContext, useParams } from 'react-router-dom';
import ProdLiveSectionGraph from '../common/ProdLiveSectionGraph';
import dayjs from 'dayjs';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';

export default function ProdLiveSection() {
  const [selectedSection, setSelectedSection] = useState('');
  const [selectedSectionData, setSelectedSectionData] = useState('');
  const params = useParams();
  const { data: liveSectionsData, status: apiStatus } = useQuery({
    queryKey: ['prodution_live_section'],
    queryFn: () => getProdLiveSectionData(Number(decodeJWT()?.mineid)),
    refetchOnWindowFocus: false,
  });
  const [
    searchText,
    setLastUpdatedDate,
    filterData,
    setlastEformSubmitted,
    setTimzezone,
  ] = useOutletContext() as [string, any, any, any, any];

  const url = getPageNamesFromUrl(params['*']);
  const decode = decodeJWT();

  const sectionId = liveSectionsData?.data?.sections?.find(
    (ele: any) => ele?.sectionId == selectedSection
  )?.sectionId;

  const sectionData = liveSectionsData?.data?.sectionsData?.find(
    (ele: any) => ele?.sectionId == sectionId
  );

  const data = [
    {
      heading: 'Mined',
      value: sectionData?.mined ? sectionData?.mined : '0',
      unit: 'ft',
    },
    {
      heading: 'Feet Mined/Hour',
      value: sectionData?.feetMinedPerHour
        ? sectionData?.feetMinedPerHour
        : '0',
      unit: 'fmph',
    },
    // {
    //   heading: 'Belt Availability',
    //   value: 78,
    //   unit: '%',
    // },
    {
      heading: 'Total Downtime',
      value: sectionData?.totalDowntime ? sectionData?.totalDowntime : '0',
      unit: 'mins',
    },
    {
      heading: 'Last Downtime Started',
      value: sectionData?.lastDowntimeStarted
        ? sectionData?.lastDowntimeStarted
        : '-',
      unit:
        sectionData?.lastDowntimeStartedPostfix === null
          ? ''
          : sectionData?.lastDowntimeStartedPostfix,
    },
  ];

  const columns: ColumnDef[] = [
    {
      key: 'type',
      label: 'Type',
      type: 'text',
    },
    {
      key: 'timeLogged',
      label: 'Time Logged',
      type: 'text',
    },
    {
      key: 'description',
      label: 'Description',
      type: 'text',
    },
    {
      key: 'productionLost',
      label: 'Production Lost',
      type: 'text',
    },
    {
      key: '',
      label: '',
      type: 'element',
      render: (row: any) => (
        <>
          <button className="bg-transparent  text-white-300 font-semibold  py-2 px-4 border-[1px] border-[#4AA8FE]  rounded-lg mr-10">
            Clear
          </button>
        </>
      ),
    },
  ];

  const sections = liveSectionsData?.data?.sections;

  const sectionIds = liveSectionsData?.data?.sections?.map(
    (ele: any) => ele?.sectionId
  );
  const assetsData = ['CM12', 'SC2', 'SC3', 'RB4'];

  const alertsData = [
    {
      type: 'Planned Downtime',
      timeLogged: '1:07pm',
      description: 'CM12 down',
      productionLost: '-42 feet',
    },
    {
      type: 'Planned Downtime',
      timeLogged: '1:07pm',
      description: 'CM12 down',
      productionLost: '-42 feet',
    },
    {
      type: 'Planned Downtime',
      timeLogged: '1:07pm',
      description: 'CM12 down',
      productionLost: '-42 feet',
    },
    {
      type: 'Planned Downtime',
      timeLogged: '1:07pm',
      description: 'CM12 down',
      productionLost: '-42 feet',
    },
  ];

  const sectionDate = String(dayjs(new Date()).format('YYYY-MM-DD'));
  useEffect(() => {
    setLastUpdatedDate(liveSectionsData?.data?.lastUpdatedTs);
    setlastEformSubmitted(liveSectionsData?.data?.lastSavedOrSubmittedTs);
    setTimzezone(liveSectionsData?.data?.tzAbbreviation);
  }, [
    liveSectionsData?.data?.lastUpdatedTs,
    liveSectionsData?.data?.lastSavedOrSubmittedTs,
    liveSectionsData?.data?.tzAbbreviation,
  ]);

  const savedSectionId = sessionStorage.getItem('prodLiveSection');

  useEffect(() => {
    if (savedSectionId) {
      const isSectionExists = sections?.find(
        (ele: any) => ele?.sectionId == savedSectionId
      );
      if (isSectionExists) {
        setSelectedSection(isSectionExists?.sectionId);
        setSelectedSectionData(isSectionExists?.sectionName);
      }
    } else {
      setSelectedSection(liveSectionsData?.data?.sections[0]?.sectionId);
      setSelectedSectionData(liveSectionsData?.data?.sections[0]?.sectionName);
    }
  }, [liveSectionsData?.data?.sections[0]?.sectionId]);

  return (
    <div className="">
      <div className="bg-[#2c5c8bf6]">
        <div className="py-2 w-[93%] mx-auto">
          <div className="flex justify-between  border-[#4AA8FE]  border-b-[1px]">
            <div className="flex flex-row mt-3 w-[80%] overflow-x-scroll">
              {sections?.map((ele: any, index) => (
                <div key={index}>
                  {ele?.sectionId == selectedSection ? (
                    <div className="text-[#FFD084] mx-3 text-[14px] cursor-pointer border-b-[6px]  border-[#FFB132]">
                      {ele?.sectionName}{' '}
                      <div className="relative top-1 w-[30px]"></div>
                    </div>
                  ) : (
                    <div
                      className="text-white mx-3 text-[14px] cursor-pointer"
                      onClick={() => {
                        setSelectedSection(ele?.sectionId);
                        sessionStorage.setItem(
                          'prodLiveSection',
                          ele?.sectionId
                        );
                        setSelectedSectionData(ele?.sectionName);
                        mixpanel.track('Section Click', {
                          Page_Name: url,
                          Section_Name: ele?.sectionName,
                          MshaId: decode?.mshaId,
                        });
                      }}
                    >
                      {ele?.sectionName}
                    </div>
                  )}
                </div>
              ))}
            </div>
            <div
              className="mt-2 mb-3"
              title="Click to view section historical report"
            >
              <Link
                to={
                  params?.mineId
                    ? `/app/Mines/${params?.mineId}/Production/report/sections/${selectedSection}`
                    : `/app/Production/report/sections/${selectedSection}`
                }
                className={`text-white  bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm 2xl:px-10 xl:px-4 py-2 text-center  items-center  mb-2`}
                onClick={() => mixpanel.track('Historical Report Click', {
                  Section_Name: selectedSectionData,
                  Page_Name: url,
                })}
              >
                Section Historical Report
              </Link>
            </div>
          </div>
        </div>
        <div className="grid  grid-cols-4 gap-4 pt-6 w-[93%] mx-auto  pb-6 ">
          {data?.map((ele: any, index) => (
            <div key={index}
              className={`h-[100px] border-[#4AA8FE]   rounded-lg ${
                ele?.heading == 'Belt Availability'
                  ? 'border-dashed  border-[1px]'
                  : 'border-[1px]'
              } `}
            >
              <div
                className={`${
                  ele?.heading == 'Belt Availability' ? 'blur-sm' : ''
                }`}
              >
                <div
                  className={`text-center  text-[14px] text-[#FFD084] font-normal pt-3`}
                >
                  {ele?.heading}
                </div>
                <div className=" text-center text-[32px] text-white font-bold pt-3">
                  {ele?.value}
                  <span className="text-white text-[12px] mx-1">
                    {ele?.unit}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="flex w-[93%] mx-auto">
        <div className="w-[60%]">
          <ProdLiveSectionGraph
            data={sectionData?.cutsGraphData}
            shifts={liveSectionsData?.data?.shifts}
            apiStatus={apiStatus}
          />
        </div>
        <div className="grid grid-cols-2 w-[40%] relative z-[999] ">
          <div className="">
            <div
              className={`xl:h-[146px] 2xl:h-[156px] border-[#4AA8FE]   rounded-lg border-[1px] my-6 cursor-pointer`}
            >
              <Link
                to={
                  selectedSection
                    ? params?.mineId
                      ? `/app/Mines/${params?.mineId}/Location/report/sections/${sectionDate}/${selectedSectionData}`
                      : `/app/Location/report/sections/${sectionDate}/${selectedSectionData}`
                    : '#'
                }
                onClick={() => {
                  if(selectedSectionData !== ''){
                    mixpanel.track('On Section Click', {
                    Page_Name : getPageNamesFromUrl(params['*']??''),
                    Section_Name: selectedSectionData,
                  });
                }
                }}
              >
                <div
                  className={`text-center   text-[14px] text-[#FFD084]  pt-6`}
                >
                  On-Section
                </div>
                <div className=" text-center leading-normal text-[56px] text-white font-bold ">
                  {sectionData?.onSection ? sectionData?.onSection : 0}
                </div>
              </Link>
            </div>
            <div
              className={`xl:h-[146px] 2xl:h-[156px] border-[#4AA8FE]   rounded-lg border-[1px] my-2`}
            >
              <div>
                <div
                  className={`text-center  text-[14px] text-[#FFD084]  pt-6`}
                >
                  Cuts
                </div>
                <div className=" text-center leading-normal text-[56px] text-white font-bold ">
                  {sectionData?.cuts ? sectionData?.cuts : 0}
                </div>
              </div>
            </div>
          </div>
          <div
            className={`border-[#4AA8FE]   rounded-lg border-dashed  border-[1px] mt-6 ml-6 mb-7 opacity-40`}
          >
            <div className="opacity-40 mb-4">
              {assetsData?.map((ele: any, index) => (
                <div key={index}
                  className="flex px-[4%] my-2">
                  <div>
                    <div className="ml-2 mt-5 border-white text-white text-[10px] text-center   border-dashed p-1 border-[1px] w-min">
                      Icon
                    </div>
                  </div>
                  <div className="text-white text-[42px] font-bold mx-2">
                    {ele}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="w-[93%] mx-auto">
        <div
          className={`mb-4 bg-[#25354354] border-[#4AA8FE]    border-dashed  border-[1px] rounded-sm opacity-40`}
        >
          <div className="flex mb-2 justify-between mx-4 opacity-40">
            <h2 className="text-[28px] text-white font-bold mt-3">{`Alerts(${alertsData?.length})`}</h2>
            <p className="text-white text-[16px] mt-4 underline">
              View all alerts
            </p>
          </div>
          <div className="w-[97%] m-auto h-full pt-0 opacity-40">
            <Table
              columns={columns}
              data={alertsData ?? []}
              searchText={''}
              searchOnColumn="minerName"
              backgroundColor={false}
              scrollable={true}
              sortable={true}
              dataRenderLimitMdScreen={4}
              dataRenderLimitLgScreen={5}
              tableHeightClassLg={`h-[240px]`}
              tableHeightClassMd={'h-[240px]'}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
