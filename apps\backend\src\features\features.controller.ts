import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { FeaturesService } from './features.service';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { RolesGuard } from '../auth/gurads/roles.guard';
import { Roles } from '../auth/gurads/roles.decorator';
import { ApiTags } from '@nestjs/swagger';
import { CreateFeatureDto } from './dto/create-feature.dto';
import { UpdateFeatureDto } from './dto/update-feature.dto';

@Controller('portal/v1/features')
@ApiTags('features')
export class FeaturesController {
  constructor(private readonly featureService: FeaturesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() createFeatureDto: CreateFeatureDto) {
    return this.featureService.create(createFeatureDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll() {
    return this.featureService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: number) {
    return this.featureService.findOne(+id);
  }

  @Patch('id')
  @UseGuards(JwtAuthGuard)
  async update(
    @Param('id') id: number,
    @Body() updateFeatureDto: UpdateFeatureDto
  ) {
    return this.featureService.update(+id, updateFeatureDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  async remove(@Param('id') id: number) {
    return this.featureService.delete(+id);
  }
}
