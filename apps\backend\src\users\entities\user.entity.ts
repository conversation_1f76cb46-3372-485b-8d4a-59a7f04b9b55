import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  BeforeInsert,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  OneToOne,
  Relation,
  OneToMany,
} from 'typeorm';
import * as bcrypt from 'bcrypt';
import { UserRole } from './user-role.entity';
import { UserMine } from './user-mine.entity';
import { Company } from '../../companies/entities/company.entity';
import { WatchlistItem } from '../../watchlists/entities/watchlist.entity';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ unique: true, nullable: false })
  username: string;

  @Column({ name: 'company_id', type: 'int', nullable: false })
  companyId: number;

  @OneToOne(() => Company, (company) => company.users, { nullable: false })
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @Column({ nullable: false })
  password: string;

  @BeforeInsert()
  async setPassword() {
    const salt = await bcrypt.genSalt();
    this.password = await bcrypt.hash(this.password, salt);
  }

  @Column({ name: 'first_name', nullable: false })
  firstName: string;

  @Column({ name: 'last_name', nullable: false })
  lastName: string;

  @Column({ name: 'phone', nullable: true })
  contactNumber: string;

  @Column({ name: 'job_title', nullable: true })
  jobTitle: string;

  @Column({ name: 'email', unique: true, nullable: false })
  email: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'is_delete', default: false, nullable: true })
  isDelete: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @OneToMany(() => UserRole, (userRole) => userRole.user)
  userRoles: UserRole;

  @OneToMany(() => UserMine, (userMine) => userMine.user)
  userMines: UserMine;

  @OneToOne(() => WatchlistItem, (watchlistItem) => watchlistItem.user)
  watchlistItem: WatchlistItem;
}
