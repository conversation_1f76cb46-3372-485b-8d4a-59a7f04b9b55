import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ProductionGoalsService } from './production_goals.service';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { ApiTags } from '@nestjs/swagger';
import { CreateProductionGoalDto } from './dto/create-production_goal.dto';
import { UpdateProductionGoalDto } from './dto/update-production_goal.dto';

@Controller('portal/v1/goals')
@ApiTags('goals')
export class ProductionGoalsController {
  constructor(
    private readonly productionGoalsService: ProductionGoalsService
  ) {}

  @Get('section/:sectionId/shift/:shiftId')
  @UseGuards(JwtAuthGuard)
  async findActiveGoal(
    @Param('sectionId') sectionId: number,
    @Param('shiftId') shiftId: number
  ) {
    return this.productionGoalsService.findActiveGoal(sectionId, shiftId);
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(
    @Body() createProductionGoalDto: CreateProductionGoalDto,
    @Req() req
  ) {
    return this.productionGoalsService.create(
      createProductionGoalDto,
      req.user
    );
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Req() req) {
    return this.productionGoalsService.findAll(req.user.mineid);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: number) {
    return this.productionGoalsService.findOne(+id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  async update(
    @Param('id') id: number,
    @Body() updateProductionGoalDto: UpdateProductionGoalDto,
    @Req() req
  ) {
    return this.productionGoalsService.update(
      +id,
      updateProductionGoalDto,
      req.user
    );
  }

  @Patch('/isDelete/:id')
  @UseGuards(JwtAuthGuard)
  async remove(@Param('id') id: number, @Req() req) {
    return this.productionGoalsService.delete(+id, req.user);
  }
}
