import {
  IsDefined,
  <PERSON>N<PERSON>Empty,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateShiftDto {
  @IsString({ message: 'Shift Name must be a string' })
  @Length(1, 100, {
    message: 'Shift Name length must be between 1 and 100 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  shiftName: string;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  mineId: number;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  extShiftId: number;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  startTime: string;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  endTime: string;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  shift_type: string;

  isActive?: boolean;
}
