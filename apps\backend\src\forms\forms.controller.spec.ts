import { Test, TestingModule } from '@nestjs/testing';
import { FormsController } from './forms.controller';
import { FormsService } from './forms.service';
import { CreateFormDto } from './dto/create-forms.dto';
import { Form } from './entities/forms.entity';
import { UpdateFormDto } from './dto/update-forms.dto';

describe('FormsController', () => {
  let controller: FormsController;

  const mockFormService = {
    create: jest.fn(),
    findAll: jest.fn(),
    getFormsByMineId: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FormsController],
      providers: [
        {
          provide: FormsService,
          useValue: mockFormService,
      }]
    }).compile();

    controller = module.get<FormsController>(FormsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('create => should create a new form template definition by the given data', async () => {
    // arrange
    let now = new Date().toISOString();
    const req = { user: { userId: 1, mineId: 1 } };
    const createFormDto = {
      mineId: req.user.mineId,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      createdBy: req.user.userId,
      name: 'Test Form',
      content: '{}',
    } as CreateFormDto;

    const form = {
      id: 1,
      mineId: req.user.mineId,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      createdBy: req.user.userId,
      name: 'Test Form',
      content: '{}',
    } as Form;

    jest.spyOn(mockFormService, 'create').mockReturnValue(form);

    // act
    const result = await controller.create(createFormDto, req);

    // assert
    expect(mockFormService.create).toHaveBeenCalled();
    expect(mockFormService.create).toHaveBeenCalledWith(createFormDto);
    expect(result).toEqual(form);
  });

  it('findAll => should return an array of form template definitions', async () => {
    // arrange
    const form = {
      mineId: 1,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      createdBy: 1,
      name: 'Test Form',
      content: '{}',
    };
    let mineId = 1;
    const req = { user: { mineId: mineId } };
    const forms = [form];
    jest.spyOn(mockFormService, 'findAll').mockReturnValue(forms);

    // act
    const result = await controller.findAll(req);

    // assert
    expect(mockFormService.findAll).toHaveBeenCalled();
    expect(result).toEqual(forms);
  });

  it('findOne => should find a form template definition by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const form = {
      mineId: 1,
      formTemplateId: 1,
      formTemplateDefinitionId: 1,
      createdBy: 1,
      name: 'Test Form',
      content: '{}',
    };

    jest.spyOn(mockFormService, 'findOne').mockReturnValue(form);

    // act
    const result = await controller.findOne(id);

    // assert
    expect(mockFormService.findOne).toHaveBeenCalled();
    expect(mockFormService.findOne).toHaveBeenCalledWith(+id);
    expect(result).toEqual(form);
  });

  it('update => should find a form template definition by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateFormDto = {
      content: '{}',
      updatedBy: 1,
    } as UpdateFormDto;
    const form = {
      mineId: 1,
      formTemplateId: 1,
      content: '{}',
      updatedBy: 1,
    };

    jest.spyOn(mockFormService, 'update').mockReturnValue(form);

    // act
    const result = await controller.update(id, updateFormDto);

    // assert
    expect(mockFormService.update).toHaveBeenCalled();
    expect(mockFormService.update).toHaveBeenCalledWith(+id, updateFormDto);
    expect(result).toEqual(form);
  });

  it('remove => should find a form template definition by a given id, remove and then return number of affected rows', async () => {
    // arrange
    const id = 1;
    const form = {
      id: 1,
    };

    jest.spyOn(mockFormService, 'remove').mockReturnValue(form);

    // act
    const result = await controller.remove(id);

    // assert
    expect(mockFormService.remove).toHaveBeenCalled();
    expect(mockFormService.remove).toHaveBeenCalledWith(+id);
    expect(result).toEqual(form);
  });
});
