import Loader from './Loader';

function GraphSkeleton(props: any) {
  return (
    <div
      className={`flex items-center justify-center  rounded-md bg-[#215C98] ${props?.className}  `}
    >
      {props.apiStatus != 'success' ? (
        <Loader />
      ) : (
        <p className={` text-center font-[600] text-white text-[24px]`}>
          {props?.textMessage ? props?.textMessage : 'No data available'}
        </p>
      )}
    </div>
  );
}

export default GraphSkeleton;
