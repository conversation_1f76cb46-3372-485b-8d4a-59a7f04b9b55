import { ApiClient } from '../apiClient';

export interface CompanyData {
  id?: number;
  companyName: string;
  companyCode: string;
  mineName: string;
  mineCode: string;
  location: string;
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  createdBy: number;
}

export interface MineData {
  id?: number;
  name?: string;
  location?: string;
  code?: string;
  isActive?: boolean;
}

export const addCompany = async (data: CompanyData) =>
  await ApiClient.post('api/portal/v1/company-onboard', data);

export const getCompanies = async () =>
  await ApiClient.get('api/portal/v1/companies');

export const checkCompanyName = async (data: any) =>
  await ApiClient.post('api/portal/v1/companies/checkcompanyname', data);

export const checkMineName = async (data: MineData) => {
  return await ApiClient.post(`/api/portal/v1/mines/checkminename`, data);
};

export const addMine = async (data: MineData) => {
  return await ApiClient.post(`/api/portal/v1/mines`, data);
};

export const getMines = async () => {
  return await ApiClient.get(`/api/portal/v1/mines`);
};

export const editMine = async (id: number, data: MineData) => {
  return await ApiClient.patch(`/api/portal/v1/mines/${id}`, data);
};

export const deleteMine = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/mines/isDelete/${id}`);
};

export const statusChange = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/mines/${id}/status-update`);
};

export const getTimezoneData = async () =>
  await ApiClient.get('api/portal/v1/timezones');

export const getTimezoneBySearch = async (partialtext: string) =>
  await ApiClient.get(
    `/api/portal/v1/timezones/searchTimezone?partialName=${partialtext}`
  );
