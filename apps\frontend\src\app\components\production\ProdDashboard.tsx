import { getUserById } from '../../../api/users/userapis';
import decodeJWT from '../../../utils/jwtdecoder';
import { useQuery } from '@tanstack/react-query';

const ProdDashboard = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['user_by_id'],
    queryFn: () => getUserById(Number(decodeJWT()?.userId)),
    refetchOnWindowFocus: false,
  });
  if (decodeJWT()?.role == 'admin' || decodeJWT()?.role == 'manager') {
    return (
      <div className="sticky w-full top-0 z-30 px-6 box  bg-top">
        <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe]  pb-4 pt-4">
          <div className="">
            <h6 className="p-2 font-bold text-white text-[32px] text-left">
              Dashboard
            </h6>
          </div>
          <div className="">
            <h6 className="p-2 font-bold text-white text-[32px] text-right">
              Production
            </h6>
          </div>
        </div>
      </div>
    );
  } else {
    return '';
  }
};

export default ProdDashboard;
