import { useParams } from 'react-router-dom';
import decodeJWT from '../../../utils/jwtdecoder';
import SubMenuInnerNavBar from '../common/SubMenuInnerNavBar';
import { useState } from 'react';
import { getFeatures } from '../../../api/users/userapis';
import { useQuery } from '@tanstack/react-query';
const LocLive = () => {
  const [arrive, setArrive] = useState('');
  const params = useParams();
  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  if (
    features?.data.some((feature: any) => feature.FeatureName == 'Location') ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <SubMenuInnerNavBar
        leftTitle="Live"
        rightTitle="Locations"
        rightSubTitle1="Last Updated"
        tabNames={['checkins', 'sections', 'watchlist']}
        showSearchBox={true}
        pathToRender={
          decodeJWT()?.role == 'superuser'
            ? `Mines/${params?.mineId}/Location/live`
            : 'Location/live'
        }
        defaultTab="checkins"
        setArrive={setArrive}
        arrive={arrive}
      />
    );
  } else {
    return '';
  }
};

export default LocLive;
