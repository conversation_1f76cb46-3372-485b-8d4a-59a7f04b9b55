import Table from '../common/Table';
import Loader from '../common/Loader';
import { useEffect, useRef, useState } from 'react';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import decodeJWT from '../../../utils/jwtdecoder';
import { useParams } from 'react-router-dom';

interface Props {
  selectedMenu: string;
  dummySections: any;
  dummyData: any;
  columns: any;
}
const ChildScreenForLiveAtomsphere = (props: Props) => {
  let decoded = decodeJWT();
  const params = useParams();
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const [selectedSection, setSelectedSection] = useState('');

  useEffect(() => {
    setSelectedSection('');
  }, [props?.selectedMenu]);

  return (
    <div>
      <div className="sticky">
        <div
          className={`${
            props?.dummySections?.length > 0
              ? `agBreakup  grid xl:grid-cols-6 gap-4   px-[4%] w-full m-auto sm:grid-cols-3 p-6 sticky top-[128px] z-30`
              : ' bg-[#2c5c8bf6] px-[4%] w-full m-auto sm:grid-cols-3 p-6 sticky top-[128px] z-30'
          }`}
        >
          {props?.dummySections?.length > 0 ? (
            props?.dummySections.map((data: any, index: any) => (
              <div
                key={index}
                className={`h-[100px] cursor-pointer border border-[#4AA8FE] rounded-md flex justify-center items-center
         ${
           selectedSection === data.name
             ? 'bg-[#21303C] shadow-section'
             : 'bg-transparent'
         }
        `}
                onClick={() => {
                  mixpanel.track('On Section Click', {
                    Section_Name: data.name,
                    MineName: decoded?.minename,
                    Page_Name: getPageNamesFromUrl(
                      params['*'] ?? 'Atmosphere Live page'
                    ),
                  });

                  setSelectedSection((prev) =>
                    prev === data.name ? null : data.name
                  );
                }}
              >
                <div className="flex flex-col">
                  <span className="text-[#FFD084] text-center text-base py-0">
                    {data.name}
                  </span>
                  <span className="text-[#FFFFFF] text-center text- font-bold text-5xl py-0">
                    {data?.count}
                  </span>
                </div>
              </div>
            ))
          ) : false ? (
            ''
          ) : (
            <div
              className="text-white font-semibold text-2xl text-center  "
              dangerouslySetInnerHTML={{
                __html: `${'Live data for section is currently unavailable'}`,
              }}
            />
          )}
        </div>
      </div>
      {/* Table */}
      <div
        ref={tableContainerRef}
        className="  max-w-screen w-full overflow-y flex flex-col min-h-96"
      >
        <div className="text-white max-w-screen pt-6 px-[4%] w-full m-auto gap-1 ">
          {props?.dummyData?.sectionBreakup?.length > 0 ? (
            props?.dummyData?.sectionBreakup?.map(
              (section: any, index: any) => {
                return (
                  <div
                    key={index}
                    ref={
                      section.sectionName === selectedSection
                        ? sectionRef
                        : null
                    }
                    id={section.sectionName}
                    className={`mb-4 bg-[#25354354] 
                ${
                  selectedSection === section.sectionName || !selectedSection
                    ? ''
                    : 'hidden '
                }
                 rounded-sm ${section.sectionName}`}
                  >
                    <div className="flex mb-2 ">
                      <h2 className=" flex text-xl font-bold mt-3 2xl:ml-6 xl:ml-4 w-1/2">
                        {section.sectionName}
                      </h2>
                    </div>
                    <div className="w-[97%] m-auto h-full pt-0">
                      <Table
                        columns={props?.columns}
                        data={section?.totalNodesBreakup ?? []}
                        searchText={''}
                        searchOnColumn=""
                        backgroundColor={false}
                        scrollable={true}
                        sortable={true}
                        dataRenderLimitMdScreen={4}
                        dataRenderLimitLgScreen={5}
                        tableHeightClassLg={`${
                          selectedSection ? 'h-[240px]' : 'h-[240px]'
                        }`}
                        tableHeightClassMd={`${
                          selectedSection ? 'h-full' : 'h-[240px]'
                        }`}
                      />
                    </div>
                  </div>
                );
              }
            )
          ) : false ? (
            <div>
              <div className="flex justify-center items-center h-full">
                {<Loader />}
              </div>
              <div className="flex justify-center items-center h-full text-xl pt-2">
                Loading....
              </div>
            </div>
          ) : (
            ''
          )}
        </div>
      </div>{' '}
    </div>
  );
};

export default ChildScreenForLiveAtomsphere;
