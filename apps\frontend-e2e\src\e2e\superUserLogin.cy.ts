import { getGreeting } from '../support/app.po';

describe('frontend-e2e', () => {
  beforeEach(() => {
    cy.on('uncaught:exception', (e) => {
      if (e.message.includes('EOF is not defined')) {
        return false;
      }
    });

    cy.visit('http://localhost:4200/app');
    cy.wait(1000);
  });

  // it('should visit given url', () => {
  //   cy.visit('http://localhost:4200/app');
  //   cy.wait(1000);
  // });

  it('should successfully login with valid credentials', () => {
    cy.fixture('loginData').then((loginData) => {
      const data = loginData.users[0];
      cy.get('#username').type(data.username);
      cy.wait(1000);
      cy.get('#password').type(data.password);
      cy.wait(1000);
      // cy.contains('Sign In').click();
      cy.get('#signin').click();
      cy.wait(1000);
      cy.url().should('include', '/Dashboard');
      cy.wait(1000);
    });
  });

  it('should display error message for empty fileds', () => {
    cy.get('#signin').should('be.visible').click();
    cy.wait(1000);
    cy.get('#usernameError').should('contain.text', 'Please enter username');
    cy.wait(1000);
    cy.get('#passwordError').should('contain.text', 'Please enter password');
    cy.wait(1000);
  });

  it('should display error message for invalid username', () => {
    cy.fixture('logindata').then((loginData) => {
      const data = loginData.users[1];

      cy.get('#username').should('be.visible').type(data.invaliUser);
      cy.wait(1000);
      cy.get('#password').should('be.visible').type(data.validPass);
      cy.wait(1000);

      cy.get('#signin').click();
      cy.wait(1000);
    });
  });

  it('should display error message for invalid password', () => {
    cy.fixture('loginData').then((loginData) => {
      const data = loginData.users[2];

      cy.get('#username').should('be.visible').type(data.validUserName);
      cy.wait(1000);
      cy.get('#password').should('be.visible').type(data.Invalidpass);
      cy.wait(1000);

      cy.get('#signin').click();
      cy.wait(1000);
      cy.get('#passwordError')
        .should('be.visible')
        .should('contain.text', 'Password failed. Please try again.');
    });
  });

  it('should redirect to forgot password page when clicked', () => {
    cy.fixture('loginData').then((loginData) => {
      const data = loginData.users[4];
      cy.get('#forgotPassword').click();
      cy.wait(1000);
      cy.url().should('include', '/app/forgotpassword');
      cy.wait(1000);
      cy.get('#emailToReset').type(data.validEmail);
      cy.wait(1000);
      cy.get('body').click();
      cy.wait(1000);
      cy.get('#emailToReset').should('have.value', data.validEmail);
      cy.wait(1000);
      // cy.get('#resetPass').click();
    });
  });

  it('should display error for unregistered email', () => {
    cy.fixture('loginData').then((loginData) => {
      const data = loginData.users[5];
      cy.get('#forgotPassword').click();
      cy.wait(1000);
      cy.url().should('include', '/app/forgotpassword');
      cy.wait(1000);
      cy.get('#emailToReset').type(data.invalidEmail);
      cy.wait(1000);
      cy.get('body').click();
      cy.wait(1000);
      cy.get('#emailToReset').should('have.value', data.invalidEmail);
      cy.wait(1000);
      cy.get('#resetPass').click();
      cy.wait(1000);
    });
  });

  it('should redirect to sign in page when clicking back to sign in link', () => {
    cy.get('#forgotPassword').click();
    cy.wait(1000);
    cy.url().should('include', '/app/forgotpassword');
    cy.wait(1000);
    cy.get('#backToSignInPage').click();
    cy.wait(1000);
    cy.url().should('include', '/app');
    cy.wait(1000);
  });

  // it('should not redirect on hover', () => {
  //   cy.get('#forgotPassword').trigger('mouseover');

  //   cy.url().should('not.include', '/app/forgotpassword');
  // });

  // it('should not redirect when not clicked', () => {
  //   cy.url().then((initialUrl) => {
  //     cy.wait(1000);

  //     cy.url().should('eq', initialUrl);
  //   });
  // });
});
