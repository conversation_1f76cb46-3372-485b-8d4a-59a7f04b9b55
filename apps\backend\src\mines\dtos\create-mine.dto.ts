import { IsString, Is<PERSON><PERSON>ber, IsBoolean } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
export class CreateMineDto {
  @IsString()
  @ApiProperty()
  name: string;
  @IsString()
  @ApiProperty()
  location: string;
  @IsString()
  @ApiProperty()
  code: string;
  @IsNumber()
  @ApiProperty()
  companyId: number;
  @IsBoolean()
  @ApiProperty()
  isActive: boolean;
  @ApiProperty()
  timezoneId: number;
}
