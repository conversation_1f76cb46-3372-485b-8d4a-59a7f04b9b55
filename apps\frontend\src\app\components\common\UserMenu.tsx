import { Dropdown, Tooltip } from 'flowbite-react';
import mixpanel from 'mixpanel-browser';
import { Logo, Logout, MineIcon, UserIcon } from '../../../assets/icons/icons';
import decodeJWT from '../../../utils/jwtdecoder';
import { useParams } from 'react-router-dom';

export function UserMenu(props: any) {
  const decoded = decodeJWT();
  const params = useParams();
  return (
    <div
      className={`my-1 cursor-pointer ${
        params['*']?.includes('dashboard') ||
        params['*']?.includes('Setting') ||
        params['*'] == 'Mines' ||
        params['*'] == 'AuditLogs'
          ? params['*']?.includes('Setting')
            ? 'pr-[2.4rem] 2xl:pr-[2.4rem]'
            : ' pr-[2.3rem] 2xl:pr-[2.3rem]'
          : params['*']?.includes('Compliance')
          ? 'pr-[1.3rem] 2xl:pr-[1.3rem]'
          : ''
      } `}
    >
      <Dropdown
        label=""
        dismissOnClick={false}
        className="z-50 bg-[#1A252F] text-white border-[#1A252F] "
        renderTrigger={() => (
          <div className="flex">
            <div className="flex items-center justify-center w-[26px] h-[26px] my-auto  bg-[#FFB132] text-black rounded-full font-bold text-[12px]">
              {`${decoded?.firstname
                ?.charAt(0)
                .toUpperCase()}${decoded?.lastname?.charAt(0).toUpperCase()}`}
            </div>
          </div>
        )}
      >
        <Dropdown.Item
          className="text-white  hover:bg-[#4AA8FE] cursor-text"
          icon={UserIcon}
        >
          {decoded?.username}
        </Dropdown.Item>
        <Dropdown.Divider className="bg-white" />
        {decoded?.role !== 'superuser' && (
          <>
            <Dropdown.Item
              className="text-white  hover:bg-[#4AA8FE] cursor-text"
              icon={MineIcon}
            >
              {decoded?.minename}
            </Dropdown.Item>
            <Dropdown.Divider className="bg-white" />
          </>
        )}
        <Dropdown.Item
          className="text-white hover:bg-[#4AA8FE]"
          icon={Logout}
          onClick={() => {
            localStorage.removeItem('token');
            window.location.replace(window.location.origin);
            sessionStorage.clear();
            mixpanel.track('Logout', {
              UserId: decoded?.userId,
            });
            mixpanel.reset();
          }}
        >
          {'Logout'}
        </Dropdown.Item>
      </Dropdown>
    </div>
  );
}
