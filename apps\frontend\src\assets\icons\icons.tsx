export const EyeOffIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
      />
    </svg>
  );
};

export const SideBarPointLine = (props: { className?: string }) => {
  return (
    <svg
      width="14"
      height="28"
      viewBox="0 0 14 28"
      className={props?.className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.25 1C1.25 0.861929 1.13807 0.75 1 0.75C0.861929 0.75 0.75 0.861929 0.75 1H1.25ZM13.1768 26.1768C13.2744 26.0791 13.2744 25.9209 13.1768 25.8232L11.5858 24.2322C11.4882 24.1346 11.3299 24.1346 11.2322 24.2322C11.1346 24.3299 11.1346 24.4882 11.2322 24.5858L12.6464 26L11.2322 27.4142C11.1346 27.5118 11.1346 27.6701 11.2322 27.7678C11.3299 27.8654 11.4882 27.8654 11.5858 27.7678L13.1768 26.1768ZM0.75 1V24H1.25V1H0.75ZM3 26.25H13V25.75H3V26.25ZM0.75 24C0.75 25.2426 1.75736 26.25 3 26.25V25.75C2.0335 25.75 1.25 24.9665 1.25 24H0.75Z"
        fill="white"
      />
    </svg>
  );
};

export const AlertTableCompliance = (props: { className?: string }) => {
  return (
    <svg
      width="17"
      height="23"
      viewBox="0 0 17 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props?.className}
    >
      <path
        d="M9.48828 21.7344C8.97266 22.3789 7.98438 22.3789 7.46875 21.7344C5.27734 18.9414 0.25 12.2812 0.25 8.5C0.25 3.94531 3.94531 0.25 8.5 0.25C13.0547 0.25 16.75 3.94531 16.75 8.5C16.75 12.2812 11.7227 18.9414 9.48828 21.7344ZM8.5 4.375C7.89844 4.375 7.46875 4.84766 7.46875 5.40625V10.2188C7.46875 10.8203 7.89844 11.25 8.5 11.25C9.05859 11.25 9.53125 10.8203 9.53125 10.2188V5.40625C9.53125 4.84766 9.05859 4.375 8.5 4.375ZM7.125 14C7.125 14.7734 7.72656 15.375 8.5 15.375C9.23047 15.375 9.875 14.7734 9.875 14C9.875 13.2695 9.23047 12.625 8.5 12.625C7.72656 12.625 7.125 13.2695 7.125 14Z"
        fill="white"
      />
    </svg>
  );
};

export const LeftArrow = (props: { className?: string }) => {
  return (
    <svg
      width="24"
      height="26"
      viewBox="0 0 24 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props?.className}
    >
      <circle cx="12.125" cy="13" r="11.375" fill="transparent" />
      <path
        d="M2.25 13C2.25 16.5156 4.07812 19.7031 7.125 21.4844C10.125 23.2188 13.8281 23.2188 16.875 21.4844C19.875 19.7031 21.75 16.5156 21.75 13C21.75 9.53125 19.875 6.34375 16.875 4.5625C13.8281 2.82812 10.125 2.82812 7.125 4.5625C4.07812 6.34375 2.25 9.53125 2.25 13ZM24 13C24 17.3125 21.7031 21.25 18 23.4062C14.25 25.5625 9.70312 25.5625 6 23.4062C2.25 21.25 0 17.3125 0 13C0 8.73438 2.25 4.79688 6 2.64062C9.70312 0.484375 14.25 0.484375 18 2.64062C21.7031 4.79688 24 8.73438 24 13ZM12.7031 7.32812V7.375C13.125 6.90625 13.8281 6.90625 14.25 7.375C14.7188 7.79688 14.7188 8.5 14.25 8.96875L10.1719 13.0469L14.25 17.125C14.7188 17.5469 14.7188 18.25 14.25 18.6719C13.8281 19.1406 13.125 19.1406 12.7031 18.6719L7.82812 13.7969C7.35938 13.375 7.35938 12.6719 7.82812 12.2031L12.7031 7.32812Z"
        fill="white"
      />
    </svg>
  );
};

export const Logo = (props: { className?: string }) => {
  return (
    <svg
      width="84"
      height="89"
      viewBox="0 0 138 144"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M68.9307 0L137.08 68.1498L68.9307 136.3L0.780914 68.1498L68.9307 0ZM47.753 67.6952C47.753 76.2331 40.8317 83.1544 32.2939 83.1544C23.756 83.1544 16.8347 76.2331 16.8347 67.6952C16.8347 59.1574 23.756 52.2361 32.2939 52.2361C40.8317 52.2361 47.753 59.1574 47.753 67.6952ZM57.1465 67.6952C57.1465 81.7494 47.5656 91.185 33.483 93.1426C50.9944 94.0683 62.6175 81.7494 62.6175 67.6952C62.6175 53.6411 51.7736 41.7024 33.483 42.2479C47.5656 44.5077 57.1465 53.6411 57.1465 67.6952ZM42.9681 104.429C60.813 101.159 75.0404 88.3112 75.0404 67.6952C75.0404 47.0792 61.7481 35.7801 42.9681 30.9613C66.6573 29.9357 82.7881 47.0792 82.7881 67.6952C82.7881 88.3112 67.047 105.212 42.9681 104.429ZM95.5551 67.6973C95.5551 93.2351 77.931 109.151 55.826 113.201C85.6534 114.17 105.152 93.2351 105.152 67.6973C105.152 42.1595 85.1707 20.9232 55.826 22.1936C79.0894 28.1629 95.5551 42.1595 95.5551 67.6973Z"
        fill="white"
      />
      <path
        d="M96.187 130.749V124.984H99.1088C99.3432 124.984 99.5984 125.052 99.8744 125.187C100.15 125.317 100.385 125.52 100.578 125.796C100.775 126.067 100.874 126.411 100.874 126.828C100.874 127.25 100.773 127.609 100.57 127.906C100.367 128.197 100.119 128.419 99.8275 128.57C99.5411 128.721 99.2702 128.796 99.015 128.796H96.9057V127.859H98.6244C98.7963 127.859 98.9812 127.773 99.1791 127.601C99.3822 127.429 99.4838 127.171 99.4838 126.828C99.4838 126.473 99.3822 126.234 99.1791 126.109C98.9812 125.984 98.8067 125.921 98.6557 125.921H97.4526V130.749H96.187ZM99.6713 128.031L101.109 130.749H99.7182L98.3119 128.031H99.6713ZM98.3744 134.187C97.5099 134.187 96.7 134.025 95.9448 133.703C95.1896 133.38 94.5255 132.932 93.9526 132.359C93.3797 131.786 92.9318 131.122 92.6089 130.367C92.286 129.611 92.1246 128.802 92.1246 127.937C92.1246 127.072 92.286 126.263 92.6089 125.507C92.9318 124.752 93.3797 124.088 93.9526 123.515C94.5255 122.942 95.1896 122.494 95.9448 122.171C96.7 121.849 97.5099 121.687 98.3744 121.687C99.239 121.687 100.049 121.849 100.804 122.171C101.559 122.494 102.223 122.942 102.796 123.515C103.369 124.088 103.817 124.752 104.14 125.507C104.463 126.263 104.624 127.072 104.624 127.937C104.624 128.802 104.463 129.611 104.14 130.367C103.817 131.122 103.369 131.786 102.796 132.359C102.223 132.932 101.559 133.38 100.804 133.703C100.049 134.025 99.239 134.187 98.3744 134.187ZM98.3744 132.687C99.2494 132.687 100.046 132.473 100.765 132.046C101.484 131.619 102.057 131.046 102.484 130.328C102.911 129.609 103.124 128.812 103.124 127.937C103.124 127.062 102.911 126.265 102.484 125.546C102.057 124.828 101.484 124.255 100.765 123.828C100.046 123.401 99.2494 123.187 98.3744 123.187C97.4994 123.187 96.7026 123.401 95.9838 123.828C95.2651 124.255 94.6922 124.828 94.2651 125.546C93.8381 126.265 93.6245 127.062 93.6245 127.937C93.6245 128.812 93.8381 129.609 94.2651 130.328C94.6922 131.046 95.2651 131.619 95.9838 132.046C96.7026 132.473 97.4994 132.687 98.3744 132.687Z"
        fill="white"
      />
    </svg>
  );
};

export const XIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6 18 18 6M6 6l12 12"
      />
    </svg>
  );
};

export const LockIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
      />
    </svg>
  );
};

export const EditIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={props.className}
    >
      <path d="M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-8.4 8.4a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32l8.4-8.4Z" />
      <path d="M5.25 5.25a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3V13.5a.75.75 0 0 0-1.5 0v5.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5V8.25a1.5 1.5 0 0 1 1.5-1.5h5.25a.75.75 0 0 0 0-1.5H5.25Z" />
    </svg>
  );
};

export const KeyIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={props.className}
    >
      <path
        fillRule="evenodd"
        d="M15.75 1.5a6.75 6.75 0 0 0-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 0 0-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75v-1.5h1.5A.75.75 0 0 0 9 19.5V18h1.5a.75.75 0 0 0 .53-.22l2.658-2.658c.19-.189.517-.288.906-.22A6.75 6.75 0 1 0 15.75 1.5Zm0 3a.75.75 0 0 0 0 1.5A2.25 2.25 0 0 1 18 8.25a.75.75 0 0 0 1.5 0 3.75 3.75 0 0 0-3.75-3.75Z"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const TrashIcon = (props: { className?: string }) => {
  return (
    <svg
      width="13"
      height="15"
      viewBox="0 0 13 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path
        d="M4.83203 0.75H8.14062C8.46875 0.75 8.76953 0.941406 8.90625 1.24219L9.125 1.625H11.75C12.2148 1.625 12.625 2.03516 12.625 2.5C12.625 2.99219 12.2148 3.375 11.75 3.375H1.25C0.757812 3.375 0.375 2.99219 0.375 2.5C0.375 2.03516 0.757812 1.625 1.25 1.625H3.875L4.06641 1.24219C4.20312 0.941406 4.50391 0.75 4.83203 0.75ZM1.25 4.25H11.75L11.1484 13.5195C11.1211 14.2305 10.5469 14.75 9.83594 14.75H3.13672C2.42578 14.75 1.85156 14.2305 1.82422 13.5195L1.25 4.25ZM4.28516 7.28516C4.01172 7.55859 4.01172 7.96875 4.28516 8.21484L5.57031 9.5L4.28516 10.7852C4.01172 11.0586 4.01172 11.4688 4.28516 11.7148C4.53125 11.9883 4.94141 11.9883 5.1875 11.7148L6.47266 10.4297L7.75781 11.7148C8.03125 11.9883 8.44141 11.9883 8.6875 11.7148C8.96094 11.4688 8.96094 11.0586 8.6875 10.7852L7.40234 9.5L8.6875 8.21484C8.96094 7.96875 8.96094 7.55859 8.6875 7.28516C8.44141 7.03906 8.03125 7.03906 7.75781 7.28516L6.47266 8.57031L5.1875 7.28516C4.94141 7.03906 4.53125 7.03906 4.28516 7.28516Z"
        fill="white"
      />
    </svg>
  );
};

export const UserIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`lucide lucide-user ${props?.className}`}
    >
      <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  );
};

export const TriangleUpIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 7 4.5"
      fill="currentColor"
      aria-hidden="true"
      className={props.className}
    >
      <path d="M 0,4.5 H 7 L 3.5,0 Z" />
    </svg>
  );
};

export const TriangleDownIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 7 4.5"
      fill="currentColor"
      aria-hidden="true"
      className={props.className}
    >
      <path d="M 0,0 H 7 L 3.5,4.5 Z" />
    </svg>
  );
};

export const Logout = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`lucide lucide-log-out ${props?.className}`}
    >
      <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
      <polyline points="16 17 21 12 16 7" />
      <line x1="21" x2="9" y1="12" y2="12" />
    </svg>
  );
};

export const CSVIcon = (props: { className?: string }) => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_1950_7248"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="25"
      >
        <rect y="0.5" width="24" height="24" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_1950_7248)">
        <path
          d="M5.05002 22.875L3.65002 21.45L6.60002 18.5H4.35002V16.5H10V22.15H8.00002V19.925L5.05002 22.875ZM12 22.5V20.5H18V9.5H13V4.5H6.00002V14.5H4.00002V4.5C4.00002 3.95 4.19586 3.47917 4.58752 3.0875C4.97919 2.69583 5.45002 2.5 6.00002 2.5H14L20 8.5V20.5C20 21.05 19.8042 21.5208 19.4125 21.9125C19.0209 22.3042 18.55 22.5 18 22.5H12Z"
          fill="#FFB132"
        />
      </g>
    </svg>
  );
};

export const EyeIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
      />
    </svg>
  );
};

// export const DashboardIcon = (props: { className?: string }) => {
//   return (
//     <svg
//       xmlns="http://www.w3.org/2000/svg"
//       fill="currentColor"
//       aria-hidden="true"
//       className={props.className}
//       viewBox="0 0 24 24"
//     >
//       <g id="ic_dashboard" transform="translate(-635 3)">
//         <g id="home" transform="translate(635 -3)">
//           <path
//             id="Path_372"
//             data-name="Path 372"
//             d="M0,0H24V24H0Z"
//             fill="none"
//           />
//         </g>
//         <path
//           id="Shape"
//           d="M13,20H1a1,1,0,0,1-1-1V6.5a2,2,0,0,1,2-2H6V2A2,2,0,0,1,8,0h4a2,2,0,0,1,2,2V9h4a2,2,0,0,1,2,2v8a1,1,0,0,1-1,1Zm5-2V11H14v7ZM8,18h4V11q0-.03,0-.059V2H8ZM2,18H6V6.5H2Z"
//           transform="translate(637 -1)"
//           fill="#7e84a3"
//         />
//       </g>
//     </svg>
//   );
// };

export const SearchIcon = (props: { className?: string; fill?: string }) => {
  return (
    <svg
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props?.className}
    >
      <path
        d="M11.7957 5.88672C11.7957 7.14453 11.3855 8.32031 10.7019 9.25L14.1472 12.7227C14.5027 13.0508 14.5027 13.625 14.1472 13.9531C13.8191 14.3086 13.2449 14.3086 12.9167 13.9531L9.44409 10.4805C8.5144 11.1914 7.33862 11.5742 6.10815 11.5742C2.96362 11.5742 0.420654 9.03125 0.420654 5.88672C0.420654 2.76953 2.96362 0.199219 6.10815 0.199219C9.22534 0.199219 11.7957 2.76953 11.7957 5.88672ZM6.10815 9.82422C7.50269 9.82422 8.78784 9.08594 9.49878 7.85547C10.2097 6.65234 10.2097 5.14844 9.49878 3.91797C8.78784 2.71484 7.50269 1.94922 6.10815 1.94922C4.68628 1.94922 3.40112 2.71484 2.69019 3.91797C1.97925 5.14844 1.97925 6.65234 2.69019 7.85547C3.40112 9.08594 4.68628 9.82422 6.10815 9.82422Z"
        fill={props?.fill ?? 'white'}
      />
    </svg>
  );
};

export const CloseIcon = (props: { className?: string; stroke?: string }) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props?.className}
    >
      <path
        d="M15.9974 29.3346C23.3612 29.3346 29.3307 23.3651 29.3307 16.0013C29.3307 8.63751 23.3612 2.66797 15.9974 2.66797C8.6336 2.66797 2.66406 8.63751 2.66406 16.0013C2.66406 23.3651 8.6336 29.3346 15.9974 29.3346Z"
        stroke={props?.stroke ?? '#D0D0D0'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20 12L12 20"
        stroke={props?.stroke ?? '#D0D0D0'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 12L20 20"
        stroke={props?.stroke ?? '#D0D0D0'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DashboardIcon = (props: { className?: string }) => {
  return (
    <svg
      // width="24px" height="20px"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={props.className}
    >
      <path
        d="M6.5 15.75C6.5 14.3693 5.38071 13.25 4 13.25C2.61929 13.25 1.5 14.3693 1.5 15.75C1.5 17.1307 2.61929 18.25 4 18.25C5.38071 18.25 6.5 17.1307 6.5 15.75Z"
        fill="currentColor"
      />
      <path
        d="M6.5 8.25C6.5 6.86929 5.38071 5.75 4 5.75C2.61929 5.75 1.5 6.86929 1.5 8.25C1.5 9.63071 2.61929 10.75 4 10.75C5.38071 10.75 6.5 9.63071 6.5 8.25Z"
        fill="currentColor"
      />
      <path
        d="M12 13.25C13.3807 13.25 14.5 14.3693 14.5 15.75C14.5 17.1307 13.3807 18.25 12 18.25C10.6193 18.25 9.5 17.1307 9.5 15.75C9.5 14.3693 10.6193 13.25 12 13.25Z"
        fill="currentColor"
      />
      <path
        d="M14.5 8.25C14.5 6.86929 13.3807 5.75 12 5.75C10.6193 5.75 9.5 6.86929 9.5 8.25C9.5 9.63071 10.6193 10.75 12 10.75C13.3807 10.75 14.5 9.63071 14.5 8.25Z"
        fill="currentColor"
      />
      <path
        d="M20 13.25C21.3807 13.25 22.5 14.3693 22.5 15.75C22.5 17.1307 21.3807 18.25 20 18.25C18.6193 18.25 17.5 17.1307 17.5 15.75C17.5 14.3693 18.6193 13.25 20 13.25Z"
        fill="currentColor"
      />
      <path
        d="M22.5 8.25C22.5 6.86929 21.3807 5.75 20 5.75C18.6193 5.75 17.5 6.86929 17.5 8.25C17.5 9.63071 18.6193 10.75 20 10.75C21.3807 10.75 22.5 9.63071 22.5 8.25Z"
        fill="currentColor"
      />
    </svg>
  );
};
export const LocationIcon = (props: { className?: string }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={props.className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.3333 6.66634C13.3333 10.6663 7.99999 14.6663 7.99999 14.6663C7.99999 14.6663 2.66666 10.6663 2.66666 6.66634C2.66666 5.25185 3.22856 3.8953 4.22875 2.8951C5.22895 1.89491 6.5855 1.33301 7.99999 1.33301C9.41448 1.33301 10.771 1.89491 11.7712 2.8951C12.7714 3.8953 13.3333 5.25185 13.3333 6.66634Z"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 8.66699C9.10457 8.66699 10 7.77156 10 6.66699C10 5.56242 9.10457 4.66699 8 4.66699C6.89543 4.66699 6 5.56242 6 6.66699C6 7.77156 6.89543 8.66699 8 8.66699Z"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ProductionIcon = (props: { className?: string }) => {
  return (
    <svg
      fill="currentColor"
      aria-hidden="true"
      className={props.className}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M2.5 2.75a.75.75 0 00-1.5 0v18.5c0 .414.336.75.75.75H20a.75.75 0 000-1.5H2.5V2.75z" />
      <path d="M22.28 7.78a.75.75 0 00-1.06-1.06l-5.72 5.72-3.72-3.72a.75.75 0 00-1.06 0l-6 6a.75.75 0 101.06 1.06l5.47-5.47 3.72 3.72a.75.75 0 001.06 0l6.25-6.25z" />
    </svg>
  );
};

export const MineIcon = (props: { className?: string }) => {
  return (
    <svg
      width="14"
      height="15"
      fill="currentColor"
      viewBox="0 0 14 15"
      className={props.className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill="currentColor"
        d="M8.74669 7.5L3.86244 12.3842C3.63037 12.6163 3.31563 12.7467 2.98744 12.7467C2.65925 12.7467 2.3445 12.6163 2.11244 12.3842C1.88037 12.1522 1.75 11.8374 1.75 11.5092C1.75 11.1811 1.88037 10.8663 2.11244 10.6342L6.99669 5.75"
      />
      <path
        d="M10.5 9.2474L12.8333 6.91406"
        strokeWidth="2"
        fill="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fill="currentColor"
        d="M12.5417 7.20823L11.4252 6.09173C11.2064 5.87299 11.0834 5.57629 11.0833 5.2669V4.58323L9.765 3.2649C9.11431 2.61459 8.23376 2.24661 7.31383 2.24056L5.25 2.22656L5.78667 2.7049C6.16785 3.04287 6.47307 3.45781 6.68221 3.92234C6.89135 4.38687 6.99966 4.89045 7 5.3999V6.33323L8.16667 7.4999H8.85033C9.15973 7.49996 9.45643 7.62292 9.67517 7.84173L10.7917 8.95823"
      />
    </svg>
  );
};

export const FormIcon = (props: { className?: string }) => {
  return (
    <svg
      fill="currentColor"
      aria-hidden="true"
      className={props.className}
      viewBox="-16 0 174 174"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0)">
        <path
          d="M141.131 92.1921C141.311 108.696 141.551 125.2 141.638 141.704C141.669 147.538 141.211 153.372 141.178 159.207C141.133 167.576 134.667 172.676 127.049 172.841C111.448 173.179 95.84 173.539 80.2375 173.462C62.1588 173.373 44.0806 172.895 26.0063 172.417C21.6324 172.223 17.2753 171.751 12.9616 171.003C7.99972 170.231 3.12713 164.299 2.86197 158.9C2.59943 153.523 2.38674 148.141 2.31651 142.758C1.99097 117.716 1.69815 92.6739 1.43825 67.6319C1.2781 52.9206 1.21648 38.208 0.990696 23.4975C0.925062 19.3278 0.623779 15.1447 2.31122 11.2296C3.21609 8.94785 4.44412 6.80819 5.95777 4.87582C7.82113 2.65609 10.5706 1.81459 13.5103 1.73386C19.5703 1.56715 25.6408 1.52787 31.6864 1.12816C53.0213 -0.280999 74.3528 0.819983 95.687 0.978816C100.224 1.01229 103.675 2.49382 107.022 5.67509C113.854 12.1676 121.186 18.1309 128.27 24.3602C130.967 26.7316 133.469 29.3277 136.206 31.6459C138.731 33.7862 139.712 36.4788 139.807 39.6489C139.974 45.2599 140.24 50.8726 140.319 56.481C140.485 68.3841 140.558 80.2901 140.67 92.1941L141.131 92.1921ZM93.829 9.36187C91.8455 9.20632 90.1876 8.98756 88.5257 8.95934C74.3817 8.72221 60.2373 8.50693 46.0923 8.31265C39.6983 8.22404 33.3035 8.18473 26.9088 8.14535C23.6593 8.125 20.4031 8.02281 17.1621 8.19215C12.6419 8.42777 11.2453 9.73401 10.4321 14.1755C9.62173 18.3692 9.43027 22.6593 9.86379 26.9087C10.1312 29.3632 10.2658 31.8304 10.2668 34.2995C10.335 73.9295 10.3864 113.559 10.4209 153.189C10.3998 153.975 10.4301 154.761 10.5116 155.542C11.1909 160.65 13.885 164.06 19.6858 164.289C41.3508 165.142 63.0147 165.151 84.6855 164.784C95.5748 164.599 106.465 164.507 117.355 164.383C120.046 164.353 122.743 164.453 125.428 164.325C130.119 164.101 132.47 161.752 132.428 157.164C132.344 148.19 132.131 139.217 131.953 130.245C131.861 125.644 131.765 121.042 131.608 116.442C131.239 105.562 130.653 94.6849 130.493 83.8015C130.31 71.5673 130.497 59.3278 130.511 47.0911C130.511 46.3402 130.416 45.5887 130.363 44.8201C129.982 44.7315 129.594 44.6715 129.204 44.6413C122.357 44.7791 115.512 44.9942 108.665 45.0349C106.534 45.0507 104.41 44.8058 102.338 44.3048C98.1148 43.2638 96.7858 41.6316 96.3394 37.2998C95.9371 33.3946 95.6759 29.4744 95.3254 25.5633C94.8528 20.3225 94.3573 15.0811 93.829 9.36378V9.36187ZM125.226 35.7468L125.381 35.0392L103.144 14.8606L102.633 15.1888C103.724 22.354 104.815 29.5192 105.94 36.9056L125.226 35.7468Z"
          fill="currentColor"
        />
        <path
          d="M22.3865 38.9465C22.3865 35.6983 22.2994 32.4483 22.4083 29.2034C22.5527 24.9241 23.8036 23.6701 28.1132 23.3919C34.1423 23.0026 40.1774 22.6929 46.2098 22.3516C48.9237 22.1987 50.8953 23.533 51.5037 26.4556C52.0564 29.4243 52.4436 32.4212 52.6635 35.4328C53.036 38.659 53.2331 41.9035 53.2542 45.151C53.1614 47.3675 52.7733 49.5614 52.1003 51.6753C51.4295 53.96 49.5407 55.0688 47.2343 55.139C41.301 55.3208 35.3612 55.5524 29.4298 55.4441C23.8825 55.3424 22.8264 54.1408 22.4319 48.684C22.3105 47.009 22.2219 45.3294 22.176 43.6505C22.1333 42.0838 22.1667 40.5152 22.1667 38.9478L22.3865 38.9465ZM30.0658 47.0566C32.6571 47.9046 39.2231 47.6676 43.3239 46.5925V29.6213C39.7796 28.8337 31.3437 28.8993 28.824 29.7578L30.0658 47.0566Z"
          fill="currentColor"
        />
        <path
          d="M69.2056 78.6196C82.5469 78.8887 95.8896 79.1329 109.23 79.465C110.679 79.5201 112.106 79.843 113.438 80.4173C114.072 80.6773 114.608 81.1301 114.971 81.7116C115.333 82.2938 115.503 82.9744 115.457 83.6583C115.469 84.3449 115.257 85.0156 114.853 85.5702C114.449 86.1255 113.875 86.5337 113.219 86.7333C111.394 87.2229 109.513 87.4684 107.623 87.4625C97.1957 87.2282 86.7738 86.7582 76.3465 86.583C66.8158 86.4222 57.2799 86.5541 47.7466 86.5173C41.915 86.4944 36.0821 86.4471 30.2532 86.2948C28.6926 86.1806 27.1472 85.9122 25.6398 85.4921C25.0796 85.3845 24.5779 85.076 24.2288 84.6251C23.8797 84.1742 23.7072 83.6111 23.7436 83.042C23.6921 82.4736 23.8464 81.9059 24.1782 81.4412C24.51 80.9772 24.9973 80.6477 25.5517 80.5119C26.6226 80.1988 27.7284 80.0203 28.8434 79.9802C42.2948 79.6179 55.7474 79.2786 69.201 78.9622C69.2023 78.8474 69.2036 78.7338 69.2056 78.6196Z"
          fill="currentColor"
        />
        <path
          d="M23.3169 99.1059C24.1117 96.6085 26.0218 96.302 27.9606 96.1733C32.1047 95.899 36.252 95.557 40.402 95.496C62.6366 95.1691 84.8723 94.8909 107.109 94.6625C108.676 94.6211 110.242 94.7786 111.769 95.1311C112.582 95.2945 113.315 95.7283 113.851 96.3617C114.386 96.9951 114.692 97.7906 114.717 98.6195C114.744 99.4485 114.488 100.262 113.993 100.927C113.499 101.593 112.794 102.071 111.993 102.285C110.607 102.714 109.171 102.954 107.721 102.997C102.442 103.029 97.1635 102.951 91.8852 102.865C72.4576 102.539 53.03 102.194 33.6024 101.828C31.1412 101.78 28.68 101.456 26.2299 101.162C24.7104 100.974 24.7201 100.89 23.3169 99.1059Z"
          fill="currentColor"
        />
        <path
          d="M75.4415 72.8886C65.897 72.8886 56.3513 72.7954 46.8088 72.9299C42.4382 72.991 38.0793 73.5252 33.7075 73.795C31.5803 73.9262 29.4413 74.0319 27.3154 73.9919C25.2604 73.9505 24.1085 73.1025 23.9786 71.826C23.8473 70.5021 24.7663 69.4763 26.8495 69.1967C32.5202 68.4366 38.1929 67.5447 43.8945 67.1955C64.0756 65.9596 84.2857 65.6853 104.502 65.8441C106.297 65.8224 108.093 65.9281 109.874 66.1611C111.869 66.4643 112.905 67.6707 112.874 69.2164C112.843 70.762 111.786 71.9126 109.784 72.1469C107.332 72.4396 104.866 72.5965 102.397 72.6175C93.4114 72.6707 84.4261 72.6385 75.4408 72.6385L75.4415 72.8886Z"
          fill="currentColor"
        />
        <path
          d="M99.8012 136.229C96.7729 136.229 93.7446 136.274 90.7175 136.217C87.9091 136.164 86.5248 135.186 86.5032 133.4C86.4815 131.614 87.8283 130.668 90.6414 130.483C96.236 130.118 101.83 129.727 107.428 129.416C108.879 129.328 110.336 129.394 111.774 129.613C112.456 129.681 113.092 129.989 113.567 130.483C114.043 130.978 114.327 131.625 114.369 132.309C114.537 133.927 113.945 135.296 112.438 135.881C111.082 136.397 109.654 136.694 108.206 136.761C105.41 136.892 102.603 136.8 99.8006 136.8C99.8006 136.611 99.8012 136.42 99.8012 136.229Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <rect
            width="141.113"
            height="173.93"
            fill="white"
            transform="translate(0.587891 0.0107422)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const ComplianceIcon = (props: { className?: string }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      className={props.className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.20667 9.79948L7.20667 10.7995L9.87333 8.13281"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.66668 3.99967H9.33334C10.6667 3.99967 10.6667 3.33301 10.6667 2.66634C10.6667 1.33301 10 1.33301 9.33334 1.33301H6.66668C6.00001 1.33301 5.33334 1.33301 5.33334 2.66634C5.33334 3.99967 6.00001 3.99967 6.66668 3.99967Z"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6667 2.67969C12.8867 2.79969 14 3.61969 14 6.66635V10.6664C14 13.333 13.3333 14.6664 10 14.6664H6C2.66667 14.6664 2 13.333 2 10.6664V6.66635C2 3.62635 3.11333 2.79969 5.33333 2.67969"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SafetyIcon = (props: { className?: string }) => {
  return (
    <svg
      fill="currentColor"
      aria-hidden="true"
      className={props.className}
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 446.02 446.02"
    >
      <g>
        <g>
          <path
            d="M129.031,262.984h-10.72c-5.929,0-10.737,4.803-10.737,10.731V395.92
                 c0,4.797-1.24,8.099-2.089,9.551H55.756c-0.848-1.452-2.089-4.754-2.089-9.551V273.715c0-5.929-4.803-10.731-10.737-10.731H31.014
                 c-5.26,0-9.546-4.286-9.546-9.551V151.516c0-5.265,4.286-9.551,9.546-9.551h98.012c2.551-7.816,8.066-14.212,15.273-17.933
                 c-4.242-2.17-8.98-3.53-14.076-3.53h-3.046c9.252-10.758,14.876-24.71,14.876-39.977c0-33.874-27.56-61.445-61.429-61.445
                 c-33.88,0-61.44,27.571-61.44,61.445c0,15.267,5.629,29.219,14.876,39.977h-3.046C13.913,120.502,0,134.416,0,151.516v101.917
                 c0,17.106,13.913,31.019,31.014,31.019h1.186v111.463c0,17.682,9.317,31.024,21.669,31.024h53.493
                 c12.358,0,21.669-13.342,21.669-31.024V284.452h1.186c5.096,0,9.834-1.349,14.076-3.535
                 C137.097,277.196,131.582,270.795,129.031,262.984z M80.629,40.548c22.039,0,39.966,17.938,39.966,39.983
                 c0,22.05-17.927,39.977-39.966,39.977c-22.045,0-39.977-17.927-39.977-39.977C40.652,58.481,58.584,40.548,80.629,40.548z"
          />
          <path
            d="M415.006,120.502h-3.04c9.246-10.758,14.876-24.71,14.876-39.977
                 c0-33.874-27.56-61.445-61.434-61.445c-33.88,0-61.44,27.571-61.44,61.445c0,15.267,5.635,29.219,14.881,39.977h-3.046
                 c-5.379,0-10.378,1.501-14.794,3.927c6.875,3.769,12.113,10.019,14.577,17.585c0.082,0,0.136-0.049,0.218-0.049h99.203
                 c5.265,0,9.551,4.286,9.551,9.551v101.917c0,5.265-4.286,9.551-9.551,9.551h-11.912c-5.929,0-10.737,4.803-10.737,10.731V395.92
                 c0,4.797-1.235,8.099-2.083,9.551h-49.729c-0.854-1.452-2.089-4.754-2.089-9.551V273.715c0-5.929-4.797-10.731-10.731-10.731
                 h-11.922c-0.082,0-0.136-0.043-0.218-0.049c-2.464,7.571-7.702,13.81-14.577,17.585c4.417,2.42,9.41,3.932,14.794,3.932h1.186
                 v111.463c0,17.682,9.312,31.024,21.669,31.024h53.493c12.352,0,21.669-13.342,21.669-31.024V284.452h1.186
                 c17.106,0,31.014-13.919,31.014-31.019V151.516C446.02,134.41,432.112,120.502,415.006,120.502z M365.407,120.502
                 c-22.045,0-39.972-17.927-39.972-39.977c0-22.045,17.927-39.983,39.972-39.983c22.039,0,39.977,17.938,39.977,39.983
                 C405.379,102.575,387.446,120.502,365.407,120.502z"
          />
          <path
            d="M302.918,253.433V151.516c0-3.329-0.669-6.489-1.643-9.502
                 c-2.458-7.571-7.702-13.815-14.577-17.585c-4.422-2.42-9.41-3.927-14.794-3.927h-3.046c9.246-10.758,14.876-24.71,14.876-39.977
                 c0-33.874-27.56-61.445-61.44-61.445c-33.874,0-61.434,27.571-61.434,61.445c0,15.267,5.629,29.219,14.876,39.977h-3.046
                 c-5.096,0-9.839,1.36-14.082,3.53c-7.207,3.72-12.722,10.117-15.273,17.933c-0.99,3.024-1.664,6.201-1.664,9.551v101.917
                 c0,3.356,0.674,6.527,1.664,9.551c2.551,7.81,8.061,14.212,15.273,17.933c4.242,2.187,8.985,3.535,14.082,3.535h1.186v111.463
                 c0,17.682,9.312,31.024,21.669,31.024h53.499c12.352,0,21.669-13.342,21.669-31.024V284.452h1.186
                 c5.385,0,10.378-1.507,14.794-3.932c6.875-3.775,12.118-10.019,14.577-17.585C302.249,259.922,302.918,256.773,302.918,253.433z
                  M222.295,40.548c22.045,0,39.977,17.938,39.977,39.983c0,22.05-17.927,39.977-39.977,39.977
                 c-22.039,0-39.972-17.927-39.972-39.977C182.328,58.481,200.255,40.548,222.295,40.548z M281.444,253.433
                 c0,5.189-4.166,9.377-9.323,9.502c-0.082,0.005-0.141,0.049-0.223,0.049h-11.912c-5.929,0-10.742,4.803-10.742,10.731V395.92
                 c0,4.797-1.235,8.099-2.083,9.551h-49.729c-0.848-1.452-2.089-4.754-2.089-9.551V273.715c0-5.929-4.803-10.731-10.731-10.731
                 h-10.726h-1.191c-5.265,0-9.546-4.286-9.546-9.551V151.516c0-5.265,4.281-9.551,9.546-9.551h1.191h98.012
                 c0.082,0,0.141,0.049,0.223,0.049c5.156,0.136,9.323,4.313,9.323,9.502C281.444,151.516,281.444,253.433,281.444,253.433z"
          />
        </g>
      </g>
    </svg>
  );
};
export const AtmosphereIcon = (props: { className?: string }) => {
  return (
    <>
      <svg
        width="10"
        height="20"
        viewBox="0 0 10 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={props?.className}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.10944 0C2.65425 0 2.42863 0.355134 2.42863 0.791296V10.999C1.43837 11.4061 0.817561 12.0574 0.417166 13.0168C-0.238387 14.5861 -0.131274 16.3641 0.809144 17.7745C1.69477 19.1029 3.12916 19.9088 4.74956 19.9901C4.83901 19.9945 4.92929 20 5.01775 20C6.34305 20 7.611 19.4978 8.53524 18.5955C9.4542 17.6984 10 16.5074 10 15.2324C10 13.4058 9.03037 11.9795 7.71002 11.1869V0.791296C7.71002 0.355134 7.19558 0 6.74039 0H3.10944ZM3.41889 0.949555H6.71976V11.4738C6.71976 11.6586 6.76143 11.8315 6.93638 11.909C8.65019 12.6705 8.93753 14.1379 8.93753 15.2324C8.93753 16.257 8.53862 17.2146 7.80286 17.9327C7.01659 18.7006 5.95454 19.0972 4.81145 19.0406C3.51421 18.9754 2.36633 18.3274 1.65499 17.2601C0.886219 16.1074 0.767223 14.6487 1.30427 13.363C1.64822 12.5388 2.18882 12.0623 3.02691 11.7606C3.21969 11.6913 3.41889 11.5137 3.41889 11.3155V0.949555ZM0.994818 1.83976C0.631722 1.83976 0.334644 2.12463 0.334644 2.4728C0.334644 2.82097 0.631722 3.09594 0.994818 3.09594C1.35791 3.09594 1.65499 2.82097 1.65499 2.4728C1.65499 2.12463 1.35791 1.83976 0.994818 1.83976ZM0.994818 3.9367C0.631722 3.9367 0.334644 4.22156 0.334644 4.56973C0.334644 4.9179 0.631722 5.20277 0.994818 5.20277C1.35791 5.20277 1.65499 4.9179 1.65499 4.56973C1.65499 4.22156 1.35791 3.9367 0.994818 3.9367ZM0.994818 6.04352C0.631722 6.04352 0.334644 6.32807 0.334644 6.67656C0.334644 7.02473 0.631722 7.2997 0.994818 7.2997C1.35791 7.2997 1.65499 7.02473 1.65499 6.67656C1.65499 6.32807 1.35791 6.04352 0.994818 6.04352ZM0.994818 8.14046C0.631722 8.14046 0.334644 8.42501 0.334644 8.77349C0.334644 9.12166 0.631722 9.40653 0.994818 9.40653C1.35791 9.40653 1.65499 9.12166 1.65499 8.77349C1.65499 8.42501 1.35791 8.14046 0.994818 8.14046ZM2.51115 14.8764C2.49564 15.1929 2.49052 15.2294 2.49052 15.3709C2.49052 16.8658 3.72728 18.1108 5.28595 18.1108C6.84495 18.1108 8.01948 16.8457 8.01948 15.3511C8.01948 15.2093 8.01469 15.1929 7.99885 14.8764H2.51115Z"
          fill="currentColor"
        />
      </svg>
    </>
  );
};
export const MaintenanceIcon = (props: { className?: string }) => {
  return (
    <svg
      fill="currentColor"
      aria-hidden="true"
      className={props.className}
      viewBox="0 0 64 64"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M27.954 14.394C30.2317 12.09 33.1724 10.5552 36.365 10.0041C39.5576 9.45293 42.8426 9.91301 45.761 11.32L38.099 19.042C37.3647 19.7842 36.9529 20.786 36.9529 21.83C36.9529 22.874 37.3647 23.8759 38.099 24.618L39.945 26.479C40.3069 26.8449 40.7377 27.1354 41.2126 27.3336C41.6874 27.5319 42.1969 27.6339 42.7115 27.6339C43.2261 27.6339 43.7356 27.5319 44.2104 27.3336C44.6853 27.1354 45.1161 26.8449 45.478 26.479L53.14 18.757C54.6974 22.0413 55.0787 25.7609 54.2201 29.2928C53.3615 32.8247 51.3152 35.9542 48.424 38.157C46.1421 39.8846 43.4283 40.95 40.5806 41.2365C37.7329 41.523 34.8613 41.0194 32.281 39.781L18.418 53.751C18.0562 54.1169 17.6255 54.4073 17.1507 54.6056C16.6759 54.8038 16.1665 54.9058 15.652 54.9058C15.1375 54.9058 14.6281 54.8038 14.1533 54.6056C13.6785 54.4073 13.2478 54.1169 12.886 53.751L11.04 51.89C10.3052 51.1482 9.89305 50.1462 9.89305 49.102C9.89305 48.0579 10.3052 47.0559 11.04 46.3141L24.905 32.3401C23.5083 29.3978 23.0517 26.0968 23.5972 22.8858C24.1428 19.6749 25.664 16.7099 27.954 14.394V14.394Z"
        stroke="currentColor"
        strokeWidth="4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const RefreshIcon = (props: { className: string }) => {
  return (
    <svg
      width="16"
      height="17"
      className={props?.className}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 8.5C14 9.68669 13.6481 10.8467 12.9888 11.8334C12.3295 12.8201 11.3925 13.5892 10.2961 14.0433C9.19974 14.4974 7.99334 14.6162 6.82946 14.3847C5.66557 14.1532 4.59647 13.5818 3.75736 12.7426C2.91824 11.9035 2.3468 10.8344 2.11529 9.67054C1.88378 8.50666 2.0026 7.30026 2.45672 6.2039C2.91085 5.10754 3.67988 4.17047 4.66658 3.51118C5.65327 2.85189 6.81331 2.5 8 2.5C9.67737 2.50631 11.2874 3.16082 12.4933 4.32667L14 5.83333"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14 2.5V5.83333H10.6667"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const AuditLogIcon = (props: { className: string }) => {
  return (
    <svg
      width="14"
      height="15"
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.74479 1.66406H3.49479C3.18537 1.66406 2.88863 1.78698 2.66983 2.00577C2.45104 2.22456 2.32813 2.52131 2.32812 2.83073V12.1641C2.32813 12.4735 2.45104 12.7702 2.66983 12.989C2.88863 13.2078 3.18537 13.3307 3.49479 13.3307H10.4948C10.8042 13.3307 11.101 13.2078 11.3197 12.989C11.5385 12.7702 11.6615 12.4735 11.6615 12.1641V4.58073L8.74479 1.66406Z"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.17188 1.66406V3.9974C8.17188 4.30681 8.29479 4.60356 8.51358 4.82235C8.73238 5.04115 9.02912 5.16406 9.33854 5.16406H11.6719"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.875 7.5L4.375 7.5"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.875 10.125L4.375 10.125"
        stroke={`${props.className?.includes('black') ? 'black' : 'white'}`}
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SettingIcon = (props: { className?: string }) => {
  return (
    <svg
      fill="currentColor"
      aria-hidden="true"
      className={props.className}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="style=stroke">
        <g id="setting">
          <path
            id="vector (Stroke)"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 9.75C10.7574 9.75 9.75 10.7574 9.75 12C9.75 13.2426 10.7574 14.25 12 14.25C13.2426 14.25 14.25 13.2426 14.25 12C14.25 10.7574 13.2426 9.75 12 9.75ZM8.25 12C8.25 9.92893 9.92893 8.25 12 8.25C14.0711 8.25 15.75 9.92893 15.75 12C15.75 14.0711 14.0711 15.75 12 15.75C9.92893 15.75 8.25 14.0711 8.25 12Z"
            fill="currentColor"
          />
          <path
            id="vector (Stroke)_2"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.60347 3.77018C9.3358 3.32423 8.77209 3.18551 8.35347 3.43457L8.34256 3.44105L6.61251 4.43096C6.06514 4.74375 5.8763 5.45289 6.1894 5.9948L5.54 6.37001L6.18888 5.99391C6.72395 6.91704 6.86779 7.92882 6.38982 8.75823C5.91192 9.58753 4.96479 9.97001 3.9 9.97001C3.26678 9.97001 2.75 10.4917 2.75 11.12V12.88C2.75 13.5084 3.26678 14.03 3.9 14.03C4.96479 14.03 5.91192 14.4125 6.38982 15.2418C6.86773 16.0711 6.72398 17.0827 6.18909 18.0058C5.87642 18.5476 6.06491 19.2561 6.6121 19.5688L8.35352 20.5654C8.77214 20.8144 9.33577 20.6758 9.60345 20.2299L9.71093 20.0442C10.2458 19.1214 11.052 18.4925 12.0087 18.4925C12.9662 18.4925 13.77 19.1219 14.3 20.0458C14.3002 20.0462 14.3004 20.0466 14.3007 20.047L14.4065 20.2298C14.6742 20.6758 15.2379 20.8145 15.6565 20.5655L15.6674 20.559L17.3975 19.5691C17.9434 19.2571 18.1351 18.5578 17.8198 18.0038C17.2858 17.0813 17.1426 16.0705 17.6202 15.2418C18.0981 14.4125 19.0452 14.03 20.11 14.03C20.7432 14.03 21.26 13.5084 21.26 12.88V11.12C21.26 10.4868 20.7384 9.97001 20.11 9.97001C19.0452 9.97001 18.0981 9.58753 17.6202 8.75824C17.1423 7.92899 17.286 6.91744 17.8208 5.99445C18.1336 5.45258 17.9451 4.74391 17.3979 4.43119L15.6565 3.43466C15.2379 3.1856 14.6742 3.32423 14.4065 3.77019L14.2991 3.95579C13.7642 4.8786 12.958 5.50751 12.0012 5.50751C11.0439 5.50751 10.2402 4.87825 9.71021 3.95455C9.70992 3.95403 9.70962 3.95352 9.70933 3.95301L9.60347 3.77018ZM7.59248 2.14193C8.75191 1.45656 10.2226 1.87704 10.8946 3.00654L10.8991 3.01421L11.0091 3.20423L11.0107 3.20701C11.3807 3.85247 11.7666 4.00751 12.0012 4.00751C12.237 4.00751 12.6259 3.85115 13.0009 3.20423C13.001 3.20412 13.0009 3.20434 13.0009 3.20423L13.1154 3.00651C13.7874 1.877 15.2581 1.45656 16.4175 2.14193L18.1421 3.12883C19.4147 3.85604 19.8463 5.48713 19.1194 6.74522L19.1189 6.74611C18.7439 7.39298 18.8028 7.8062 18.9198 8.00929C19.0369 8.21249 19.3648 8.47001 20.11 8.47001C21.5616 8.47001 22.76 9.65323 22.76 11.12V12.88C22.76 14.3317 21.5768 15.53 20.11 15.53C19.3648 15.53 19.0369 15.7875 18.9198 15.9907C18.8028 16.1938 18.7439 16.607 19.1189 17.2539L19.1212 17.2579C19.8444 18.5235 19.4157 20.1431 18.1425 20.871C18.1424 20.871 18.1426 20.8709 18.1425 20.871L16.4174 21.8581C15.258 22.5434 13.7874 22.123 13.1154 20.9935L13.1109 20.9858L13.0009 20.7958L12.9993 20.793C12.6293 20.1476 12.2434 19.9925 12.0087 19.9925C11.773 19.9925 11.3841 20.1489 11.0091 20.7958C11.009 20.7959 11.0091 20.7957 11.0091 20.7958L10.8946 20.9935C10.2226 22.123 8.75199 22.5434 7.59257 21.8581L5.8679 20.8712C5.86776 20.8711 5.86803 20.8713 5.8679 20.8712C4.59558 20.1439 4.16378 18.5128 4.8906 17.2548L4.89112 17.2539C5.26605 16.607 5.20721 16.1938 5.09018 15.9907C4.97308 15.7875 4.64521 15.53 3.9 15.53C2.43322 15.53 1.25 14.3317 1.25 12.88V11.12C1.25 9.66837 2.43322 8.47001 3.9 8.47001C4.64521 8.47001 4.97308 8.21249 5.09018 8.00929C5.20721 7.8062 5.26605 7.39298 4.89112 6.74611L4.8906 6.74522C4.16378 5.48726 4.59518 3.85639 5.86749 3.12906L7.59248 2.14193Z"
            fill="currentColor"
          />
        </g>
      </g>
    </svg>
  );
};

export const SunIcon = (props: { className?: string }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 0C8.40625 0 8.75 0.34375 8.75 0.75V2.75C8.75 3.1875 8.40625 3.5 8 3.5C7.5625 3.5 7.25 3.1875 7.25 2.75V0.75C7.25 0.34375 7.5625 0 8 0ZM8 12.5C8.40625 12.5 8.75 12.8438 8.75 13.25V15.25C8.75 15.6875 8.40625 16 8 16C7.5625 16 7.25 15.6875 7.25 15.25V13.25C7.25 12.8438 7.5625 12.5 8 12.5ZM15.25 8.75H13.25C12.8125 8.75 12.5 8.4375 12.5 8C12.5 7.59375 12.8125 7.25 13.25 7.25H15.25C15.6562 7.25 16 7.59375 16 8C16 8.4375 15.6562 8.75 15.25 8.75ZM3.5 8C3.5 8.4375 3.15625 8.75 2.75 8.75H0.75C0.3125 8.75 0 8.4375 0 8C0 7.59375 0.3125 7.25 0.75 7.25H2.75C3.15625 7.25 3.5 7.59375 3.5 8ZM13.6562 3.40625L12.2188 4.84375C11.9375 5.125 11.4688 5.125 11.1562 4.84375C10.875 4.53125 10.875 4.0625 11.1562 3.78125L12.5938 2.34375C12.875 2.0625 13.3438 2.0625 13.6562 2.34375C13.9375 2.65625 13.9375 3.125 13.6562 3.40625ZM4.8125 11.1875C5.09375 11.5 5.09375 11.9688 4.8125 12.25L3.375 13.6562C3.09375 13.9688 2.625 13.9688 2.34375 13.6562C2.03125 13.375 2.03125 12.9062 2.34375 12.625L3.75 11.1875C4.03125 10.9062 4.5 10.9062 4.8125 11.1875ZM12.5938 13.6562L11.1562 12.25C10.875 11.9688 10.875 11.5 11.1562 11.1875C11.4688 10.9062 11.9375 10.9062 12.2188 11.1875L13.6562 12.625C13.9375 12.9062 13.9375 13.375 13.6562 13.6562C13.3438 13.9688 12.875 13.9688 12.5938 13.6562ZM4.8125 4.84375C4.5 5.125 4.03125 5.125 3.75 4.84375L2.34375 3.40625C2.03125 3.125 2.03125 2.65625 2.34375 2.34375C2.625 2.0625 3.09375 2.0625 3.375 2.34375L4.8125 3.78125C5.09375 4.0625 5.09375 4.53125 4.8125 4.84375ZM8 11.5C6.71875 11.5 5.59375 10.8438 4.96875 9.75C4.3125 8.6875 4.3125 7.34375 4.96875 6.25C5.59375 5.1875 6.71875 4.5 8 4.5C9.25 4.5 10.375 5.1875 11 6.25C11.6562 7.34375 11.6562 8.6875 11 9.75C10.375 10.8438 9.25 11.5 8 11.5Z"
        fill="#FFB132"
      />
    </svg>
  );
};
export const WifiIcon = (props: { className?: string }) => {
  return (
    <svg
      width="22"
      height="15"
      viewBox="0 0 22 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 0C11.8438 0 12.5 0.75 12.4375 1.59375L11.9688 8.09375C11.9375 8.59375 11.5 9 11 9C10.4688 9 10.0312 8.59375 10 8.09375L9.53125 1.59375C9.46875 0.75 10.125 0 11 0ZM8.53125 1.65625L8.5625 2.25C6.3125 2.71875 4.28125 3.8125 2.6875 5.34375C2.28125 5.75 1.65625 5.71875 1.25 5.3125C0.875 4.9375 0.90625 4.28125 1.28125 3.90625C3.34375 1.96875 5.96875 0.59375 8.90625 0.15625C8.625 0.59375 8.5 1.125 8.53125 1.65625ZM13.4375 1.65625C13.4688 1.125 13.3438 0.59375 13.0625 0.15625C16 0.59375 18.625 1.96875 20.6875 3.90625C21.0625 4.28125 21.0938 4.9375 20.7188 5.3125C20.3125 5.71875 19.6875 5.75 19.2812 5.34375C17.6875 3.8125 15.6562 2.71875 13.4062 2.25L13.4375 1.65625ZM5.03125 7.25C6.09375 6.34375 7.375 5.625 8.78125 5.28125L8.9375 7.3125C7.96875 7.625 7.09375 8.125 6.34375 8.75C5.9375 9.125 5.3125 9.09375 4.9375 8.6875C4.59375 8.25 4.625 7.625 5.03125 7.25ZM13.1875 5.28125C14.5938 5.625 15.875 6.34375 16.9375 7.25C17.3438 7.625 17.4062 8.25 17.0312 8.6875C16.6562 9.09375 16.0312 9.125 15.625 8.75C14.875 8.125 14 7.625 13.0312 7.3125L13.1875 5.28125ZM13 12C13 12.7188 12.5938 13.375 12 13.75C11.375 14.0938 10.5938 14.0938 10 13.75C9.375 13.375 9 12.7188 9 12C9 11.3125 9.375 10.6562 10 10.2812C10.5938 9.9375 11.375 9.9375 12 10.2812C12.5938 10.6562 13 11.3125 13 12Z"
        fill="#FFB132"
      />
    </svg>
  );
};
export const AxeIcon = (props: { className?: string }) => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.4375 11.6875V11.6562C15.4062 11.625 15.4062 11.5938 15.375 11.5625C15.3438 11.4688 15.2812 11.3125 15.2188 11.1562C15.0625 10.8125 14.8125 10.3125 14.5 9.75C13.8438 8.5625 12.8125 7.0625 11.375 5.625C9.9375 4.1875 8.40625 3.15625 7.25 2.5C6.6875 2.1875 6.1875 1.9375 5.84375 1.78125C5.65625 1.71875 5.53125 1.65625 5.4375 1.625C5.40625 1.59375 5.375 1.59375 5.34375 1.5625H5.3125C5.125 1.5 4.96875 1.3125 4.96875 1.09375C4.96875 0.875 5.09375 0.6875 5.28125 0.625C6.28125 0.21875 7.375 0 8.5 0C10.3438 0 12.0625 0.625 13.4688 1.625L13.75 1.3125C13.9375 1.125 14.2188 1.03125 14.4688 1C14.75 1 15 1.125 15.1875 1.3125L15.6875 1.8125C15.875 2 16 2.25 16 2.53125C15.9688 2.78125 15.875 3.0625 15.6875 3.25L15.375 3.53125C16.375 4.9375 17 6.65625 17 8.5C17 9.625 16.7812 10.7188 16.375 11.6875C16.2812 11.9062 16.0938 12.0312 15.9062 12C15.6875 12 15.5 11.875 15.4375 11.6875ZM1.28125 15.7188C0.90625 15.3438 0.875 14.7188 1.25 14.3125L9.75 5.46875C10.0625 5.75 10.375 6.03125 10.6875 6.3125C10.9688 6.625 11.25 6.9375 11.5312 7.21875L2.6875 15.75C2.28125 16.125 1.65625 16.0938 1.28125 15.7188Z"
        fill="#FFB132"
      />
    </svg>
  );
};

export const DownArrow = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="15"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-move-down inline"
    >
      <path d="M8 18L12 22L16 18" />
      <path d="M12 2V22" />
    </svg>
  );
};

export const UpArrow = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="15"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-move-up inline"
    >
      <path d="M8 6L12 2L16 6" />
      <path d="M12 2V22" />
    </svg>
  );
};

export const Sun = (props: { className?: string }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 0C8.40625 0 8.75 0.34375 8.75 0.75V2.75C8.75 3.1875 8.40625 3.5 8 3.5C7.5625 3.5 7.25 3.1875 7.25 2.75V0.75C7.25 0.34375 7.5625 0 8 0ZM8 12.5C8.40625 12.5 8.75 12.8438 8.75 13.25V15.25C8.75 15.6875 8.40625 16 8 16C7.5625 16 7.25 15.6875 7.25 15.25V13.25C7.25 12.8438 7.5625 12.5 8 12.5ZM15.25 8.75H13.25C12.8125 8.75 12.5 8.4375 12.5 8C12.5 7.59375 12.8125 7.25 13.25 7.25H15.25C15.6562 7.25 16 7.59375 16 8C16 8.4375 15.6562 8.75 15.25 8.75ZM3.5 8C3.5 8.4375 3.15625 8.75 2.75 8.75H0.75C0.3125 8.75 0 8.4375 0 8C0 7.59375 0.3125 7.25 0.75 7.25H2.75C3.15625 7.25 3.5 7.59375 3.5 8ZM13.6562 3.40625L12.2188 4.84375C11.9375 5.125 11.4688 5.125 11.1562 4.84375C10.875 4.53125 10.875 4.0625 11.1562 3.78125L12.5938 2.34375C12.875 2.0625 13.3438 2.0625 13.6562 2.34375C13.9375 2.65625 13.9375 3.125 13.6562 3.40625ZM4.8125 11.1875C5.09375 11.5 5.09375 11.9688 4.8125 12.25L3.375 13.6562C3.09375 13.9688 2.625 13.9688 2.34375 13.6562C2.03125 13.375 2.03125 12.9062 2.34375 12.625L3.75 11.1875C4.03125 10.9062 4.5 10.9062 4.8125 11.1875ZM12.5938 13.6562L11.1562 12.25C10.875 11.9688 10.875 11.5 11.1562 11.1875C11.4688 10.9062 11.9375 10.9062 12.2188 11.1875L13.6562 12.625C13.9375 12.9062 13.9375 13.375 13.6562 13.6562C13.3438 13.9688 12.875 13.9688 12.5938 13.6562ZM4.8125 4.84375C4.5 5.125 4.03125 5.125 3.75 4.84375L2.34375 3.40625C2.03125 3.125 2.03125 2.65625 2.34375 2.34375C2.625 2.0625 3.09375 2.0625 3.375 2.34375L4.8125 3.78125C5.09375 4.0625 5.09375 4.53125 4.8125 4.84375ZM8 11.5C6.71875 11.5 5.59375 10.8438 4.96875 9.75C4.3125 8.6875 4.3125 7.34375 4.96875 6.25C5.59375 5.1875 6.71875 4.5 8 4.5C9.25 4.5 10.375 5.1875 11 6.25C11.6562 7.34375 11.6562 8.6875 11 9.75C10.375 10.8438 9.25 11.5 8 11.5Z"
        fill="#FFB132"
      />
    </svg>
  );
};

export const Hammer = (props: { className?: string }) => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.4375 11.6875V11.6562C15.4062 11.625 15.4062 11.5938 15.375 11.5625C15.3438 11.4688 15.2812 11.3125 15.2188 11.1562C15.0625 10.8125 14.8125 10.3125 14.5 9.75C13.8438 8.5625 12.8125 7.0625 11.375 5.625C9.9375 4.1875 8.40625 3.15625 7.25 2.5C6.6875 2.1875 6.1875 1.9375 5.84375 1.78125C5.65625 1.71875 5.53125 1.65625 5.4375 1.625C5.40625 1.59375 5.375 1.59375 5.34375 1.5625H5.3125C5.125 1.5 4.96875 1.3125 4.96875 1.09375C4.96875 0.875 5.09375 0.6875 5.28125 0.625C6.28125 0.21875 7.375 0 8.5 0C10.3438 0 12.0625 0.625 13.4688 1.625L13.75 1.3125C13.9375 1.125 14.2188 1.03125 14.4688 1C14.75 1 15 1.125 15.1875 1.3125L15.6875 1.8125C15.875 2 16 2.25 16 2.53125C15.9688 2.78125 15.875 3.0625 15.6875 3.25L15.375 3.53125C16.375 4.9375 17 6.65625 17 8.5C17 9.625 16.7812 10.7188 16.375 11.6875C16.2812 11.9062 16.0938 12.0312 15.9062 12C15.6875 12 15.5 11.875 15.4375 11.6875ZM1.28125 15.7188C0.90625 15.3438 0.875 14.7188 1.25 14.3125L9.75 5.46875C10.0625 5.75 10.375 6.03125 10.6875 6.3125C10.9688 6.625 11.25 6.9375 11.5312 7.21875L2.6875 15.75C2.28125 16.125 1.65625 16.0938 1.28125 15.7188Z"
        fill="#FFB132"
      />
    </svg>
  );
};

export const Offline = (props: { className?: string }) => {
  return (
    <svg
      width="22"
      height="15"
      viewBox="0 0 22 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 0C11.8438 0 12.5 0.75 12.4375 1.59375L11.9688 8.09375C11.9375 8.59375 11.5 9 11 9C10.4688 9 10.0312 8.59375 10 8.09375L9.53125 1.59375C9.46875 0.75 10.125 0 11 0ZM8.53125 1.65625L8.5625 2.25C6.3125 2.71875 4.28125 3.8125 2.6875 5.34375C2.28125 5.75 1.65625 5.71875 1.25 5.3125C0.875 4.9375 0.90625 4.28125 1.28125 3.90625C3.34375 1.96875 5.96875 0.59375 8.90625 0.15625C8.625 0.59375 8.5 1.125 8.53125 1.65625ZM13.4375 1.65625C13.4688 1.125 13.3438 0.59375 13.0625 0.15625C16 0.59375 18.625 1.96875 20.6875 3.90625C21.0625 4.28125 21.0938 4.9375 20.7188 5.3125C20.3125 5.71875 19.6875 5.75 19.2812 5.34375C17.6875 3.8125 15.6562 2.71875 13.4062 2.25L13.4375 1.65625ZM5.03125 7.25C6.09375 6.34375 7.375 5.625 8.78125 5.28125L8.9375 7.3125C7.96875 7.625 7.09375 8.125 6.34375 8.75C5.9375 9.125 5.3125 9.09375 4.9375 8.6875C4.59375 8.25 4.625 7.625 5.03125 7.25ZM13.1875 5.28125C14.5938 5.625 15.875 6.34375 16.9375 7.25C17.3438 7.625 17.4062 8.25 17.0312 8.6875C16.6562 9.09375 16.0312 9.125 15.625 8.75C14.875 8.125 14 7.625 13.0312 7.3125L13.1875 5.28125ZM13 12C13 12.7188 12.5938 13.375 12 13.75C11.375 14.0938 10.5938 14.0938 10 13.75C9.375 13.375 9 12.7188 9 12C9 11.3125 9.375 10.6562 10 10.2812C10.5938 9.9375 11.375 9.9375 12 10.2812C12.5938 10.6562 13 11.3125 13 12Z"
        fill="#FFB132"
      />
    </svg>
  );
};
export const PrintIcon = (props: { className?: string }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props?.className}
    >
      <path
        d="M4 0H11.0625C11.5938 0 12.125 0.21875 12.5 0.59375L13.4062 1.5C13.7812 1.875 14 2.40625 14 2.9375V5H12V2.9375L11.0625 2H4V5H2V2C2 0.90625 2.875 0 4 0ZM12 11H4V11.5V12V14H12V12V11ZM14 12V14C14 15.125 13.0938 16 12 16H4C2.875 16 2 15.125 2 14V12H1C0.4375 12 0 11.5625 0 11V8C0 6.90625 0.875 6 2 6H14C15.0938 6 16 6.90625 16 8V11C16 11.5625 15.5312 12 15 12H14ZM13.5 7.75C13.0625 7.75 12.75 8.09375 12.75 8.5C12.75 8.9375 13.0625 9.25 13.5 9.25C13.9062 9.25 14.25 8.9375 14.25 8.5C14.25 8.09375 13.9062 7.75 13.5 7.75Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const ActiveTabIcon = (props: { className?: string }) => {
  return (
    <svg
      width="76"
      height="14"
      viewBox="0 0 76 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props?.className}
    >
      <g filter="url(#filter0_d_2343_693)">
        <path
          d="M4.09766 8C4.09766 5.79086 5.88852 4 8.09766 4H67.0977C69.3068 4 71.0977 5.79086 71.0977 8V10H4.09766V8Z"
          fill="#FFC870"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_2343_693"
          x="0.0976562"
          y="0"
          width="75"
          height="14"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 0.847059 0 0 0 0 0.596078 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_2343_693"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2343_693"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export const DropdownIcon = (props: { className?: string }) => {
  return (
    <svg
      width="8"
      height="10"
      viewBox="0 0 8 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="inline-block mr-2"
    >
      <path
        d="M3.79492 9.48828L0.513672 6.05078C0.337891 5.85547 0.337891 5.5625 0.533203 5.38672C0.728516 5.21094 1.02148 5.21094 1.19727 5.40625L3.67773 8.00391V1.34375C3.67773 1.08984 3.87305 0.875 4.14648 0.875C4.40039 0.875 4.61523 1.08984 4.61523 1.34375V8.00391L7.07617 5.40625C7.25195 5.21094 7.56445 5.21094 7.74023 5.38672C7.93555 5.5625 7.93555 5.85547 7.75977 6.05078L4.47852 9.48828C4.38086 9.58594 4.26367 9.625 4.14648 9.625C4.00977 9.625 3.89258 9.58594 3.79492 9.48828Z"
        fill="#FFD084"
      />
    </svg>
  );
};

export const ProdActiveTabIcon = (props: { tabName?: string }) => {
  if (props?.tabName == 'mine') {
    return (
      <svg
        width="44"
        height="14"
        viewBox="0 0 44 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="ml-[-4px]"
      >
        <g filter="url(#filter0_d_3054_9372)">
          <path
            d="M4.09766 8C4.09766 5.79086 5.88852 4 8.09766 4H35.0977C37.3068 4 39.0977 5.79086 39.0977 8V10H4.09766V8Z"
            fill="#FFC870"
          />
        </g>
        <defs>
          <filter
            id="filter0_d_3054_9372"
            x="0.0976562"
            y="0"
            width="43"
            height="14"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset />
            <feGaussianBlur stdDeviation="2" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 0.847059 0 0 0 0 0.596078 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_3054_9372"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_3054_9372"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    );
  } else if (props?.tabName == 'alerts') {
    return (
      <svg
        width="57"
        height="14"
        viewBox="0 0 57 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_d_5463_22306)">
          <path
            d="M4.09766 8C4.09766 5.79086 5.88852 4 8.09766 4H48.0977C50.3068 4 52.0977 5.79086 52.0977 8V10H4.09766V8Z"
            fill="#FFC870"
          />
        </g>
        <defs>
          <filter
            id="filter0_d_5463_22306"
            x="0.0976562"
            y="0"
            width="56"
            height="14"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset />
            <feGaussianBlur stdDeviation="2" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 0.847059 0 0 0 0 0.596078 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_5463_22306"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_5463_22306"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    );
  } else {
    return (
      <ActiveTabIcon
        className={props?.tabName == 'compare' ? 'ml-[-4px]' : 'ml-[-6px]'}
      />
    );
  }
};

export const ProdDownArrow = (props: { className?: string }) => {
  return (
    <svg
      width="8"
      height="5"
      viewBox="0 0 8 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 1L4 4L7 1"
        stroke="#FE4A6A"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ProdUpArrow = (props: { className?: string }) => {
  return (
    <svg
      width="8"
      height="5"
      viewBox="0 0 8 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 4L4 1L1 4"
        stroke="#96FB60"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PlusIcon = (props: { className?: string; stroke?: string }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${props?.className} rotate-45`}
    >
      <path
        d="M15.9974 29.3346C23.3612 29.3346 29.3307 23.3651 29.3307 16.0013C29.3307 8.63751 23.3612 2.66797 15.9974 2.66797C8.6336 2.66797 2.66406 8.63751 2.66406 16.0013C2.66406 23.3651 8.6336 29.3346 15.9974 29.3346Z"
        stroke={`${props?.stroke ?? '#D0D0D0'}`}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20 12L12 20"
        stroke={`${props?.stroke ?? '#D0D0D0'}`}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 12L20 20"
        stroke={`${props?.stroke ?? '#D0D0D0'}`}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const MinusIcon = (props: { className?: string; stroke?: string }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${props?.className} rotate-45`}
    >
      <path
        d="M15.9974 29.3346C23.3612 29.3346 29.3307 23.3651 29.3307 16.0013C29.3307 8.63751 23.3612 2.66797 15.9974 2.66797C8.6336 2.66797 2.66406 8.63751 2.66406 16.0013C2.66406 23.3651 8.6336 29.3346 15.9974 29.3346Z"
        stroke={`${props?.stroke ?? '#D0D0D0'}`}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20 12L12 20"
        stroke={`${props?.stroke ?? '#D0D0D0'}`}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DualArrowTextIcon = (props: { className?: string; fill?: string }) => {
  const fill = props.fill ?? "white";

  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path
        d="M7.53045 3.5261C7.76157 3.76346 7.76157 4.14838 7.53045 4.38726C7.4149 4.50593 7.2623 4.56527 7.1112 4.56527C6.96011 4.56527 6.8075 4.50593 6.69196 4.38726L4.44449 2.0791V13.3897C4.44449 13.7259 4.17932 13.9983 3.8519 13.9983C3.52449 13.9983 3.25932 13.7259 3.25932 13.3897V2.0791L1.01185 4.38726C0.780734 4.62462 0.404457 4.62462 0.173339 4.38726C-0.0577795 4.1499 -0.0577795 3.76498 0.173339 3.5261L3.43266 0.178771C3.4608 0.149862 3.49043 0.125519 3.52303 0.102698C3.53784 0.0920477 3.55414 0.0859619 3.56895 0.0768336C3.58673 0.0661834 3.60451 0.0555321 3.62525 0.0464042C3.64451 0.0372754 3.66525 0.032711 3.68599 0.0266257C3.70228 0.0220614 3.71858 0.0144539 3.73636 0.0114112C3.81339 -0.00380372 3.89191 -0.00380372 3.96895 0.0114112C3.98673 0.0144539 4.00154 0.0220614 4.01932 0.0266257C4.04006 0.0327116 4.0608 0.037276 4.08006 0.0464042C4.09932 0.0540117 4.1171 0.0661829 4.13636 0.0768336C4.15117 0.0859625 4.16747 0.0920483 4.18228 0.102698C4.21488 0.125521 4.24599 0.149864 4.27265 0.178771L7.53045 3.5261ZM15.8267 9.61198C15.5955 9.37462 15.2193 9.37462 14.9881 9.61198L12.7407 11.9201V0.609554C12.7407 0.273301 12.4755 0.000965551 12.1481 0.000965551C11.8207 0.000965551 11.5555 0.273301 11.5555 0.609554V11.9201L9.30804 9.61198C9.07692 9.37462 8.70064 9.37462 8.46952 9.61198C8.23841 9.84934 8.23841 10.2343 8.46952 10.4731L11.7288 13.8205C11.757 13.8494 11.7866 13.8737 11.8192 13.8965C11.834 13.9072 11.8503 13.9133 11.8651 13.9224C11.8844 13.9331 11.9022 13.9437 11.9214 13.9528C11.9407 13.962 11.9614 13.9665 11.9822 13.9726C11.9985 13.9772 12.0148 13.9848 12.0325 13.9878C12.0711 13.9954 12.1096 14 12.1481 14C12.1866 14 12.2251 13.9954 12.2637 13.9878C12.2814 13.9848 12.2977 13.9772 12.314 13.9726C12.3348 13.9665 12.354 13.962 12.3748 13.9528C12.394 13.9437 12.4118 13.9331 12.4311 13.9224C12.4459 13.9133 12.4622 13.9072 12.477 13.8965C12.5096 13.8737 12.5407 13.8494 12.5674 13.8205L15.8267 10.4731C16.0578 10.2358 16.0578 9.84934 15.8267 9.61198Z"
        fill={fill}
      />
    </svg>
  );
};

export const AscendingIcon = (props: { className?: string; fill1?: string; fill2?: string }) => {
  const fill1 = props.fill1 ?? "#FFB132";
  const fill2 = props.fill2 ?? "white";

  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path
        d="M3.96923 0.0114112C3.98681 0.0144955 4.00142 0.0225048 4.01903 0.0270362C4.03967 0.0330906 4.06041 0.0375331 4.07958 0.0465674C4.09881 0.0541625 4.117 0.0662024 4.13622 0.0768409C4.15104 0.0859697 4.16731 0.0925582 4.18212 0.103208C4.21464 0.125983 4.24535 0.150548 4.27196 0.17938L7.52978 3.52606C7.76089 3.76342 7.76089 4.14851 7.52978 4.38739C7.41424 4.50598 7.26187 4.5661 7.11083 4.5661C6.95979 4.56605 6.80738 4.50602 6.69189 4.38739L4.44384 2.07977V13.3903C4.44369 13.7263 4.1792 13.9985 3.85204 13.9987C3.52472 13.9987 3.25942 13.7264 3.25927 13.3903V2.07977L1.01122 4.38739C0.780096 4.62465 0.404417 4.62471 0.173332 4.38739C-0.0577725 4.15003 -0.0577818 3.76493 0.173332 3.52606L3.43212 0.17938C3.46027 0.150472 3.49035 0.126029 3.52294 0.103208C3.53776 0.0925581 3.55402 0.0859689 3.56884 0.0768409C3.58654 0.066234 3.60482 0.0556581 3.62548 0.0465674C3.64462 0.0375726 3.66544 0.0330765 3.68603 0.0270362C3.70218 0.0224875 3.71823 0.0144694 3.73583 0.0114112C3.81287 -0.00380372 3.89219 -0.00380372 3.96923 0.0114112Z"
        fill={fill1}
      />
      <path
        d="M11.9691 13.9867C11.9869 13.9836 12.0021 13.9756 12.0199 13.971C12.0405 13.965 12.0614 13.9606 12.0805 13.9515C12.0995 13.9439 12.1171 13.9318 12.1361 13.9212C12.1509 13.9121 12.1672 13.9055 12.182 13.8949C12.2145 13.8721 12.2462 13.8485 12.2729 13.8197L15.5307 10.472C15.7618 10.2346 15.7618 9.84956 15.5307 9.61068C15.4152 9.49211 15.2627 9.43303 15.1117 9.43294C14.9606 9.43294 14.8073 9.492 14.6918 9.61068L12.4447 11.9193V0.608724C12.4447 0.272472 12.1794 -0.000650816 11.852 -0.000650816C11.5246 -0.000573797 11.2592 0.27252 11.2592 0.608724V11.9193L9.01211 9.61068C8.78099 9.37333 8.40436 9.37332 8.17324 9.61068C7.94225 9.84801 7.94225 10.2332 8.17324 10.472L11.433 13.8197C11.4609 13.8483 11.4906 13.8722 11.5229 13.8949C11.5376 13.9054 11.554 13.9122 11.5688 13.9212C11.5865 13.9319 11.6047 13.9424 11.6254 13.9515C11.6446 13.9606 11.6653 13.965 11.6859 13.971C11.7022 13.9756 11.7189 13.9836 11.7367 13.9867C11.8137 14.0018 11.8922 14.0019 11.9691 13.9867Z"
        fill={fill2}
      />
    </svg>
  );
};
export const DescendingIcon = (props: { className?: string; fill1?: string; fill2?: string }) => {
  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path
        d="M11.9691 13.9867C11.9869 13.9836 12.0021 13.9756 12.0199 13.971C12.0405 13.965 12.0614 13.9606 12.0805 13.9515C12.0995 13.9439 12.1171 13.9318 12.1361 13.9212C12.1509 13.9121 12.1672 13.9055 12.182 13.8949C12.2145 13.8721 12.2462 13.8485 12.2729 13.8197L15.5307 10.472C15.7618 10.2346 15.7618 9.84956 15.5307 9.61068C15.4152 9.49211 15.2627 9.43303 15.1117 9.43294C14.9606 9.43294 14.8073 9.492 14.6918 9.61068L12.4447 11.9193V0.608724C12.4447 0.272472 12.1794 -0.000650816 11.852 -0.000650816C11.5246 -0.000573797 11.2592 0.27252 11.2592 0.608724V11.9193L9.01211 9.61068C8.78099 9.37333 8.40436 9.37332 8.17324 9.61068C7.94225 9.84801 7.94225 10.2332 8.17324 10.472L11.433 13.8197C11.4609 13.8483 11.4906 13.8722 11.5229 13.8949C11.5376 13.9054 11.554 13.9122 11.5688 13.9212C11.5865 13.9319 11.6047 13.9424 11.6254 13.9515C11.6446 13.9606 11.6653 13.965 11.6859 13.971C11.7022 13.9756 11.7189 13.9836 11.7367 13.9867C11.8137 14.0018 11.8922 14.0019 11.9691 13.9867Z"
        fill={props.fill1 ?? "#FFB132"}
      />
      <path
        d="M3.96826 0.0113792C3.98602 0.0144191 4.00129 0.0214619 4.01904 0.0260276C4.03978 0.0321135 4.06033 0.0374072 4.07959 0.0465354C4.09885 0.054143 4.11697 0.0661587 4.13623 0.0768089C4.15098 0.0858511 4.1674 0.0916106 4.18213 0.102199C4.21463 0.124958 4.24536 0.149571 4.27197 0.178371L7.52978 3.52603C7.7609 3.76339 7.7609 4.14848 7.52978 4.38736C7.41427 4.50577 7.26177 4.56509 7.11084 4.56509C6.95991 4.56507 6.80738 4.50579 6.69189 4.38736L4.44385 2.07876V13.3893C4.44385 13.7254 4.17926 13.9975 3.85205 13.9977C3.52464 13.9977 3.25928 13.7256 3.25928 13.3893V2.07876L1.01123 4.38736C0.780198 4.62422 0.404365 4.62423 0.173339 4.38736C-0.0577795 4.15 -0.0577795 3.76491 0.173339 3.52603L3.43213 0.178371C3.46028 0.149463 3.49036 0.12502 3.52295 0.102199C3.5377 0.0916522 3.55411 0.0858893 3.56885 0.0768089C3.58647 0.0662528 3.604 0.0556084 3.62451 0.0465354C3.64377 0.0374066 3.66529 0.0321129 3.68603 0.0260276C3.70212 0.0214744 3.71831 0.0144263 3.73584 0.0113792C3.81277 -0.00381437 3.89132 -0.00377174 3.96826 0.0113792Z"
        fill={props.fill2 ?? "white"}
      />
    </svg>
  );
};

export const CalendarIcon = (props: {
  className?: string;
  stroke?: string;
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke={props.stroke ?? "currentColor"}
      className={props.className}
      width={24}
      height={24}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6.75 3v2.25M17.25 3v2.25M3 8.25h18M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25M3 18.75a2.25 2.25 0 002.25 2.25h13.5a2.25 2.25 0 002.25-2.25"
      />
    </svg>
  );
};

export const DragIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 24 24"
      height="1.5em"
      width="1.5em"
      xmlns="http://www.w3.org/2000/svg"
      className={`${props?.className}`}
    >
      <path
        d="M11 11V5.82843L9.17157 7.65685L7.75736 6.24264L12 2L16.2426 6.24264L14.8284 7.65685L13 5.82843V11H18.1716L16.3431 9.17157L17.7574 7.75736L22 12L17.7574 16.2426L16.3431 14.8284L18.1716 13H13V18.1716L14.8284 16.3431L16.2426 17.7574L12 22L7.75736 17.7574L9.17157 16.3431L11 18.1716V13H5.82843L7.65685 14.8284L6.24264 16.2426L2 12L6.24264 7.75736L7.65685 9.17157L5.82843 11H11Z"
      />
    </svg>
  );
};

export const CheckboxIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 512 512"
      height="1.5em"
      width="1.5em"
      xmlns="http://www.w3.org/2000/svg"
      className={`${props?.className}`}
    >
      <path
        d="M416 64H96c-17.7 0-32 14.3-32 32v320c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-52.5 134.5L229.8 332.8h-.1c-1.7 1.7-6.3 5.5-11.6 5.5-3.8 0-8.1-2.1-11.7-5.7l-56-56c-1.6-1.6-1.6-4.1 0-5.7l17.8-17.8c.8-.8 1.8-1.2 2.8-1.2 1 0 2 .4 2.8 1.2l44.4 44.4 122-122.9c.8-.8 1.8-1.2 2.8-1.2 1.1 0 2.1.4 2.8 1.2l17.5 18.1c1.8 1.7 1.8 4.2.2 5.8z"
      />
    </svg>
  );
};

export const ColorIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 512 512"
      height="1.5em"
      width="1.5em"
      xmlns="http://www.w3.org/2000/svg"
      className={`${props?.className}`}
    >
      <path
        fill="none"
        d="M0 0h24v24H0z"
      />
      <path
        d="M416 352c-12.6-.84-21-4-28-12-14-16-14-36 5.49-52.48l32.82-29.14c50.27-44.41 50.27-117.21 0-161.63C389.26 64.14 339.54 48 287.86 48c-60.34 0-123.39 22-172 65.11-90.46 80-90.46 210.92 0 290.87 45 39.76 105.63 59.59 165.64 60h1.84c60 0 119.07-19.5 161.2-56.77C464 390 464 385 444.62 355.56 440 348 431 353 416 352zM112 208a32 32 0 1 1 32 32 32 32 0 0 1-32-32zm40 135a32 32 0 1 1 32-32 32 32 0 0 1-32 32zm40-199a32 32 0 1 1 32 32 32 32 0 0 1-32-32zm64 271a48 48 0 1 1 48-48 48 48 0 0 1-48 48zm72-239a32 32 0 1 1 32-32 32 32 0 0 1-32 32z"
      />
    </svg>
  );
};

export const SelectListIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 24 24"
      height="1.5em"
      width="1.5em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.75 11.5h11.5a.75.75 0 0 1 0 1.5H8.75a.75.75 0 0 1 0-1.5Zm0 6h11.5a.75.75 0 0 1 0 1.5H8.75a.75.75 0 0 1 0-1.5Zm-5-12h10a.75.75 0 0 1 0 1.5h-10a.75.75 0 0 1 0-1.5ZM5 12a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm-1 7a1 1 0 1 1 0-2 1 1 0 0 1 0 2ZM19.309 7.918l-2.245-2.501A.25.25 0 0 1 17.25 5h4.49a.25.25 0 0 1 .185.417l-2.244 2.5a.25.25 0 0 1-.372 0Z"
      />
    </svg>
  );
};

export const ImageIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 512 512"
      height="1.5em"
      width="1.5em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M448 80c8.8 0 16 7.2 16 16l0 319.8-5-6.5-136-176c-4.5-5.9-11.6-9.3-19-9.3s-14.4 3.4-19 9.3L202 340.7l-30.5-42.7C167 291.7 159.8 288 152 288s-15 3.7-19.5 10.1l-80 112L48 416.3l0-.3L48 96c0-8.8 7.2-16 16-16l384 0zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zm80 192a48 48 0 1 0 0-96 48 48 0 1 0 0 96z"
      />
    </svg>
  );
};

export const InputIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="none"
      strokeWidth="2"
      viewBox="0 0 24 24"
      strokeLinecap="round"
      strokeLinejoin="round"
      height="1.5em"
      width="1.5em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 4h1a3 3 0 0 1 3 3 3 3 0 0 1 3-3h1"
      />
      <path
        d="M13 20h-1a3 3 0 0 1-3-3 3 3 0 0 1-3 3H5"
      />
      <path
        d="M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1"
      />
      <path
        d="M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7"
      />
      <path
        d="M9 7v10"
      />
    </svg>
  );
};

export const RadioIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 24 24"
      height="1.5em"
      width="1.5em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill="none"
        d="M0 0h24v24H0V0z"
      />
      <path
        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
      />
      <circle
        cx="12"
        cy="12"
        r="5"
      />
    </svg>
  );
};

export const CloneIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      width="18"
      height="18"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
      />
</svg>
  );
};

export const UndoIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3"
      />
    </svg>
  );
};

export const RedoIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m15 15 6-6m0 0-6-6m6 6H9a6 6 0 0 0 0 12h3"
      />
    </svg>
  );
};

export const LeftAlign = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25H12"
      />
    </svg>
  );
};

export const CenterAlign = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
      />
    </svg>
  );
};

export const RightAlign = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.75 6.75h16.5M3.75 12h16.5M12 17.25h8.25"
      />
    </svg>
  );
};

export const GroupIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3"
      />
    </svg>
  );
};

export const ClearIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15 12H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      />
    </svg>
  );
};

export const TextareaIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 16 16"
      strokeWidth={0}
      stroke="currentColor"
      height="1.5em"
      width="1.5em"
      className={props.className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M0 4.5A2.5 2.5 0 0 1 2.5 2h11A2.5 2.5 0 0 1 16 4.5v7a2.5 2.5 0 0 1-2.5 2.5h-11A2.5 2.5 0 0 1 0 11.5zM2.5 3A1.5 1.5 0 0 0 1 4.5v7A1.5 1.5 0 0 0 2.5 13h11a1.5 1.5 0 0 0 1.5-1.5v-7A1.5 1.5 0 0 0 13.5 3zm10.854 4.646a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708-.708l3-3a.5.5 0 0 1 .708 0m0 2.5a.5.5 0 0 1 0 .708l-.5.5a.5.5 0 0 1-.708-.708l.5-.5a.5.5 0 0 1 .708 0"
      />
    </svg>
  );
};

export const SignatureIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 256 256"
      strokeWidth="0"
      stroke="currentColor"
      height="1.5em"
      width="1.5em"
      className={props.className}
    >
      <path
        d="M232,168H63.86c2.66-5.24,5.33-10.63,8-16.11,15,1.65,32.58-8.78,52.66-31.14,5,13.46,14.45,30.93,30.58,31.25,9.06.18,18.11-5.2,27.42-16.37C189.31,143.75,203.3,152,232,152a8,8,0,0,0,0-16c-30.43,0-39.43-10.45-40-16.11a7.67,7.67,0,0,0-5.46-7.75,8.14,8.14,0,0,0-9.25,3.49c-12.07,18.54-19.38,20.43-21.92,20.37-8.26-.16-16.66-19.52-19.54-33.42a8,8,0,0,0-14.09-3.37C101.54,124.55,88,133.08,79.57,135.29,88.06,116.42,94.4,99.85,98.46,85.9c6.82-23.44,7.32-39.83,1.51-50.1-3-5.38-9.34-11.8-22.06-11.8C61.85,24,49.18,39.18,43.14,65.65c-3.59,15.71-4.18,33.21-1.62,48s7.87,25.55,15.59,31.94c-3.73,7.72-7.53,15.26-11.23,22.41H24a8,8,0,0,0,0,16H37.41c-11.32,21-20.12,35.64-20.26,35.88a8,8,0,1,0,13.71,8.24c.15-.26,11.27-18.79,24.7-44.12H232a8,8,0,0,0,0-16ZM58.74,69.21C62.72,51.74,70.43,40,77.91,40c5.33,0,7.1,1.86,8.13,3.67,3,5.33,6.52,24.19-21.66,86.39C56.12,118.78,53.31,93,58.74,69.21Z"
      />
    </svg>
  );
};

export const LabelIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="2"
      stroke="currentColor"
      height="1.5em"
      width="1.5em"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={props.className}
    >
      <path
        d="M16.52 7h-12.52l4 5l-4 5h12.52a1 1 0 0 0 .78 -.375l3.7 -4.625l-3.7 -4.625a1 1 0 0 0 -.78 -.375"
      />
    </svg>
  );
};

export const HeaderIcon = (props: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 512 512"
      strokeWidth="0"
      stroke="currentColor"
      height="1.5em"
      width="1.5em"
      className={props.className}
    >
      <path
        d="M448 96v320h32a16 16 0 0 1 16 16v32a16 16 0 0 1-16 16H320a16 16 0 0 1-16-16v-32a16 16 0 0 1 16-16h32V288H160v128h32a16 16 0 0 1 16 16v32a16 16 0 0 1-16 16H32a16 16 0 0 1-16-16v-32a16 16 0 0 1 16-16h32V96H32a16 16 0 0 1-16-16V48a16 16 0 0 1 16-16h160a16 16 0 0 1 16 16v32a16 16 0 0 1-16 16h-32v128h192V96h-32a16 16 0 0 1-16-16V48a16 16 0 0 1 16-16h160a16 16 0 0 1 16 16v32a16 16 0 0 1-16 16z"
      />
    </svg>
  );
};

export const ImportIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M5.552,20.968a2.577,2.577,0,0,1-2.5-2.73c-.012-2.153,0-4.306,0-6.459a.5.5,0,0,1,1,0c0,2.2-.032,4.4,0,6.6.016,1.107.848,1.589,1.838,1.589H18.353A1.546,1.546,0,0,0,19.825,19a3.023,3.023,0,0,0,.1-1.061V11.779h0a.5.5,0,0,1,1,0c0,2.224.085,4.465,0,6.687a2.567,2.567,0,0,1-2.67,2.5Z"></path><path d="M11.63,15.818a.459.459,0,0,0,.312.138c.014,0,.027.005.042.006s.027,0,.041-.006a.457.457,0,0,0,.312-.138l3.669-3.669a.5.5,0,0,0-.707-.707l-2.815,2.815V3.515a.5.5,0,0,0-1,0V14.257L8.668,11.442a.5.5,0,0,0-.707.707Z"></path>
    </svg>
  );
};

export const ExportIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M5.552,20.968a2.577,2.577,0,0,1-2.5-2.73c-.012-2.153,0-4.306,0-6.459a.5.5,0,0,1,1,0c0,2.2-.032,4.4,0,6.6.016,1.107.848,1.589,1.838,1.589H18.353A1.546,1.546,0,0,0,19.825,19a3.023,3.023,0,0,0,.1-1.061V11.779h0a.5.5,0,0,1,1,0c0,2.224.085,4.465,0,6.687a2.567,2.567,0,0,1-2.67,2.5Z"></path><path d="M12.337,3.176a.455.455,0,0,0-.311-.138c-.015,0-.028,0-.043-.006s-.027,0-.041.006a.457.457,0,0,0-.312.138L7.961,6.845a.5.5,0,0,0,.707.707l2.816-2.815V15.479a.5.5,0,0,0,1,0V4.737L15.3,7.552a.5.5,0,0,0,.707-.707Z"></path>
    </svg>
  );
};

export const SingleArrow = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 256 512"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path d="M31.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L127.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L201.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34z"></path>
    </svg>
  );
};

export const DoubleArrow = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 448 512"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path d="M223.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L319.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L393.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34zm-192 34l136 136c9.4 9.4 24.6 9.4 33.9 0l22.6-22.6c9.4-9.4 9.4-24.6 0-33.9L127.9 256l96.4-96.4c9.4-9.4 9.4-24.6 0-33.9L201.7 103c-9.4-9.4-24.6-9.4-33.9 0l-136 136c-9.5 9.4-9.5 24.6-.1 34z"></path>
    </svg>
  );
};

export const GenerateIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path d="M20.4668 8.69379L20.7134 8.12811C21.1529 7.11947 21.9445 6.31641 22.9323 5.87708L23.6919 5.53922C24.1027 5.35653 24.1027 4.75881 23.6919 4.57612L22.9748 4.25714C21.9616 3.80651 21.1558 2.97373 20.7238 1.93083L20.4706 1.31953C20.2942 0.893489 19.7058 0.893489 19.5293 1.31953L19.2761 1.93083C18.8442 2.97373 18.0384 3.80651 17.0252 4.25714L16.308 4.57612C15.8973 4.75881 15.8973 5.35653 16.308 5.53922L17.0677 5.87708C18.0555 6.31641 18.8471 7.11947 19.2866 8.12811L19.5331 8.69379C19.7136 9.10792 20.2864 9.10792 20.4668 8.69379ZM2 4C2 3.44772 2.44772 3 3 3H14V5H4V19H20V11H22V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4ZM7 8H17V11H15V10H13V14H14.5V16H9.5V14H11V10H9V11H7V8Z"></path>
    </svg>
  );
};

export const RevokeIcon = (props: { className?: string }) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth="0"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <path fill="none" d="M0 0h24v24H0z"></path>
      <path d="M21.19 21.19 2.81 2.81 1.39 4.22l2.27 2.27A9.91 9.91 0 0 0 2 12c0 5.52 4.48 10 10 10 2.04 0 3.93-.61 5.51-1.66l2.27 2.27 1.41-1.42zm-10.6-4.59-4.24-4.24 1.41-1.41 2.83 2.83.18-.18 1.41 1.41-1.59 1.59zm3-5.84-7.1-7.1A9.91 9.91 0 0 1 12 2c5.52 0 10 4.48 10 10 0 2.04-.61 3.93-1.66 5.51L15 12.17l2.65-2.65-1.41-1.41-2.65 2.65z"></path>
    </svg>
  );
};

