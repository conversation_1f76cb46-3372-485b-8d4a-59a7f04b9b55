import { IsString, IsOptional, IsNumber } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateMineDto {
  @IsOptional()
  @IsString()
  @ApiProperty()
  name: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  location: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  code: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  companyId: number;
}
