import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mine } from './entities/mine.entity';
import { CreateMineDto } from './dtos/create-mine.dto';
import { AuditService } from '../audit/audit.service';

@Injectable()
export class MinesService {
  constructor(
    private auditService: AuditService,
    @InjectRepository(Mine) private mineRepository: Repository<Mine>
  ) {}

  async findAll() {
    const mines = await this.mineRepository.find({
      where: {
        isDelete: false,
      },
      relations: {
        company: true,
      },
      order: { createdAt: 'DESC' },
    });
    return mines;
  }

  async findAllByCompanyId(companyId: number) {
    const mines = await this.mineRepository.find({
      where: { companyId },
      relations: {
        company: true,
      },
    });
    return mines;
  }

  async getMine(id: number) {
    // return this.mineRepository.findOneBy({ id });
    return await this.mineRepository.findOne({
      where: {
        id,
      },
      relations: ['company'],
    });
  }

  async checkMineName(mineDto: any) {
    let name = await this.mineRepository.findOne({
      where: { name: mineDto?.name },
    });

    let code = await this.mineRepository.findOne({
      where: { code: mineDto?.code },
    });

    if (name) {
      return { ...name, duplicate: 'name' };
    } else if (code) {
      return { ...code, duplicate: 'code' };
    }
  }

  async create(mineDetails: CreateMineDto, user?: any) {
    let name = await this.mineRepository.findOne({
      where: { name: mineDetails?.name },
    });
    if (name) {
      throw new NotFoundException('Mine Name Already Exist!');
    }
    let code = await this.mineRepository.findOne({
      where: { code: mineDetails?.code },
    });
    if (code) {
      throw new NotFoundException('Mine Code Already Exist!');
    }
    if (user) {
      this.auidtLog(
        user,
        mineDetails.name,
        'Mine created',
        mineDetails.name,
        'Mine'
      );
    }
    return await this.mineRepository.save(mineDetails);
  }

  async updateMineStatus(id: number, user?: any) {
    const mine = await this.findMineById(id);
    if (!mine) {
      throw new NotFoundException(`Mine with id ${id} not found`);
    } else {
      if (mine.isActive === true) {
        mine.isActive = false;
        if (user) {
          this.auidtLog(user, mine.name, 'Mine deactivated', mine.name, 'Mine');
        }
      } else {
        mine.isActive = true;
        if (user) {
          this.auidtLog(user, mine.name, 'Mine activated', mine.name, 'Mine');
        }
      }

      return this.mineRepository.save(mine);
    }
  }

  async findMineById(id: number): Promise<Mine> {
    const mine = await this.mineRepository.findOne({ where: { id: id } });
    if (!mine) throw new NotFoundException(`Mine with id ${id} not found`);
    return mine;
  }

  async deleteMine(id: number, user?: any) {
    let mine = await this.findMineById(id);
    if (!mine) {
      throw new NotFoundException(`Mine with given id ${id} is not found`);
    } else {
      if (mine.isDelete === true) {
        mine.isDelete = false;
      } else {
        mine.isDelete = true;
      }
      if (user) {
        this.auidtLog(user, mine.name, 'Mine deleted', mine.name, 'Mine');
      }
      return this.mineRepository.save(mine);
    }
  }

  async update(id: number, args: Partial<Mine>, user?: any) {
    const mine = await this.getMine(id);
    if (!mine) throw new NotFoundException('Mine not found');
    Object.assign(mine, args);
    const updateMine = await this.mineRepository.save(mine);
    if (user) {
      this.auidtLog(user, mine.name, 'Mine updated', updateMine.name, 'Mine');
    }
    return updateMine;
  }

  async getMineByCompanyId(companyId: number) {
    const mine = await this.mineRepository.findOne({
      where: { companyId: companyId },
    });
    if (!mine) throw new NotFoundException(`mine not found`);
    return mine;
  }

  async auidtLog(
    user: any,
    mineName: string,
    action: string,
    forUser: string,
    forUserRole: string
  ) {
    this.auditService.createAuditLog(
      user,
      mineName,
      action,
      forUser,
      forUserRole
    );
  }
}
