import {
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
export class OnBoardCompanyDto {
  companyId: number;

  @IsString({ message: 'Name must be a string' })
  @Length(1, 100, {
    message: 'Name length must be between 1 and 100 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  companyName: string;

  @IsString({ message: 'Code must be a string' })
  @Length(1, 50, { message: 'Code length must be between 1 and 50 characters' })
  @IsNotEmpty()
  @ApiProperty()
  companyCode: string;

  @IsString()
  @ApiProperty()
  mineName: string;

  @IsString()
  @ApiProperty()
  mineLocation: string;

  @IsString()
  @ApiProperty()
  mineCode: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;

  @ApiProperty()
  email: string;

  @IsNumber()
  @ApiProperty()
  createdBy: number;

  @IsNumber()
  @ApiProperty()
  timezone: number;
}
