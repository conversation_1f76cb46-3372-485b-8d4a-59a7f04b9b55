import { getUserById } from '../../../api/users/userapis';
import decodeJWT from '../../../utils/jwtdecoder';
import { useQuery } from '@tanstack/react-query';
import SubMenuInnerNavBar from '../common/SubMenuInnerNavBar';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { getFeatures } from '../../../api/users/userapis';

const ProdReport = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['user_by_id'],
    queryFn: () => getUserById(Number(decodeJWT()?.userId)),
    refetchOnWindowFocus: false,
  });
  const params = useParams();
  const [repSecDate, setRepSecDate] = useState({
    startDate: new Date().toISOString().split('T')[0],
  });

  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  const getLastMonthToPast30Days = () => {
    const today = new Date();
    const startDate = new Date(today);
    startDate.setMonth(today.getMonth() - 1);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() - 31);

    return { startDate, endDate };
  };

  const getLast30Days = () => {
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - 30);
    const endDate = new Date(today);
    return { startDate, endDate };
  };

  let reportMineData = getLast30Days();
  let compareFromData = getLastMonthToPast30Days();

  const [reportMineDate, setReportMineDate] = useState({
    startDate: reportMineData.startDate.toISOString().split('T')[0],
    endDate: reportMineData.endDate.toISOString().split('T')[0],
  });

  const [compareToDate, setCompareToDate] = useState({
    startDate: reportMineData.startDate.toISOString().split('T')[0],
    endDate: reportMineData.endDate.toISOString().split('T')[0],
  });

  const [compareFromDate, setCompareFromDate] = useState({
    startDate: compareFromData.endDate.toISOString().split('T')[0],
    endDate: compareFromData.startDate.toISOString().split('T')[0],
  });

  if (
    features?.data.some(
      (feature: any) => feature.FeatureName == 'Production'
    ) ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <SubMenuInnerNavBar
        leftTitle="Reports"
        rightTitle="Production"
        tabNames={['mine', 'sections']}
        showSearchBox={false}
        pathToRender={
          decodeJWT()?.role == 'superuser'
            ? `Mines/${params?.mineId}/Production/report`
            : 'Production/report'
        }
        defaultTab="mine"
        repSecDate={repSecDate}
        setRepSecDate={setRepSecDate}
        reportMineDate={reportMineDate}
        setReportMineDate={setReportMineDate}
        compareToDate={compareToDate}
        setCompareToDate={setCompareToDate}
        compareFromDate={compareFromDate}
        setCompareFromDate={setCompareFromDate}
      />
    );
  } else {
    return '';
  }
};

export default ProdReport;
