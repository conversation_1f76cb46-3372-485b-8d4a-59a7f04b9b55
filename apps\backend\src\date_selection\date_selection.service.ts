import { Injectable } from '@nestjs/common';
import { CreateDateSelectionDto } from './dto/create-date_selection.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DateSelection } from './entities/date_selection.entity';

@Injectable()
export class DateSelectionService {
  @InjectRepository(DateSelection)
  private dateSelectionRepository: Repository<DateSelection>;

  async create(createDateSelectionDto: CreateDateSelectionDto) {
    const existingRecord = await this.dateSelectionRepository.findOne({
      where: {
        userId: createDateSelectionDto?.userId,
        type: createDateSelectionDto?.type,
      } as any,
    });
    if (!existingRecord) {
      return await this.dateSelectionRepository.save(createDateSelectionDto);
    } else {
      // delete createDateSelectionDto.userId;
      // return await this.dateSelectionRepository.update(
      //   existingRecord?.userId,
      //   createDateSelectionDto
      // );

      Object.assign(existingRecord, createDateSelectionDto);
      return await this.dateSelectionRepository.save(existingRecord);
    }
  }

  async findOne(id: number, type: string) {
    return await this.dateSelectionRepository.findOne({
      where: { userId: id, type: type } as any,
    });
  }
}
