import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  ManyToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Role } from '../../roles/entities/role.entity';
import { Feature } from '../../features/entities/feature.entity';

@Entity('role_feature_access')
export class RoleFeatureAccess {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'role_id', nullable: false })
  roleId: number;

  @Column({ name: 'feature_id', nullable: false })
  featureId: number;

  @Column({ name: 'has_read_access', default: true })
  hasReadAccess: boolean;

  @Column({ name: 'has_write_access', default: true })
  hasWriteAccess: boolean;

  @ManyToMany(() => Role)
  roles: Role[];

  @ManyToOne(() => Feature, (feature) => feature.roleFeatureAccess)
  @JoinColumn({ name: 'feature_id' })
  feature: Feature;

  @ManyToOne(() => Role, (role) => role.roleFeatureAccess)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;
}
