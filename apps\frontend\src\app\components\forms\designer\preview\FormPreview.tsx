import FormGroupPreview from './FormGroupPreview';
import { PlusIcon } from '../../../../../assets/icons/icons';
import { Id, Group, Item, SelectedItem } from '../../types';

interface Props {
	showPreview: Boolean;
	groups: Group[];
	items: Item[];
	createNewGroup: (index:number) => void;
	moveGroup: (fromIndex: number, direction: string) => void;
	moveItem: (groupId: Id, fromIndex: number, direction: string) => void;
	deleteGroup: (id: Id) => void;
	createNewItem: (groupId: Id) => void;
	cloneGroup: (group: Group) => void;
	cloneItem: (item: Item) => void;
	deleteItem: (item: Item) => void;
	selectedItem: SelectedItem;
	setSelectedItem: (item: SelectedItem) => void;
}

const FormGridArea = ({
	showPreview, groups, items,
	createNewGroup, moveGroup, moveItem, deleteGroup,
	createNewItem, cloneGroup, cloneItem,
	deleteItem, selectedItem, setSelectedItem
}: Props) => {
	function handleAddNewFormGroup(e: React.MouseEvent<HTMLButtonElement> | React.MouseEvent<HTMLDivElement>, index:number) {
		e.stopPropagation();
		createNewGroup(index);
	}

	return (
		<div className="form-grid-area flex flex-row flex-wrap h-fit w-full
			items-center overflow-x-auto overflow-y-hidden px-2 py-4">
			{!showPreview &&
				// <div className="flex w-full justify-center">
				// 	<button
				// 		title="Click to add group"
				// 		onClick={(e) => handleAddNewFormGroup(e, 0)}
				// 		className=" h-fit w-fit cursor-pointer justify-center
				// 		bg-mainBackgroundColor flex">
				// 		<PlusIcon className="w-6 h-6 opacity-80 hover:opacity-100" />
				// 	</button>
				// </div>
				<div className="flex w-full justify-center">
					<button
						title="Click to add group"
						onClick={(e) => handleAddNewFormGroup(e, 0)}
						className=" h-fit w-fit cursor-pointer justify-center px-2
						border-columnBackgroundColor border rounded-md border-x-columnBackgroundColor
						bg-mainBackgroundColor flex opacity-40 hover:opacity-100">
						Add Group
					</button>
				</div>
			}
			{groups.map((grp:Group, index:number) =>
				<div className="w-full m-0 p-0" key={index}>
					<FormGroupPreview
						key={index} group={grp}
						showPreview={showPreview}
						createNewItem={createNewItem}
						selectedItem={selectedItem}
						setSelectedItem={setSelectedItem}
						moveGroup={moveGroup}
						moveItem={moveItem}
						deleteGroup={deleteGroup}
						cloneGroup={cloneGroup}
						cloneItem={cloneItem}
						deleteItem={deleteItem}
						items={items.filter(item => item.groupId === grp.id)}
						questions={grp.questions}
					/>
					{!showPreview &&
						// <div className="flex w-full justify-center">
						// 	<button
						// 		title="Click to add group"
						// 		onClick={(e) => handleAddNewFormGroup(e, index+1)}
						// 		className="h-fit w-fit cursor-pointer justify-center
						// 		bg-mainBackgroundColor flex">
						// 		<PlusIcon className="w-6 h-6 opacity-80 hover:opacity-100" />
						// 	</button>
						// </div>
						<div className="flex w-full justify-center">
							<button
								title="Click to add group"
								onClick={(e) => handleAddNewFormGroup(e, index+1)}
								className=" h-fit w-fit cursor-pointer justify-center px-2
								border-columnBackgroundColor border rounded-md border-x-columnBackgroundColor
								bg-mainBackgroundColor flex opacity-40 hover:opacity-100">
								Add Group
							</button>
						</div>
					}
				</div>
			)}
		</div>
	);
};

export default FormGridArea;