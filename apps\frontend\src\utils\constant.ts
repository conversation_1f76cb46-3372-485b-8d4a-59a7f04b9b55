import dayjs from 'dayjs';
import { addMonths, getWeek } from './dateutils';
import { addDays, endOfMonth, startOfMonth } from 'date-fns';


export const emailRegEx =
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

export function escapeRegExp(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
}

const startOfCurrentMonth = startOfMonth(new Date());
const endOfCurrentMonth = endOfMonth(new Date());
const startOfLastMonth = startOfMonth(addMonths(new Date(), -1));
const endOfLastMonth = endOfMonth(addMonths(new Date(), -1));

export const shortcuts = {
  today: {
    text: 'Today',
    period: {
      start: new Date(),
      end: new Date(),
    },
  },
  yesterday: {
    text: 'Yesterday',
    period: {
      start: addDays(new Date(), -1),
      end: addDays(new Date(), -1),
    },
  },
  thisweek: {
    text: 'This Week',
    period: {
      start: getWeek('current')[0],
      end: getWeek('current')[1],
    },
  },
  lastweek: {
    text: 'Last Week',
    period: {
      start: getWeek('last')[0],
      end: getWeek('last')[1],
    },
  },
  thisMonth: {
    text: 'This Month',
    period: {
      start: startOfCurrentMonth,
      end: endOfCurrentMonth,
    },
  },
  lastMonth: {
    text: 'Last Month',
    period: {
      start: startOfLastMonth,
      end: endOfLastMonth,
    },
  },
  last6Months: {
    text: 'Last 6 months',
    period: {
      start: addMonths(new Date(), -6),
      end: new Date(),
    },
  },
  last12Months: {
    text: 'Last 12 months',
    period: {
      start: addMonths(new Date(), -12),
      end: new Date(),
    },
  },
  yearToDate: {
    text: 'Year To Date',
    period: {
      start: `${new Date().getFullYear()}-01-01`,
      end: new Date(),
    },
  },
};

export const prodReportShortcuts = {
  today: {
    text: 'Today',
    period: {
      start: new Date(),
      end: new Date(),
    },
  },
  yesterday: {
    text: 'Yesterday',
    period: {
      start: addDays(new Date(), -1),
      end: addDays(new Date(), -1),
    },
  },
  thisweek: {
    text: 'This Week',
    period: {
      start: getWeek('current')[0],
      end: getWeek('current')[1],
    },
  },
  lastweek: {
    text: 'Last Week',
    period: {
      start: getWeek('last')[0],
      end: getWeek('last')[1],
    },
  },
  thisMonth: {
    text: 'This Month',
    period: {
      start: startOfCurrentMonth,
      end: endOfCurrentMonth,
    },
  },
  lastMonth: {
    text: 'Last Month',
    period: {
      start: startOfLastMonth,
      end: endOfLastMonth,
    },
  },
};

export const generateWeeklyTickVals = (startDate: string, endDate: string) => {
  const tickVals = [];
  let current = dayjs(startDate);
  const end = dayjs(endDate);

  let monthsDiff = end.diff(current, 'month');

  if(monthsDiff <= 1 ) {
    monthsDiff = monthsDiff + 1;
  }


  while (current.isBefore(end)) {
    tickVals.push(current.format('YYYY-MM-DD'));

    current = current.add(monthsDiff, 'days');
  }

  if (!tickVals.includes(end.format('YYYY-MM-DD'))) {
    const lastDate = dayjs(tickVals[tickVals.length - 1]);
    const dayDiff = end.diff(lastDate, 'days');
    if (dayDiff != monthsDiff) {
      const last = lastDate.add(monthsDiff, 'days');
      tickVals.push(last.format('YYYY-MM-DD'));
    } else {
      tickVals.push(end.format('YYYY-MM-DD'));
    }
  }

  return tickVals;
};