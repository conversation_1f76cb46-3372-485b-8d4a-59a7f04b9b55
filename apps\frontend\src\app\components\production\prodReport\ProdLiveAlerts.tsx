import dayjs from 'dayjs';
import {
  AlertTableCompliance,
  ProdActiveTabIcon,
} from '../../../../assets/icons/icons';
import Table, { ColumnDef } from '../../common/Table';
import Plot from 'react-plotly.js';

export default function ProdLiveSection() {
  const columns: ColumnDef[] = [
    {
      key: 'dayTime',
      label: 'Day / Time',
      type: 'text',
      render: (row) => (
        <div>
          <span>
            {row?.dayTime?.toLocaleTimeString('en-US', { hour12: true })}
          </span>
          <p className="text-[#787878]">
            {dayjs(row?.dayTime).format('DD/MM/YYYY')}
          </p>
        </div>
      ),
    },
    {
      key: 'type',
      label: 'Type',
      type: 'text',
    },
    {
      key: 'section',
      label: 'Section',
      type: 'text',
    },
    {
      key: 'shift',
      label: 'Shift',
      type: 'text',
    },
    {
      key: 'compliance',
      label: 'Compliance',
      type: 'element',
      render: (row) => (
        <div className="ml-[50px]">
          {row?.compliance ? <AlertTableCompliance /> : ''}
        </div>
      ),
    },
    {
      key: 'pin',
      label: 'Pin',
      type: 'element',
      render: (row: any) => (
        <>
          <button className="bg-transparent  text-white-300 font-semibold w-[90px] ml-[25px] py-2 px-4 border-[1px] border-[#4AA8FE]  rounded-lg mr-10">
            {row?.pin ? 'Pin' : 'Unpin'}
          </button>
        </>
      ),
    },
  ];

  const alertsData = [
    {
      dayTime: new Date(),
      type: 'Planned Downtime',
      shift: 'AM shift',
      section: '5YN',
      compliance: false,
      pin: true,
    },
    {
      dayTime: new Date(),
      type: 'Planned Downtime',
      shift: 'AM shift',
      section: '5YN',
      compliance: true,
      pin: false,
    },
    {
      dayTime: new Date(),
      type: 'Planned Downtime',
      shift: 'AM shift',
      section: '5YN',
      compliance: true,
      pin: true,
    },
    {
      dayTime: new Date(),
      type: 'Planned Downtime',
      shift: 'AM shift',
      section: '5YN',
      compliance: false,
      pin: false,
    },
  ];

  const layout = {
    xaxis: {
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      autorange: 'reversed',
      fixedrange: true,
    },
    yaxis: {
      color: 'rgba(0, 0, 0, 0)',
      gridcolor: 'rgba(74, 168, 256, 50%)',
      gridwidth: '0.5px',
      fixedrange: true,
    },
    showlegend: false,
    margin: { t: 0, l: 0, b: 30, r: 0 },
    automargin: true,
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    dragmode: 'pan',
    modeBarButtonsToRemove: ['lasso2d', 'select2d'],
  };
  return (
    <div className="">
      <div className="bg-[#2c5c8bf6]">
        <div className=" w-[94%] m-auto py-2 !h-[40vh]">
          <div className="border border-dashed border-[1px] border-[#4AA8FE] rounded-sm m-2 opacity-40">
            <div className="opacity-40">
              <Plot
                data={[
                  {
                    x: [
                      '2021-11-01',
                      '2021-11-02',
                      '2021-11-03',
                      '2021-11-04',
                      '2021-11-05',
                      '2021-11-06',
                      '2021-11-07',
                      '2021-11-08',
                      '2021-11-09',
                      '2021-11-10',
                      '2021-11-11',
                      '2021-11-12',
                      '2021-11-13',
                      '2021-11-14',
                      '2021-11-15',
                      '2021-11-16',
                      '2021-11-17',
                      '2021-11-18',
                    ],
                    y: [
                      1, 4, 2, 3, 1, 3, 6, 3, 2, 1, 3, 5, 2, 5, 4, 5, 4, 5, 2,
                      1,
                    ],
                    mode: 'lines',
                    fill: 'tozeroy',
                    fillcolor: 'rgba(56, 134, 206, 0.44)/20',
                  },
                ]}
                layout={layout}
                className="!w-[100%] !h-[35vh]"
                config={{
                  displayModeBar: false,
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <div className="w-[93%] mx-auto mt-8">
        <div
          className={`mb-4 bg-[#25354354] border-[#4AA8FE]    border-dashed  border-[1px] rounded-sm opacity-40`}
        >
          <div className="flex mb-2 justify-between mx-4 opacity-40">
            <h2 className="text-[28px] text-white font-bold mt-3">{`Alerts(${alertsData?.length})`}</h2>
            <p className="text-white text-[16px] mt-4 underline">
              View all alerts
            </p>
          </div>
          <div className="w-[97%] m-auto h-full pt-0 opacity-40">
            <Table
              columns={columns}
              data={alertsData ?? []}
              searchText={''}
              searchOnColumn="minerName"
              backgroundColor={false}
              scrollable={true}
              sortable={true}
              dataRenderLimitMdScreen={4}
              dataRenderLimitLgScreen={5}
              tableHeightClassLg={`h-[240px]`}
              tableHeightClassMd={'h-[240px]'}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
