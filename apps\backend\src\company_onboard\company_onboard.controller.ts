import { Body, Controller, Post, Request, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { ApiTags } from '@nestjs/swagger';
import { OnBoardCompanyDto } from './dto/onboard-compnay.dto';
import { CompanyOnboardService } from './company_onboard.service';
import { Roles } from '../auth/gurads/roles.decorator';
import { Features } from '../auth/gurads/feature.decorator';
import { RolesGuard } from '../auth/gurads/roles.guard';

@Controller('portal/v1/company-onboard')
@ApiTags('companyonboard')
export class CompanyOnboardController {
  constructor(private readonly companyOnboardService: CompanyOnboardService) {}
  @Post()
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  OnboardCompany(@Body() onBoardCompanyDto: OnBoardCompanyDto, @Request() req) {
    return this.companyOnboardService.onBoardCompany(
      onBoardCompanyDto,
      req.user
    );
  }
}
