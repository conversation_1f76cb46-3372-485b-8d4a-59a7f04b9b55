import scanner from 'sonarqube-scanner';

scanner(
  {
    serverUrl: 'http://localhost:9000',
    token: 'sqp_d8daaa05126dd5c698dede21a35a70ada26d3f96',
    options: {
      'sonar.projectName': 'AnalyticsPlatform-Portal',
      'sonar.projectDescription': 'React Code',
      'sonar.projectKey': 'AnalyticsPlatform-Portal',
      'sonar.projectVersion': '0.0.1',
      'sonar.sourceEncoding': 'UTF-8',
      'sonar.exclusions':
        '**/node_modules/**, **/*.repo.tsx, **/*.module.tsx, **/*.stories.tsx, **/*.spec.ts, **/*.spec.tsx',
    },
  },
  () => process.exit()
);
