import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { CategoriesService } from './categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';

@Controller('portal/v1/categories')
@ApiTags('categories')
export class CategoriesController {
  constructor(private readonly categoryService: CategoriesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() categoryBody: CreateCategoryDto) {
    const categories = await this.categoryService.create(categoryBody);
    return categories;
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  find() {
    return this.categoryService.find();
  }

  @Get('/:id')
  @UseGuards(JwtAuthGuard)
  findOne(@Param('id') id: number) {
    return this.categoryService.findOne(id);
  }
}
