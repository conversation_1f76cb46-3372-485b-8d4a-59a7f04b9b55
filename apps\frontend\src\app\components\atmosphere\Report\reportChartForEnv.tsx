import React from 'react';
import Plot from 'react-plotly.js';
import ChartSkeletonAtmosphere from '../../../../assets/ChartSkeletonAtmosphere.png';

type LocationData = {
  label: string;
  gases: string[];
  values: number[][];
};

type GasChartProps = {
  data: {
    row: LocationData[];
  };
  gases: string[];
};

const unitObj: any = {
  CO: 'ppm',
  H2: 'ppm',
  'Abs. Pressure': 'in. H2O',
  Humidity: '%',
  Temperature: '℉',
  'Air Velocity': 'ft/min',
  'Comp. Pressure': 'in. H2O',
  'Diff. Pressure': 'in. H2O',
  Airflow: 'CFM',
};

const COLORS = ['#fff700', '#ffe135', '#ffd700', '#fcd116', '#ffea00'];
const COLORS2 = ['#e5b800', '#f6c700', '#f5e050', '#c9a400', '#bfa000'];

const CommonChartForReportForEnv: React.FC<GasChartProps> = ({
  data,
  gases,
}) => {
  const generateTimeLabels = (count: number): string[] => {
    return Array.from({ length: count }, (_, i) => `${i + 1}am`);
  };

  const getTracesForGas = (gas: string, yAxis: 'y' | 'y2' = 'y') => {
    const COLORSARR = yAxis == 'y2' ? COLORS2 : COLORS;
    return data.row
      .map((location, locIndex) => {
        const gasIndex = location.gases.findIndex((g) => g === gas);

        if (gasIndex !== -1 && location.values[gasIndex]) {
          return {
            x: generateTimeLabels(location.values[gasIndex].length),
            y: location.values[gasIndex],
            name: `${location.label} (${gas})`,
            type: 'scatter',
            mode: 'lines+markers',
            yaxis: yAxis,
            line: { color: COLORSARR[locIndex % COLORSARR.length] },
            marker: { color: COLORSARR[locIndex % COLORSARR.length] },
          };
        }
        return null;
      })
      .filter((trace): trace is any => trace !== null);
  };

  const getSingleGasLayout = (gas: string) => ({
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    margin: {
      t: 40,
      b: 80,
    },
    xaxis: {
      color: 'white',
      tickfont: {
        color: 'white',
      },
      showgrid: false,
    },
    yaxis: {
      title: {
        text: `${gas}(${unitObj[gas]})`,
        font: {
          color: 'white',
        },
      },
      tickfont: {
        color: 'white',
      },
    },
    legend: {
      orientation: 'h',
      y: -0.3,
      font: {
        color: 'white',
      },
    },
  });

  const getDualGasLayout = (gas1: string, gas2: string) => ({
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    margin: {
      t: 40,
      b: 80,
    },
    xaxis: {
      color: 'white',
      tickfont: {
        color: 'white',
      },
      showgrid: false,
    },
    yaxis: {
      title: {
        text: `${gas1}(${unitObj[gas1]})`,
        font: {
          color: 'white',
        },
      },
      tickfont: {
        color: 'white',
      },
      range: gas1 == 'Abs. Pressure' ? [1, 12] : [],
    },
    yaxis2: {
      title: {
        text: `${gas2}(${unitObj[gas2]})`,
        font: {
          color: 'white',
        },
      },
      side: 'right',
      overlaying: 'y',
      range: gas1 == 'Abs. Pressure' ? [10, 120] : [],
      position: 1,
      tickfont: {
        color: 'white',
      },
    },
    legend: {
      orientation: 'h',
      y: -0.3,
      font: {
        color: 'white',
      },
    },
  });

  const renderCharts = () => {
    const chartElements: JSX.Element[] = [];

    if (gases.length === 1) {
      const traces = getTracesForGas(gases[0]);
      chartElements.push(
        <div key="gas1" className="w-full" style={{ height: '350px' }}>
          <Plot
            data={[...traces]}
            layout={getSingleGasLayout(gases[0])}
            style={{ width: '100%', height: '100%' }}
            config={{ displayModeBar: false }}
          />
        </div>
      );
    }

    if (gases.length === 2) {
      gases.forEach((gas, index) => {
        const traces = getTracesForGas(gas);
        console.log(traces);
        chartElements.push(
          <div
            key={`gas2-${index}`}
            className="w-1/2"
            style={{ height: '350px' }}
          >
            <Plot
              data={traces}
              layout={getSingleGasLayout(gas)}
              style={{ width: '100%', height: '100%' }}
              config={{ displayModeBar: false }}
            />
          </div>
        );
      });
    }

    if (gases.length === 3) {
      chartElements.push(
        <div key="gas3-dual" className="w-1/2" style={{ height: '350px' }}>
          <Plot
            data={[
              ...getTracesForGas(gases[0], 'y'),
              ...getTracesForGas(gases[1], 'y2'),
            ]}
            layout={getDualGasLayout(gases[0], gases[1])}
            style={{ width: '100%', height: '100%' }}
            config={{ displayModeBar: false }}
          />
        </div>
      );

      chartElements.push(
        <div key="gas3-single" className="w-1/2" style={{ height: '350px' }}>
          <Plot
            data={getTracesForGas(gases[2])}
            layout={getSingleGasLayout(gases[2])}
            style={{ width: '100%', height: '100%' }}
            config={{ displayModeBar: false }}
          />
        </div>
      );
    }

    if (gases.length === 4) {
      for (let i = 0; i < 2; i++) {
        console.log([
          ...getTracesForGas(gases[i * 2], 'y'),
          ...getTracesForGas(gases[i * 2 + 1], 'y2'),
        ]);
        chartElements.push(
          <div key={`gas4-${i}`} className="w-1/2" style={{ height: '350px' }}>
            <Plot
              data={[
                ...getTracesForGas(gases[i * 2], 'y'),
                ...getTracesForGas(gases[i * 2 + 1], 'y2'),
              ]}
              layout={getDualGasLayout(gases[i * 2], gases[i * 2 + 1])}
              style={{ width: '100%', height: '100%' }}
              config={{ displayModeBar: false }}
            />
          </div>
        );
      }
    }

    return <div className="flex flex-wrap w-full">{chartElements}</div>;
  };

  if (gases?.length > 0) {
    return <div>{renderCharts()}</div>;
  } else {
    return (
      <div className="flex flex-col gap-6 w-full">
        <img src={ChartSkeletonAtmosphere} className="w-full" alt="skeleton" />
      </div>
    );
  }
};

export default CommonChartForReportForEnv;
