import { ApiClient } from '../apiClient';

export interface sectionData {
  id?: number;
  userId?: number;
  mineId?: number;
  sort_by?: string;
}

export const getLocationLiveSectionData = async (
  mineId: number,
  userId: number,
  sort_by?: string
) => {
  let endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/locations/live/sections/${mineId}/${userId}`;

   if (sort_by) {
    endpoint += `?sort_by=${encodeURIComponent(sort_by)}`;
  }

  return await ApiClient.get(endpoint);
};

export const getLocationReportSectionData = async (
  mineId: number,
  userId: number,
  date: string
) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/locations/reports/sections/${mineId}/${userId}/${date}`
  );
};

export const getLocationLiveCheckInsData = async (mineId: number) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/locations/live/checkins/${mineId}`
  );
};

export const getLocationReportCheckInsData = async (
  mineId: number,
  date: string
) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/locations/reports/checkins/${mineId}/${date}` // may be change in endpoint
  );
};
export const getLocationDashboardData = async (mine_id: number) => {
  return (
    await ApiClient.get(
      `/api/portal/v1/engineapi/analytics-service/api/v1/locations/live/dashboard/${mine_id}`
    )
  )?.data;
};

export const getPersonalReportData = async (
  mine_id: number,
  miner_id: number,
  from_date: string,
  to_date: string
) => {
  return await ApiClient.get(
    `/api/portal/v1/engineapi/analytics-service/api/v1/locations/reports/personnel/${mine_id}/${miner_id}/${from_date}/${to_date}`
  );
};
