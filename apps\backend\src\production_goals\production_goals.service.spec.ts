import { Test, TestingModule } from '@nestjs/testing';
import { ProductionGoalsService } from './production_goals.service';
import typeorm = require('typeorm');
import { getRepositoryToken } from '@nestjs/typeorm';
import { ProductionGoal } from './entities/production_goal.entity';
import { CreateProductionGoalDto } from './dto/create-production_goal.dto';
import { UpdateProductionGoalDto } from './dto/update-production_goal.dto';
describe('ProductionGoalsService', () => {
  let service: ProductionGoalsService;
  let productionGoalRepository: typeorm.Repository<ProductionGoal>;
  const GOAL_REPOSITORY_TOKEN = getRepositoryToken(ProductionGoal);

  const mockProductionGoalRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOneBy: jest.fn(),
    remove: jest.fn(),
  };

  const expectedProductionGoals = [
    {
      id: 1,
      sectionId: 1,
      shiftId: 1,
      goal: 100,
      effectiveDate: new Date().toISOString(),
      endDate: null,
      createdAt: '2024-02-14T05:46:09.713Z',
      createdBy: null,
      updatedAt: '2024-02-14T05:46:09.713Z',
      updatedBy: null,
    },
    {
      id: 2,
      sectionId: 1,
      shiftId: 2,
      goal: 100,
      effectiveDate: new Date().toISOString(),
      endDate: null,
      createdAt: '2024-02-14T05:46:28.793Z',
      createdBy: null,
      updatedAt: '2024-02-14T05:46:28.793Z',
      updatedBy: null,
    },
    {
      id: 3,
      sectionId: 1,
      shiftId: 3,
      goal: 100,
      effectiveDate: new Date().toISOString(),
      endDate: null,
      createdAt: '2024-02-14T05:46:52.020Z',
      createdBy: null,
      updatedAt: '2024-02-14T05:46:52.020Z',
      updatedBy: null,
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductionGoalsService,
        {
          provide: GOAL_REPOSITORY_TOKEN,
          useValue: mockProductionGoalRepository
        },
      ],
    }).compile();

    service = module.get<ProductionGoalsService>(ProductionGoalsService);
    productionGoalRepository = module.get<typeorm.Repository<ProductionGoal>>(
      GOAL_REPOSITORY_TOKEN
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  describe('fetchData', () => {
    it('should return "goal array"', async () => {
      const result = await service.findAll();
      expect(result).toEqual(expectedProductionGoals);
    });
  });

  it('create => Should create a new goal and return its data', async () => {
    // arrange
    let now = new Date().toISOString();
    const createProductionGoalDto = {
      sectionId: 1,
      shiftId: 1,
      goal: 100,
      effectiveDate: now,
      createdBy: 1,
    } as CreateProductionGoalDto;

    const goal = {
      sectionId: 1,
      shiftId: 1,
      goal: 100,
      effectiveDate: now,
      createdBy: 1,
    } as ProductionGoal;

    jest.spyOn(mockProductionGoalRepository, 'save').mockReturnValue(goal);

    // act
    const result = await service.create(createProductionGoalDto);

    // assert
    expect(mockProductionGoalRepository.save).toHaveBeenCalled();
    expect(mockProductionGoalRepository.save).toHaveBeenCalledWith(createProductionGoalDto);

    expect(result).toEqual(goal);
  });

  it('findAll => should return an array of goals', async () => {
    // arrange
    const goal = {
      id: 1,
      sectionId: 1,
      shiftId: 1,
      goal: 100,
      effectiveDate: new Date().toISOString(),
      createdBy: 1,
    }
    const goals = [goal];
    jest.spyOn(mockProductionGoalRepository, 'find').mockReturnValue(goals);

    // act
    const result = await service.findAll();

    // assert
    expect(mockProductionGoalRepository.find).toHaveBeenCalled();
    expect(result).toEqual(goals);
  });

  it('findOne => should find a goal by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const goal = {
      id: 1,
      sectionId: 1,
      shiftId: 1,
      goal: 100,
      effectiveDate: new Date().toISOString(),
      createdBy: 1,
    }

    jest.spyOn(mockProductionGoalRepository, 'findOneBy').mockReturnValue(goal);

    // act
    const result = await service.findOne(id);

    // assert
    expect(mockProductionGoalRepository.findOneBy).toHaveBeenCalled();
    expect(mockProductionGoalRepository.findOneBy).toHaveBeenCalledWith({id});
    expect(result).toEqual(goal);
  });

  it('update => should find a goal by a given id and update its data', async () => {
    // arrange
    let before = new Date().toISOString();
    let now = new Date().toISOString();
    const id = 1;
    const updateProductionGoalDto = {
      endDate: now,
    } as UpdateProductionGoalDto;

    const goal = {
      sectionId: 1,
      shiftId: 1,
      goal: 200,
      effectiveDate: before,
      endDate: now,
    };
    
    const updatedProductionGoal = Object.assign(goal, updateProductionGoalDto);
    jest.spyOn(mockProductionGoalRepository, 'save').mockReturnValue(updatedProductionGoal);

    // act
    const result = await service.update(id, goal);

    // assert
    expect(mockProductionGoalRepository.findOneBy).toHaveBeenCalled();
    expect(mockProductionGoalRepository.findOneBy).toHaveBeenCalledWith({id});
    expect(mockProductionGoalRepository.save).toHaveBeenCalled();
    expect(mockProductionGoalRepository.save).toHaveBeenCalledWith(updatedProductionGoal);
    expect(result).toEqual(updatedProductionGoal);
  });

  it('remove => should find a goal by a given id, remove and then return Number of affected rows', async () => {
    // arrange
    let before = new Date().toISOString();
    let now = new Date().toISOString();
    
    const id = 1;
    const goal = {
      sectionId: 1,
      shiftId: 1,
      goal: 100,
      effectiveDate: before,
      endDate: now,
    };

    jest.spyOn(mockProductionGoalRepository, 'remove').mockReturnValue(goal);

    // act
    const result = await service.delete(id);

    // assert
    expect(mockProductionGoalRepository.findOneBy).toHaveBeenCalled();
    expect(mockProductionGoalRepository.findOneBy).toHaveBeenCalledWith({id});
    expect(mockProductionGoalRepository.remove).toHaveBeenCalled();
    expect(mockProductionGoalRepository.remove).toHaveBeenCalledWith(id);
    expect(result).toEqual(goal);
  });
});
