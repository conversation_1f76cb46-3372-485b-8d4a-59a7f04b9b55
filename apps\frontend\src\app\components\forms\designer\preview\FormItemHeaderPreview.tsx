import { Item } from "../../types";

interface Props {
	item: Item;
}

interface Style {
	backgroundColor?: string,
	color?: string,
	margin?: string,
	marginTop?: string,
	marginBottom?: string,
	marginLeft?: string,
	marginRight?: string,
	padding?: string,
	paddingTop?: string,
	paddingBottom?: string,
	paddingLeft?: string,
	paddingRight?: string,
	width?: string,
	height?: string,
	fontSize?: string,
}

function FormItemLabelPreview({ item }: Props) {
	const properties = item.properties;
	const key = properties?.key ?? null;
	const typeValue = properties?.type ?? 'Input';
	const header = properties?.headergroup?.header ?? '';
	const headerposition = properties?.headergroup?.headerposition ?? null;
	const bgcolor = properties?.bgcolorgroup?.bgcolor ?? null;
	const textcolor = properties?.colorgroup?.color ?? null;
	const fUnitValueField = properties?.fontsizegroup?.fontsizeunitvaluefield ?? null;
	const fUnitTypeField = properties?.fontsizegroup?.fontsizeunittypefield ?? null;
	const mUnitValueField = properties?.margingroup?.unitvaluefield ?? null;
	const mUnitTypeField = properties?.margingroup?.unittypefield ?? null;
	const mtUnitValueField = properties?.margintop?.unitvaluefield ?? null;
	const mtUnitTypeField = properties?.margintop?.unittypefield ?? null;
	const mbUnitValueField = properties?.marginbottom?.unitvaluefield ?? null;
	const mbUnitTypeField = properties?.marginbottom?.unittypefield ?? null;
	const mlUnitValueField = properties?.marginleft?.unitvaluefield ?? null;
	const mlUnitTypeField = properties?.marginleft?.unittypefield ?? null;
	const mrUnitValueField = properties?.marginright?.unitvaluefield ?? null;
	const mrUnitTypeField = properties?.marginright?.unittypefield ?? null;
	const pUnitValueField = properties?.paddinggroup?.unitvaluefield ?? null;
	const pUnitTypeField = properties?.paddinggroup?.unittypefield ?? null;
	const ptUnitValueField = properties?.paddingtop?.unitvaluefield ?? null;
	const ptUnitTypeField = properties?.paddingtop?.unittypefield ?? null;
	const pbUnitValueField = properties?.paddingbottom?.unitvaluefield ?? null;
	const pbUnitTypeField = properties?.paddingbottom?.unittypefield ?? null;
	const plUnitValueField = properties?.paddingleft?.unitvaluefield ?? null;
	const plUnitTypeField = properties?.paddingleft?.unittypefield ?? null;
	const prUnitValueField = properties?.paddingright?.unitvaluefield ?? null;
	const prUnitTypeField = properties?.paddingright?.unittypefield ?? null;
	const wSizeValueField = properties?.widthgroup?.sizeunitvaluefield ?? null;
	const wSizeUnitTypeField = properties?.widthgroup?.widthunittypefield ?? null;
	const positionValue = properties?.positiongroup ?? 'left';
	
	let justifyValue = 'center';
	if(positionValue == 'left') { justifyValue = 'start' }
	if(positionValue == 'right') { justifyValue = 'end' }
	const style:Style = {};
	
	let margin, marginTop, marginBottom, marginLeft, marginRight;
	if(mUnitTypeField) {
		if(mUnitTypeField !== 'individual') {
			margin = `${mUnitValueField ?? ''}${mUnitTypeField ?? ''}`;
		}
		else {
			marginTop = `${mtUnitValueField ?? ''}${mtUnitTypeField ?? ''}`;
			marginBottom = `${mbUnitValueField ?? ''}${mbUnitTypeField ?? ''}`;
			marginLeft = `${mlUnitValueField ?? ''}${mlUnitTypeField ?? ''}`;
			marginRight = `${mrUnitValueField ?? ''}${mrUnitTypeField ?? ''}`;
		}
	}

	let padding, paddingTop, paddingBottom, paddingLeft, paddingRight;
	if(pUnitTypeField) {
		if(pUnitTypeField !== 'individual') {
			padding = `${pUnitValueField ?? ''}${pUnitTypeField ?? ''}`;
		}
		else {
			paddingTop = `${ptUnitValueField ?? ''}${ptUnitTypeField ?? ''}`;
			paddingBottom = `${pbUnitValueField ?? ''}${pbUnitTypeField ?? ''}`;
			paddingLeft = `${plUnitValueField ?? ''}${plUnitTypeField ?? ''}`;
			paddingRight = `${prUnitValueField ?? ''}${prUnitTypeField ?? ''}`;
		}
	}
	
	let width, widthClass;
	if(wSizeUnitTypeField) {
		let type = wSizeUnitTypeField ?? 'w-fit';
		let value = wSizeValueField ?? 0;
		if(type == 'px' || type == '%') {
			width = `${value}${type}`;
		}
		else {
			widthClass = type;
		}
	}
	
	let fontSize, fontSizeClass;
	if(fUnitTypeField) {
		let type = fUnitTypeField ?? 'base';
		let value = fUnitValueField ?? 1;
		if(type == 'px' || type == 'rem') {
			fontSize = `${value}${type}`;
		}
		else {
			fontSizeClass = type;
		}
	}
	
	const backgroundColor = `${bgcolor ?? ''}`;
	const color = `${textcolor ?? ''}`;

	if(fontSize && fontSize.length > 0) {style.fontSize = fontSize;}
	if(backgroundColor && backgroundColor.length > 0) {style.backgroundColor = backgroundColor;}
	if(color && color.length > 0) {style.color = color;}
	if(margin && margin.length > 0) {style.margin = margin;}
	if(marginTop && marginTop.length > 0) {style.marginTop = marginTop;}
	if(marginBottom && marginBottom.length > 0) {style.marginBottom = marginBottom;}
	if(marginLeft && marginLeft.length > 0) {style.marginLeft = marginLeft;}
	if(marginRight && marginRight.length > 0) {style.marginRight = marginRight;}
	if(padding && padding.length > 0) {style.padding = padding;}
	if(paddingTop && paddingTop.length > 0) {style.paddingTop = paddingTop;}
	if(paddingBottom && paddingBottom.length > 0) {style.paddingBottom = paddingBottom;}
	if(paddingLeft && paddingLeft.length > 0) {style.paddingLeft = paddingLeft;}
	if(paddingRight && paddingRight.length > 0) {style.paddingRight = paddingRight;}
	if(width && width.length > 0) {style.width = width;}

	return (
		<div
			style={style}
			data-refkey={key ?? ''}
			className={`
				p-2 flex items-top flex-wrap h-fit
				${widthClass ?? ''}
				text-${fontSizeClass ?? ''}
				justify-${justifyValue ?? 'center'}
				text-${positionValue ?? 'center'}
			`}
		>
			{headerposition == 'Header' && typeValue != 'Header' && typeValue != 'Label' && (
				<label className={`w-full block`}>{header}</label>
			)}
		</div>
	)
}

export default FormItemLabelPreview;