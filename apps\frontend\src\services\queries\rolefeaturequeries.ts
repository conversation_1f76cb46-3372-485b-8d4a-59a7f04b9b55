import { getAllRoles } from '../../api/roles/rolesapis';
import { getAllFeatures } from '../../api/features/featuresapis';
import { getAllRoleFeatures } from '../../api/rolefeatures/rolefeaturesapis';
import { useQuery } from '@tanstack/react-query';

export function useRoleFeatureData() {
  const { data, isLoading } = useQuery({
    queryKey: ['rolefeatures'],
    queryFn: () => getAllRoleFeatures(),
    refetchOnWindowFocus: false,
  });
  return data;
}

export function useFeatureData() {
  const { data, isLoading } = useQuery({
    queryKey: ['featuresList'],
    queryFn: () => getAllFeatures(),
    refetchOnWindowFocus: false,
  });
  return data;
}

export function useRoles() {
  const { data, isLoading } = useQuery({
    queryKey: ['roles'],
    queryFn: getAllRoles,
    refetchOnWindowFocus: false,
  });
  return { data, isLoading };
}
