import { Test, TestingModule } from '@nestjs/testing';
import { RoleFeatureAccessService } from './role_feature_access.service';
import { CreateRoleFeatureAccessDto } from './dto/create-role-feature-access.dto';
import { RoleFeatureAccess } from './entities/role-feature-access.entity';

describe('RoleFeatureAccessService', () => {
  let service: RoleFeatureAccessService;
  
  const mockRoleFeatureAccessService = {
    create: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [{
          provide: RoleFeatureAccessService,
          useValue: mockRoleFeatureAccessService,
      }]
    }).compile();

    service = module.get<RoleFeatureAccessService>(RoleFeatureAccessService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('create => should create a new role feature access record', async () => {
    // arrange
    const createRoleFeatureAccessDto = {
      roleId: 1,
      featureId: 1,
      createdBy: 1
    } as CreateRoleFeatureAccessDto;

    const roleFeatureAccess = {
      roleId: 1,
      featureId: 1,
      createdBy: 1
    } as RoleFeatureAccess

    jest.spyOn(mockRoleFeatureAccessService, 'create').mockReturnValue(roleFeatureAccess);

    // act
    //const result = await controller.create(createRoleFeatureAccessDto);

    // assert
    expect(mockRoleFeatureAccessService.create).toHaveBeenCalled();
    expect(mockRoleFeatureAccessService.create).toHaveBeenCalledWith(createRoleFeatureAccessDto);
    //expect(result).toEqual(roleFeatureAccess);
  });
});
