import {
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { UsersService } from '../users/users.service';
import { ConfigService } from '@nestjs/config';
import { EmailService } from '../common/email/email.service';
import { MinesService } from '../mines/mines.service';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private usersService: UsersService,
    private emailService: EmailService,
    private mineService: MinesService,
    private readonly configService: ConfigService
  ) {}

  async validateUser(username: string, password: string): Promise<object> {
    const user = await this.usersService.findByUsername(username);

    if (user === null || user === undefined)
      throw new NotFoundException(`user ${username} not found`);
    const mineStatus = await this.usersService.findMineIsDeleted(username);
    const storedPassword = user?.password;
    const isMatch = await bcrypt.compare(password, storedPassword || ''); // Comparing stored password with request body password
    if (!isMatch) throw new UnauthorizedException();
    if (
      !user.isActive ||
      user.isDelete ||
      mineStatus.isMineDeleted ||
      !mineStatus.isActive
    )
      throw new ForbiddenException();
    const userFeatureDataandRole = await this.usersService.getUsersWithRole(
      user.id
    );

    if (userFeatureDataandRole?.length == 0) {
      throw new ForbiddenException('No Features');
    }
    let mine;
    if (userFeatureDataandRole[0]?.RoleName != 'superuser') {
      mine = await this.mineService.getMineByCompanyId(user.companyId);
    }

    let firstLandingModule =
      userFeatureDataandRole.length != 0
        ? userFeatureDataandRole[0]?.FeatureName == 'Dashboard'
          ? userFeatureDataandRole[1]?.FeatureName ?? 'Dashboard'
          : userFeatureDataandRole[0]?.FeatureName
        : 'Dashboard';

        if (firstLandingModule == 'Dashboard') {
      firstLandingModule = 'dashboard';
    } else if (firstLandingModule == 'Location') {
      firstLandingModule = 'Location/dashboard';
    } else if (firstLandingModule == 'Production') {
      firstLandingModule = 'Production/live/mine';
    } else if (
      firstLandingModule == 'Users' ||
      firstLandingModule == 'Features' ||
      firstLandingModule == 'Goals' ||
      firstLandingModule == 'Shifts'
    ) {
      firstLandingModule = `Setting/${firstLandingModule}`;
    }

    const token = {
      username: user.username,
      email: user.email,
      userId: user.id,
      companyid: user.companyId,
      firstname: user.firstName,
      lastname: user.lastName,
      role: userFeatureDataandRole[0]?.RoleName,
      mineid: mine ? mine.id : 0,
      mshaId: mine ? mine.code : 0,
      minename: mine ? mine?.name : '',
    };
    return {
      jwtToken: await this.jwtService.signAsync(token), // Creating JWT Token & returning it
      navgationModule: firstLandingModule ? firstLandingModule : '',
    };
  }

  async sendEmail(user: any) {
    console.log(user.email);
    const mail = {
      to: user.email,
      subject: 'Sample Email From IWT Platform',
      from: this.configService.get<string>('SEND_GRID_FROM_EMAIL'),
      html: `<b>Sample Email..!</b>`,
    };
    return this.emailService.sendEmail(mail);
  }

  async refreshToken(userId: number, mineId?: number): Promise<object> {
    const user = await this.usersService.findUserById(userId);
    if (user === null || user === undefined) throw new NotFoundException();
    if (!user.isActive || user.isDelete) throw new ForbiddenException();
    const userFeatureDataandRole = await this.usersService.getUsersWithRole(
      user.id
    );
    let mineDetails = null;
    let mine = null;

    if (mineId) {
      mineDetails = await this.mineService.getMine(mineId);

      if (!mineDetails) throw new NotFoundException('Mine details not found');
    } else {
      mine = await this.mineService.getMineByCompanyId(user.companyId);
    }

    const token = {
      username: user.username,
      email: user.email,
      userId: user.id,
      companyid: user.companyId,
      firstname: user.firstName,
      lastname: user.lastName,
      role: userFeatureDataandRole[0]?.RoleName,
      mineid: mineId ? mineId : mine.id,
      mineCompanyId: mineDetails?.companyId ? mineDetails.companyId : 0,
      mshaId: mineId ? mineDetails.code : mine.code,
      minename: mineId ? mineDetails.name : mine.name,
    };
    return {
      jwtToken: await this.jwtService.signAsync(token), // Creating JWT Token & returning it
    };
  }
}
