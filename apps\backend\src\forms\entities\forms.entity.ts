import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { FormTemplate } from '../../form_templates/entities/form_template.entity';
import { FormTemplateDefinition } from '../../form_template_definitions/entities/form_template_definitions.entity';

@Entity('forms')
export class Form {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'mine_id', type: 'int', nullable: false })
  mineId: number;

  @Column({ name: 'form_template_id', type: 'int', nullable: false })
  formTemplateId: number;

  @Column({ name: 'form_template_definition_id', type: 'int', nullable: false })
  formTemplateDefinitionId: number;

  @Column({ name: 'name', nullable: false })
  name: string;

  @Column({ type: 'varchar', length: 'max', name: 'content', nullable: false })
  content: string;

  @Column({ type: 'varchar', length: 'max', name: 'submission', nullable: true })
  submission: string;

  @Column({ name: 'is_submitted', default: false, nullable: false })
  isSubmitted: boolean;

  @Column({ name: 'submitted_at', nullable: true })
  submittedAt: Date;

  @Column({ name: 'submitted_by', nullable: true })
  submittedBy: number;

  @Column({ name: 'is_delete', default: false, nullable: false })
  isDelete: boolean;

  @Column({ name: 'is_processed', default: false, nullable: false })
  isProcessed: boolean;

  @CreateDateColumn({ name: 'created_at', nullable: true })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at', nullable: true })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @ManyToOne(() => FormTemplate)
  @JoinColumn({ name: 'form_template_id' })
  formTemplate: FormTemplate;

  @ManyToOne(() => FormTemplateDefinition)
  @JoinColumn({ name: 'form_template_definition_id' })
  formTemplateDefinition: FormTemplateDefinition;
}
