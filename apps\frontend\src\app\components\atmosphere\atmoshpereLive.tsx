import { useEffect, useState } from 'react';
import decodeJWT from '../../../utils/jwtdecoder';
import { useParams, Link, Outlet } from 'react-router-dom';
import ChildScreenForLiveAtomsphere from './commonLiveChildern';
import Table, { ColumnDef } from '../common/Table';
import { AlertTableCompliance } from '../../../../src/assets/icons/icons';
import { UserMenu } from '../common/UserMenu';
import dayjs from 'dayjs';

const AtmosphereLive = () => {
  const params = useParams();
  const [selectedMenu, setSelectedMenu] = useState('gas');
  const lastUpdatedDate = new Date();
  // Parse input datetime string
  const datetime = dayjs(lastUpdatedDate);

  // Format the date
  const formattedDate = ` Today ` + datetime.format('[@] hh:mm a');
  const tabNames = ['gas', 'Environmental', 'Ventilation', 'Notifications'];

  useEffect(() => {
    const lastSegment = params['*']?.split('/')?.pop();
    setSelectedMenu(lastSegment || '');
  }, [params['*']]);

  return (
    <>
      <div className="sticky w-full top-0 z-30  agBreakup2  bg-top px-10 2xl:px-16">
        <div className="grid grid-cols-2   pt-4 ">
          <div className="">
            <h6 className="font-bold text-white text-[32px]">Live</h6>
          </div>

          <div className="">
            <div className="flex justify-end items-center  relative">
              <h6 className="font-bold text-white text-[32px]">Atmosphere</h6>
              <span className="ml-4">
                <UserMenu />
              </span>
            </div>
            <div className="mb-2 text-right">
              <span className="text-[#ffb132] text-[12px]">Last Updated: </span>
              <span className="text-white text-[12px] mr-2">
                {formattedDate}
              </span>
            </div>
          </div>
        </div>
        <div>
          <div className={` flex border-b-[1px]  border-[#80c2fe]`}>
            <div
              className={`flex flex-column justify-start  text-white w-[35%] gap-20 cursor-pointer text-[14px] font-normal`}
            >
              {tabNames.map((tab, index) => (
                <Link
                  to={
                    decodeJWT()?.role == 'superuser'
                      ? `/app/Mines/${params?.mineId}/Atmosphere/live/${tab}`
                      : `/app/Atmosphere/live/${tab}`
                  }
                  key={tab}
                  onClick={() => setSelectedMenu(tab)}
                  className={`${selectedMenu == tab ? 'text-[#FFB132]' : ''} `}
                >
                  <span
                    className={`cursor-pointer ${
                      selectedMenu === tab
                        ? 'border-b-[5px] pb-[7px] border-[#FFB132]'
                        : ''
                    }`}
                  >
                    {tab.toUpperCase()}
                  </span>
                  {selectedMenu === tab ? <div className={`h-2.5`}></div> : ''}{' '}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      <Outlet context={[]} />
    </>
  );
};

export default AtmosphereLive;
