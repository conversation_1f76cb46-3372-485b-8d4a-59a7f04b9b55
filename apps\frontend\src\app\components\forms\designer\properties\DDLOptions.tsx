import { useEffect, useRef, useState } from 'react';
import { Id } from '../../types';
import { TrashIcon, PlusIcon } from '../../../../../assets/icons/icons';

interface Props {
	itemId: Id;
	label: string;
	answer: string;
	currentIndex: number;
	focusIndex: number;
	setFocusOptionByIndex: (index: number) => void;
	updateOption: (id:Id, value:string) => void;
	deleteOption: (id:Id) => void;
	addOption: (newValue: string) => void;
}

const DDLOptions = ({
	itemId,
	label,
	answer,
	currentIndex,
	focusIndex,
	setFocusOptionByIndex,
	updateOption,
	deleteOption,
	addOption
}: Props) => {
	const [currentAnswer, setCurrentAnswer] = useState(answer);
	const optionInputRef = useRef<HTMLInputElement>(null);
	
	useEffect(() => {
		setCurrentAnswer(answer);
	}, [answer]);
	
	useEffect(() => {
		if(focusIndex == currentIndex) {
			optionInputRef.current?.focus();
		}
	}, [currentIndex, focusIndex]);
	
	function answerChanged(e:any) {
		setCurrentAnswer(e.target.value);
	}

	function handleUpdateOption(e:any, focusChangeAction: string) {
		let newAnswer = e.target.value;
		if(newAnswer !== answer) {
			if(itemId == 'temp-id') {
				setCurrentAnswer('');
				(focusChangeAction === 'up' || focusChangeAction === 'none') && addOption(newAnswer);
			}
			else {
				updateOption(itemId, newAnswer);
			}
		}
		focusChangeAction === 'up' && setFocusOptionByIndex(currentIndex+1);
		focusChangeAction === 'down' && setFocusOptionByIndex(currentIndex-1);
	}
	
	return (
		<>
			<span className="text-white text-[16px]">{label}:</span>
			<input
				ref={optionInputRef}
				name={label} type="text" id={`${itemId}`}
				className="bg-gray-200 p-1.5 rounded pl-2 text-[14px] text-black col-span-6"
				placeholder={`Enter Option`}
				value={currentAnswer ?? ''}
				onKeyDown={(e) => {
					e.key === 'Escape' && setCurrentAnswer(currentAnswer);
					e.key === 'Enter' && handleUpdateOption(e, 'up');
					if(e.key === 'Tab') {
						e.preventDefault();
						let focusChangeAction = 'up';
						if(e.shiftKey) {
							focusChangeAction = 'down';
						}
						handleUpdateOption(e, focusChangeAction);
					}
				}}
				onChange={(e) => answerChanged(e)}
				onBlur={(e) => handleUpdateOption(e, 'none')}
				onFocus={() => setFocusOptionByIndex(currentIndex)}
			/>
			{itemId !== 'temp-id' &&
				<button
					tabIndex={-1}
					onClick={() => deleteOption(itemId)}
					className="cursor-pointer">
					<TrashIcon className="text-[14px] h-5 w-5" />
				</button>
			}
			{itemId === 'temp-id' &&
				<button
					tabIndex={-1}
					onClick={() => addOption('')}
					className="cursor-pointer">
					<PlusIcon className="text-white text-[14px] h-5 w-5" />
				</button>
			}
		</>
	);
};

export default DDLOptions;