import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuditService } from './audit.service';
import { Roles } from '../auth/gurads/roles.decorator';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { RolesGuard } from '../auth/gurads/roles.guard';

@Controller('portal/v1/auditlog')
@ApiTags('AuditLog')
export class AuditController {
  constructor(private AuditService: AuditService) {}

  @Get('/:startDate/:endDate')
  @Roles(['superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  GetAudits(
    @Param('startDate') startDate: string,
    @Param('endDate') endDate: string
  ) {
    return this.AuditService.findall(startDate, endDate);
  }
}
