import { ApiClient } from '../apiClient';

export interface GoalData {
  id?: number;
  shiftId?: number;
  sectionId?: number;
  goal?: number;
  effectiveDate?: string;
}

export const checkActiveGoal = async (sectionId: number, shiftId: number) => {
  return await ApiClient.get(`/api/portal/v1/goals/section/${sectionId}/shift/${shiftId}`);
};

export const addGoal = async (data: GoalData) => {
  return await ApiClient.post(`/api/portal/v1/goals`, data);
};

export const getGoals = async () => {
  return await ApiClient.get(`/api/portal/v1/goals`);
};

export const editGoal = async (id: number, data: GoalData) => {
  return await ApiClient.patch(`/api/portal/v1/goals/${id}`, data);
};

export const deleteGoal = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/goals/isDelete/${id}`);
};
