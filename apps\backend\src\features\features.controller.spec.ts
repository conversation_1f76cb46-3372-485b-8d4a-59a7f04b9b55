import { Test, TestingModule } from '@nestjs/testing';
import { FeaturesController } from './features.controller';
import { FeaturesService } from './features.service';
import { CreateFeatureDto } from './dto/create-feature.dto';
import { Feature } from './entities/feature.entity';
import { UpdateFeatureDto } from './dto/update-feature.dto';

describe('FeaturesController', () => {
  let controller: FeaturesController;
  
  const mockFeaturesService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FeaturesController],
      providers: [{
          provide: FeaturesService,
          useValue: mockFeaturesService,
      }]
    }).compile();

    controller = module.get<FeaturesController>(FeaturesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('create => should create a new feature by the given data', async () => {
    // arrange
    const createFeatureDto = {
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    } as CreateFeatureDto;

    const feature = {
      id: 1,
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    } as Feature;

    jest.spyOn(mockFeaturesService, 'create').mockReturnValue(feature);

    // act
    const result = await controller.create(createFeatureDto);

    // assert
    expect(mockFeaturesService.create).toHaveBeenCalled();
    expect(mockFeaturesService.create).toHaveBeenCalledWith(createFeatureDto);
    expect(result).toEqual(feature);
  });

  it('findAll => should return an array of features', async () => {
    // arrange
    const feature = {
      id: 1,
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    };

    const features = [feature];
    jest.spyOn(mockFeaturesService, 'findAll').mockReturnValue(features);

    // act
    const result = await controller.findAll();

    // assert
    expect(mockFeaturesService.findAll).toHaveBeenCalled();
    expect(result).toEqual(features);
  });

  it('findOne => should find a role by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const feature = {
      id: 1,
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    };

    jest.spyOn(mockFeaturesService, 'findOne').mockReturnValue(feature);

    // act
    const result = await controller.findOne(id);

    // assert
    expect(mockFeaturesService.findOne).toHaveBeenCalled();
    expect(mockFeaturesService.findOne).toHaveBeenCalledWith(+id);
    expect(result).toEqual(feature);
  });

  it('update => should find a feature by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateFeatureDto = {
      name: 'Test Feature 1',
      code: 'abc',
    } as UpdateFeatureDto;
    const feature = {
      id: 1,
      name: 'Test Feature 2',
      code: 'abc',
    };

    jest.spyOn(mockFeaturesService, 'update').mockReturnValue(feature);

    // act
    const result = await controller.update(id, updateFeatureDto);

    // assert
    expect(mockFeaturesService.update).toHaveBeenCalled();
    expect(mockFeaturesService.update).toHaveBeenCalledWith(+id, updateFeatureDto);
    expect(result).toEqual(feature);
  });

  it('remove => should find a feature by a given id, remove and then return number of affected rows', async () => {
    // arrange
    const id = 1;
    const feature = {
      id: 1,
      name: 'Test Feature 1',
      code: 'abc',
      createdBy: 1,
    };

    jest.spyOn(mockFeaturesService, 'delete').mockReturnValue(feature);

    // act
    const result = await controller.remove(id);

    // assert
    expect(mockFeaturesService.delete).toHaveBeenCalled();
    expect(mockFeaturesService.delete).toHaveBeenCalledWith(+id);
    expect(result).toEqual(feature);
  });
});
