import { useEffect, useState } from 'react';
import { EditIcon, TrashIcon } from '../../../assets/icons/icons';
import Table, { ColumnDef } from '../common/Table';
import AddGoalForm from './AddGoalForm';
import Modal from '../common/Modal';
import { useQuery } from '@tanstack/react-query';
import { getGoals } from '../../../api/goals/goalapis';
import { useDeleteGoal } from '../../../services/mutations/goalmutations';
import { toast } from 'react-toastify';
import decodeJWT from '../../../utils/jwtdecoder';
import { Tooltip } from '@material-tailwind/react';
import { escapeRegExp } from '../../../utils/constant';
import { getFeatures } from '../../../api/users/userapis';
import { getShifts } from '../../../api/shifts/shiftapis';
import { getSections } from '../../../api/sections/sectionapis';
import { useParams } from 'react-router-dom';
import Loader from '../common/Loader';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import { UserMenu } from '../common/UserMenu';

export default function Goals() {
  const [openAddGoalForm, setOpenAddGoalForm] = useState(false);
  const [editData, setEditData] = useState(null);
  const [goalData, setGoalData] = useState<any>();
  const [searchPh, setSearchPh] = useState('');
  const [openDeleteModal, setOpenDeteteModal] = useState(false);
  const { data, isLoading } = useQuery({
    queryKey: ['goals'],
    queryFn: getGoals,
    refetchOnWindowFocus: false,
  });
  const {
    data: features,
    isFetching: isFeatureFetching,
    isLoading: isFeatureLoading,
  } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });
  const params = useParams();
  const {
    data: getShiftRes,
    status: apiStatusforShift,
    isFetching: isShiftFetching,
    isLoading: isShiftLoading,
  } = useQuery({
    queryKey: ['shifts', openAddGoalForm, params?.mineId],
    queryFn: getShifts,
    refetchOnWindowFocus: false,
  });
  const {
    data: getSectionRes,
    status: apiStatusforSection,
    isFetching: isSectionFetching,
    isLoading: isSectionLoading,
  } = useQuery({
    queryKey: ['sections', openAddGoalForm, params?.mineId],
    queryFn: getSections,
    refetchOnWindowFocus: false,
  });

  const deleteGoal = useDeleteGoal();
  const url = getPageNamesFromUrl(params['*']);

  useEffect(() => {
    const handleOverflow = (cell: any) => {
      if (cell.offsetWidth < cell.scrollWidth) {
        cell.setAttribute('title', cell.innerText);
      } else {
        cell.removeAttribute('title');
      }
    };

    const observeDataChanges = () => {
      const tableCells = document?.querySelectorAll('.text-ellipsis');
      if (tableCells) {
        tableCells.forEach((cell) => {
          handleOverflow(cell);
        });
      }
    };

    observeDataChanges(); // Initial observation

    const observer = new MutationObserver(observeDataChanges);
    observer.observe(document.body, { subtree: true, childList: true });

    return () => {
      observer.disconnect();
    };
  }, [data?.data]);

  const regex = new RegExp(`(${escapeRegExp(searchPh)})`, 'i');
  const columns: ColumnDef[] = [
    {
      key: 'sectionName',
      label: 'Working Section',
      type: 'text',
      render: (row) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.sectionName.charAt(0).toUpperCase() + row.sectionName.slice(1)}
        </div>
      ),
    },
    {
      key: 'shiftName',
      label: 'Shift',
      type: 'text',
      render: (row) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.shiftName.charAt(0).toUpperCase() + row.shiftName.slice(1)}
        </div>
      ),
    },
    {
      key: 'goal',
      label: 'Production Goal (feet)',
      type: 'text',
      render: (row) => (
        <div className="text-ellipsis overflow-hidden w-35 text-start ">
          {row.goal}
        </div>
      ),
    },
    {
      key: 'action',
      label: 'Actions',
      type: 'element',
      render: (row) => (
        <div className="flex justify-center">
          <div
            className="cursor-pointer"
            data-tooltip-target="tooltip-default"
            title="Edit Goal"
            onClick={() => {
              setEditData(row);
              setOpenAddGoalForm(true);
              document
                ?.getElementsByClassName('scrollToTop')[0]
                ?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                });
            }}
          >
            <EditIcon className="text-white font-black h-5 w-5 mx-2 edit-icon" />
          </div>
          <div
            className="cursor-pointer"
            title="Delete Goal"
            onClick={() => {
              setOpenDeteteModal(true);
              setGoalData(row);
            }}
          >
            <TrashIcon className="text-white font-black text-[14px] h-5 w-5 mx-2 delete_icon" />
          </div>
        </div>
      ),
    },
  ];

  useEffect(() => {
    if (localStorage.getItem('refreshingPage')) {
      localStorage.removeItem('refreshingPage');
      setTimeout(() => {
        toast.info(
          `Your Role has been updated to ${
            decodeJWT()?.role.charAt(0).toUpperCase() +
            decodeJWT()?.role.slice(1)
          }`
        );
      }, 1000);
    }
  });
  if (
    features?.data.some((feature: any) => feature.FeatureName == 'Goals') ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <div className="w-full">
        <div className="sticky w-full top-0 z-30 box bg-top px-10 2xl:px-16">
          <div className="grid grid-cols-1 border-b-[1px] border-[#80c2fe] pb-4 pt-4">
            <div className="grid grid-cols-2">
              <div className="">
                <h6 className=" font-bold text-white text-[32px] text-left">
                  Goals
                </h6>
              </div>
              <div className="">
                {/* <h6 className="p-2 font-bold text-white text-[32px] text-right">
                  Settings
                </h6> */}
                <div className="flex justify-end items-center -mr-10 relative">
                  <h6 className=" font-bold text-white text-[32px]">
                    Settings
                  </h6>
                  <span className="ml-4">
                    <UserMenu />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-8 px-10 2xl:px-16">
          <div>
            {openAddGoalForm ? (
              <Modal
                Content={
                  <AddGoalForm
                    editData={editData}
                    setOpenAddGoalForm={setOpenAddGoalForm}
                    getShiftRes={getShiftRes}
                    getSectionRes={getSectionRes}
                  />
                }
                size="w-[554px]"
                backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
              ></Modal>
            ) : (
              ''
            )}
          </div>
          <div>
            {isLoading ? (
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            ) : (
              <div className="pt-8 pb-2 prodGoalsTableBg px-5 rounded-xl">
                <div className="flex justify-between">
                  <div className="block">
                    <h6 className="font-semibold text-[20px] text-white">
                      {` Production Goals (${
                        data?.data?.length ? data?.data?.length : 0
                      })`}
                    </h6>
                    <p className="font-normal my-1 text-[14px] tracking-normal leading-5 text-[#cccccc] ">
                      Manage existing production goals
                    </p>
                  </div>
                  <div>
                    <button
                      id="add_new_goal"
                      title="Click to add goal"
                      className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 disabled:bg-[#4AA8FE]/50 disabled:cursor-not-allowed font-medium rounded-lg text-sm px-10 py-2 text-center  items-center  me-2 mb-2"
                      onClick={() => {
                        setEditData(null);
                        setOpenAddGoalForm(true);
                      }}
                      disabled={
                        (getShiftRes?.data?.length == 0 &&
                          getSectionRes?.data?.length == 0) ||
                        apiStatusforShift != 'success' ||
                        apiStatusforSection != 'success'
                      }
                    >
                      Add New Goal
                    </button>
                  </div>
                </div>
                <div className="mt-5">
                  {data?.data.length > 0 ? (
                    <Table
                      columns={columns}
                      data={data?.data ?? []}
                      searchText={searchPh}
                      sortable={false}
                      searchOnColumn={'sectionName'}
                      separateLine={false}
                      scrollable={true}
                      dataRenderLimitMdScreen={8}
                      dataRenderLimitLgScreen={15}
                      tableHeightClassLg="tableheightForlg"
                      tableHeightClassMd="tableheightFormd"
                    />
                  ) : (
                    <h2 className="text-[26px] font-bold  text-center text-white">
                      There are currently no goals
                    </h2>
                  )}
                </div>

                {openDeleteModal ? (
                  <Modal
                    size="w-[454px]"
                    backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
                    Content={
                      <div className="p-4">
                        <div className="text-[24px] text-white text-center font-bold">
                          Delete Goal?
                        </div>
                        <div className="my-2 text-[16px] text-white text-center text-provima text-ellipsis overflow-hidden m-auto">
                          {`Are you sure you want to delete the goal of section '${goalData.sectionName}'?`}
                        </div>
                        <div className="mt-2 text-center">
                          <button
                            title="Click to Cancel"
                            onClick={() => setOpenDeteteModal(!openDeleteModal)}
                            className="w-[177px] my-2 text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 text-[16px] py-2 px-8 rounded focus:outline-none focus:shadow-outline mr-2"
                          >
                            Cancel
                          </button>
                          <button
                            title="Click to delete goal"
                            onClick={async (e: any) => {
                              e.preventDefault();
                              try {
                                const res = await deleteGoal.mutateAsync(
                                  goalData?.goalId
                                );
                                if (res?.status == 200 || res?.status == 201) {
                                  setOpenDeteteModal(!openDeleteModal);
                                  setGoalData({});
                                  mixpanel.track('Delete Goal', {
                                    Goal_Id: res?.data?.id,
                                    MshaId: decodeJWT()?.mshaId,
                                    Goal_Value: res?.data?.goal,
                                  });
                                  toast.success('Goal deleted successfully');
                                }
                              } catch (err: any) {
                                toast.error(err.message);
                                mixpanel.track('Error Event', {
                                  Page_Name: url,
                                  Action_Name: 'Delete Goal',
                                });
                              }
                            }}
                            className="w-[177px] my-2 text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 text-[16px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    }
                  />
                ) : (
                  <></>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } else {
    return '';
  }
}
