import { ApiClient } from '../apiClient';

export const getATMDashboard = async (mineId: any) => {
  let endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/dashboard/${mineId}`;
  return await ApiClient.get(endpoint);
};

export const getATMLiveNotification = async (mineId: any) => {
  let endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/live/notifications/${mineId}`;
  return await ApiClient.get(endpoint);
};

export const getATMReportNotification = async (
  mineId: any,
  startDate: any,
  endDate: any
) => {
  let endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/reports/notifications/${mineId}/${startDate}/${endDate}`;
  return await ApiClient.get(endpoint);
};

export const getLiveGasATMdata = async (mineId: any) => {
  let endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/live/gas/${mineId}`;
  return await ApiClient.get(endpoint);
};

export const getATMLiveEnvirnoment = async (mineId: any) => {
  let endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/live/environmental/${mineId}`;
  return await ApiClient.get(endpoint);
};

export const getATMLiveVentilation = async (mineId: any) => {
  let endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/live/ventilation/${mineId}`;
  return await ApiClient.get(endpoint);
};

export const getReportGasATMdata = async (mineId: any, startDate: any, endDate: any) => {
  const endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/reports/gas/${mineId}`;
  return await ApiClient.get(endpoint, {
    params: {
      start_date: startDate,
      end_date: endDate,
    },
  });
}

export const getReportGasGraphATMdata = async (
  mineId: number,
  startDate: string,
  endDate: string,
  deviceIds: number[]
) => {
  console.log("API Call → deviceIds:", deviceIds);
  const endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/reports/gasgraph/${mineId}`;
  return await ApiClient.get(endpoint, {
    params: {
      start_date: startDate,
      end_date: endDate,
      device_ids: deviceIds.join(','),
    },
  });
};

export const getReportEnvironmentATMdata = async (mineId: any, startDate: any, endDate: any) => {
  const endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/reports/environmental/${mineId}`;
  return await ApiClient.get(endpoint, {
    params: {
      start_date: startDate,
      end_date: endDate,
    },
  });
}

export const getReportEnvironmentGraphATMdata = async (
  mineId: number,
  startDate: string,
  endDate: string,
  deviceIds: number[]
) => {
  const endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/reports/environmentalgraph/${mineId}`;
  console.log("API Call → deviceIds:",startDate,endDate,deviceIds);
  return await ApiClient.get(endpoint, {
    params: {
      start_date: startDate,
      end_date: endDate,
      device_ids: deviceIds.join(','),
    },
  });
};
export const getReportVentilationATMdata = async (mineId: any, startDate: any, endDate: any) => {
  const endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/reports/ventilation/${mineId}`;
  return await ApiClient.get(endpoint, {
    params: {
      start_date: startDate,
      end_date: endDate,
    },
  });
}

export const getReportVentilationGraphATMdata = async (
  mineId: number,
  startDate: string,
  endDate: string,
  deviceIds: number[]
) => {
  console.log("API Call → deviceIds:", deviceIds);
  const endpoint = `/api/portal/v1/engineapi/analytics-service/api/v1/atmosphere/reports/ventilationgraph/${mineId}`;
  return await ApiClient.get(endpoint, {
    params: {
      start_date: startDate,
      end_date: endDate,
      device_ids: deviceIds.join(','),
    },
  });
};
