import { CloseIcon } from '../../../../assets/icons/icons';
import { FormInput, Group, Item } from '../types';
import { useQuery } from '@tanstack/react-query';
import { getPublishedRevision } from "../../../../api/forms/formtemplateapis";
import { useEffect, useMemo, useState } from 'react';
import FormItem from './FormItem';
import RequiredFieldFormItems from './RequiredFieldFormItems';
import { toast } from 'react-toastify';

interface Props {
  selectedTemplateId: number | null;
  setOpenNewFormRequiredFields: (value: boolean) => void;
  continueWithDefaults: (defaults: any) => void;
}

export default function RequiredFieldsForm({
  selectedTemplateId,
  setOpenNewFormRequiredFields,
  continueWithDefaults
}: Props) {
  const [currentFormData, setCurrentFormData] = useState<any>();
  const [isLoading, setIsLoading] = useState(false);

  const { status: templateStatus, data: template, isLoading: templateLoading } = useQuery({
    queryKey: ['form-template'],
    queryFn: () => getPublishedRevision(selectedTemplateId),
    refetchOnWindowFocus: false,
  });
  
  useEffect(() => {
    if(templateLoading) {
      setIsLoading(true);
    }
    else {
      setIsLoading(false);
    }
  }, [templateLoading]);

  useEffect(() => {
    if(template?.data && template?.data?.definition) {
      setCurrentFormData(JSON.parse(template?.data?.definition) ?? {});
    }
  }, [template?.data]);

  const groups = useMemo(() => {
    if(currentFormData && currentFormData?.content && currentFormData?.content?.groups) {
      return JSON.parse(JSON.stringify(currentFormData?.content?.groups)) ?? [];
    }
    return [];
  }, [currentFormData]);

  const items = useMemo(() => {
    return groups.map((grp: Group) => {
      return (
        grp.items.map((item: Item) => {
          return item;
        })
      );
    }).flat();
  }, [groups]);

  const prelimFields = useMemo(() => {
    return items.filter((i: Item) => i?.properties?.requiredgroup?.prelimfield);
  }, [items]);

  function saveInputValue(formInput: FormInput) {
    const activeItemIndex = prelimFields.findIndex((itm:Item) => itm.id === formInput.item.id);
    const item = prelimFields[activeItemIndex];
    
    let itemValue:any = formInput.value;
    if(item.properties && item.properties.type == 'DropDown') {
      itemValue = {
        itemId: formInput.value.itemId,
        answer: formInput.value.answer
      }
    }
    item.answer = itemValue;
  }

  const validationPassed = () => {
    let valid = true;
    for(let i = 0; i < prelimFields.length; i++) {
      let defaultField = prelimFields[i];
      let answer = defaultField?.answer?.answer ?? defaultField?.answer ?? null;
      
      if(!answer || answer.length < 1) {
        valid = false;
      }
    }
    return valid;
  };

  return (
    <div>
      <div className="">
        <div className="flex justify-between mx-4">
          <div className="block">
            <h6 className="font-semibold text-[24px] text-white">
              Required Fields
            </h6>
            <p className="font-normal my-1 text-[16px] tracking-normal leading-5 text-[#cccccc] ">
              Enter required fields
            </p>
          </div>
          <div
            className="mt-1 cursor-pointer"
            onClick={() => {
              setOpenNewFormRequiredFields(false);
            }}
          >
            <CloseIcon />
          </div>
        </div>
        <div className="">
          <div className="rounded pt-3">
            <div className="m-2">
              {prelimFields.map((item: Item) => (
                <RequiredFieldFormItems
                  key={item?.id}
                  item={item}
                  saveInputValue={saveInputValue}
                />
              ))}
            </div>
              <div className="grid grid-cols-2 mt-5">
                <div className="mx-3">
                  <button
                    id="cancel_button"
                    title="Click to cancel"
                    onClick={() => {
                      setOpenNewFormRequiredFields(false);
                    }}
                    className={`
                      text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75
                      font-medium rounded-lg text-sm px-4 py-2 text-center h-9 w-full items-center me-2 mb-2
                    `}
                  >
                    {'Cancel'}
                  </button>
                </div>
                <div className="mx-3">
                  <button
                    id="create_form_template"
                    title="Click to save required fields"
                    type="submit"
                    className={`
                      text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg
                      text-sm px-4 py-2 text-center h-9 w-full items-center me-2 mb-2
                    `}
                    onClick={() => {
                      if(validationPassed()) {
                        continueWithDefaults(prelimFields);
                      }
                      else {
                        toast.error('Please fill out missing fields.');
                      }
                    }}
                  >
                    Continue
                  </button>
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
  );
}
