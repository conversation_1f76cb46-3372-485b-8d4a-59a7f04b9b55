import Login from './components/login/Login';
import {
  Navigate,
  RouterProvider,
  createBrowserRouter,
} from 'react-router-dom';
import LandingPage from './LandingPage';
import ErrorPage from './components/errorPage/error-page';
import ForgotPassword from './components/login/ForgotPassword';
import decodeJWT from '../utils/jwtdecoder';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getFeatures } from '../api/users/userapis';

export function App() {
  const [routeItems, setRouteItem] = useState<any>([]);

  const { data: features, status: apiStatus } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    enabled: !!localStorage.getItem('token'),
    refetchOnWindowFocus: false,
  });

  let firstLandingModule =
    features?.data?.length != 0
      ? features?.data[0]?.FeatureName == 'Dashboard'
        ? features?.data[1]?.FeatureName
        : features?.data[0]?.FeatureName
      : 'Dashboard';

  if (firstLandingModule == 'Location') {
    firstLandingModule = 'Location/dashboard';
  } else if (firstLandingModule == 'Production') {
    firstLandingModule = 'Production/live/mine';
  } else if (
    firstLandingModule == 'Users' ||
    firstLandingModule == 'Features' ||
    firstLandingModule == 'Goals' ||
    firstLandingModule == 'Shifts'
  ) {
    firstLandingModule = `Setting/${firstLandingModule}`;
  }

  const router = createBrowserRouter([
    {
      path: '/',
      element: localStorage.getItem('token') ? (
        decodeJWT()?.role === 'superuser' ? (
          <Navigate to={'/app/Mines'} />
        ) : apiStatus == 'success' ? (
          <Navigate to={`/app/${firstLandingModule}`} />
        ) : (
          ''
        )
      ) : (
        <Navigate to={'/app'} />
      ),

      errorElement: <ErrorPage />,
    },
    {
      path: '/app',
      element: localStorage.getItem('token') ? (
        decodeJWT()?.role === 'superuser' ? (
          <Navigate to={'/app/Mines'} />
        ) : apiStatus == 'success' ? (
          <Navigate to={`/app/${firstLandingModule}`} />
        ) : (
          ''
        )
      ) : (
        <Login />
      ),
      errorElement: <ErrorPage />,
    },
    {
      path: '/app/forgotpassword',
      element: <ForgotPassword />,
      errorElement: <ErrorPage />,
    },
    {
      path: '/app/*',
      element: <LandingPage setRouteItem={setRouteItem} />,
      errorElement: <ErrorPage />,
      children: routeItems,
    },
  ],
  {
    future: {
      v7_relativeSplatPath: true, // Enables relative paths in nested routes
      v7_fetcherPersist: true,   // Retains fetcher state during navigation
      v7_normalizeFormMethod: true, // Normalizes form methods (e.g., POST or GET)
      v7_partialHydration: true, // Supports partial hydration for server-side rendering
      v7_skipActionErrorRevalidation: true, // Prevents revalidation when action errors occur
    },
  });
  return (
    <div className="box bg-cover bg-center">
      <RouterProvider
        future={{ v7_startTransition: true }} // Enables React's startTransition API
        router={router} />
    </div>
  );
}

export default App;
