import React, { useEffect, useRef, useState } from "react";
import SignatureCanvas from 'react-signature-canvas';

interface Props {
	readOnly: boolean;
	currentValue: string;
	saveSignatureField: (value: string) => void;
	style: any;
}

export default function Signature({readOnly, currentValue, saveSignatureField, style}: Props) {
	const sigCanvas = useRef<any>(null);

	useEffect(() => {
		sigCanvas.current?.clear();
		if(currentValue && currentValue.length > 0) {
			sigCanvas.current?.fromDataURL(`data:image/png;base64,${currentValue}`);
		}
	}, [currentValue]);

	useEffect(() => {
		if(readOnly) {
			sigCanvas.current?.off();
		}
		else {
			sigCanvas.current?.on();
		}
	}, [readOnly]);

	const handle = () => {
		sigCanvas.current?.clear();
		saveSignatureField('');
	}

	const handlePoints = () => {
		const signatureData = sigCanvas.current?.toDataURL().split(',')[1];
		saveSignatureField(signatureData);
	}

	return (
		<>
			<div style={style} className="relative min-h-[100px]">
				<SignatureCanvas
					ref={sigCanvas}
					onEnd={handlePoints}
					canvasProps={{className:'bg-gray-200 p-1.5 w-full h-full rounded text-black absolute'}}
				/>
			</div>
			<div className="w-full flex justify-center py-2">
				<button className="text-center text-lg opacity-80 hover:opacity-100 cursor-pointer" onClick={handle}>Clear</button>
			</div>
		</>
	);
}