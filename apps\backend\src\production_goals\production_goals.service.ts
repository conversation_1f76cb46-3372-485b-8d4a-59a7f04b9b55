import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductionGoal } from './entities/production_goal.entity';
import { Repository } from 'typeorm';
import { IsNull } from 'typeorm';
import { CreateProductionGoalDto } from './dto/create-production_goal.dto';
import { UpdateProductionGoalDto } from './dto/update-production_goal.dto';
import { AuditService } from '../audit/audit.service';
import { SectionsService } from '../sections/sections.service';
import { ShiftsService } from '../shifts/shifts.service';
import { MinesService } from '../mines/mines.service';

@Injectable()
export class ProductionGoalsService {
  constructor(
    private auditService: AuditService,
    private sectionsService: SectionsService,
    private shiftService: ShiftsService,
    private mineService: MinesService,
    @InjectRepository(ProductionGoal)
    private productionGoalRepository: Repository<ProductionGoal>
  ) {}

  async findOne(id: number) {
    return this.productionGoalRepository.findOneBy({ id });
  }

  async findAll(mineId: number) {
    const goalData = await this.productionGoalRepository
      .createQueryBuilder('production_goals')
      .select([
        'production_goals.id AS goalId',
        'section.id AS sectionId',
        'section.sectionName AS sectionName',
        'shift.id AS shiftId',
        'shift.shiftName AS shiftName',
        'production_goals.goal AS goal',
      ])
      .innerJoin(
        'sections',
        'section',
        'production_goals.sectionId = section.id'
      )
      .innerJoin('shifts', 'shift', 'production_goals.shiftId = shift.id')
      .where('production_goals.endDate is null and section.mine_id = :mineId', {
        mineId: mineId,
      })
      .orderBy('section.sectionName', 'ASC')
      .addOrderBy('shift.startTime', 'ASC')
      .getRawMany();

    return goalData;
  }

  async findActiveGoal(sectionId: number, shiftId: number) {
    const goalData = await this.productionGoalRepository.findOne({
      where: { sectionId, shiftId, endDate: IsNull() },
    });
    return goalData;
  }

  async create(createProductionGoalDto: CreateProductionGoalDto, user: any) {
    const now = new Date().toISOString().split('T')[0];
    const productionGoal = this.productionGoalRepository.create(
      createProductionGoalDto
    );
    Object.assign(productionGoal, { effectiveDate: now });
    const newGoal = await this.productionGoalRepository.save(productionGoal);

    await this.auidtLog(productionGoal, user, 'Production goal created');

    return newGoal;
  }

  async update(
    id: number,
    updateProductionGoalDto: UpdateProductionGoalDto,
    user: any
  ) {
    const productionGoal = await this.productionGoalRepository.findOneBy({
      id,
    });

    if (!productionGoal) {
      throw new NotFoundException('Production Goal not Found!');
    }

    const now = new Date().toISOString().split('T')[0];
    const yesterday = new Date(new Date().setDate(new Date().getDate() - 1))
      .toISOString()
      .split('T')[0];

    const goalDate = productionGoal?.effectiveDate
      ? new Date(productionGoal.effectiveDate).toISOString().split('T')[0]
      : now;
    const goalSpan = new Date(now).valueOf() - new Date(goalDate).valueOf();
    const goalSpanDays = goalSpan / 1000 / 60 / 60 / 24;

    if (goalSpanDays > 0) {
      Object.assign(productionGoal, { endDate: yesterday });
      await this.productionGoalRepository.save(productionGoal);

      const newGoal = this.productionGoalRepository.create(
        updateProductionGoalDto
      );
      Object.assign(newGoal, { effectiveDate: now });
      const goalcreate = await this.productionGoalRepository.save(newGoal);
      await this.auidtLog(productionGoal, user, 'Production goal updated');
      return goalcreate;
    } else {
      Object.assign(productionGoal, updateProductionGoalDto);
      const goalcreate = await this.productionGoalRepository.save(
        productionGoal
      );
      await this.auidtLog(productionGoal, user, 'Production goal updated');
      return goalcreate;
    }
  }

  async delete(id: number, user: any) {
    const existingRecord = await this.productionGoalRepository.findOneBy({
      id,
    });
    const now = new Date().toISOString().split('T')[0];
    const yesterday = new Date(new Date().setDate(new Date().getDate() - 1))
      .toISOString()
      .split('T')[0];

    if (!existingRecord) {
      throw new NotFoundException('Goal not Found!');
    }

    const goalDate = existingRecord?.effectiveDate
      ? new Date(existingRecord.effectiveDate).toISOString().split('T')[0]
      : now;
    const goalSpan = new Date(now).valueOf() - new Date(goalDate).valueOf();
    const goalSpanDays = goalSpan / 1000 / 60 / 60 / 24;

    await this.auidtLog(existingRecord, user, 'Production goal removed');
    if (goalSpanDays > 0) {
      Object.assign(existingRecord, { endDate: yesterday });
      return await this.productionGoalRepository.save(existingRecord);
    } else {
      return this.productionGoalRepository.remove(existingRecord);
    }
  }

  async auidtLog(productionGoal: any, user: any, action: string) {
    const section = await this.sectionsService.findOne(
      productionGoal.sectionId
    );
    const shift = await this.shiftService.findOne(productionGoal.shiftId);
    const mine = await this.mineService.findMineById(user.mineid);
    this.auditService.createAuditLog(
      user,
      mine.name,
      action,
      section.sectionName + ' / ' + shift.shiftName,
      'Section / Shift'
    );
  }
}
