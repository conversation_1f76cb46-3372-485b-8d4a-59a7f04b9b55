import Table, { ColumnDef } from '../../common/Table';
import Chart from '../../../../assets/Chart.png';

const ReportPersonnelWithoutMiner = () => {
  const tableData: any[] = [];
  const columns: ColumnDef[] = [
    {
      key: 'date',
      label: 'Date',
      type: 'text',
    },
    {
      key: 'dayOfWeek',
      label: 'Day Of Week',
      type: 'text',
    },
    {
      key: 'enterUG',
      label: 'Enter UG',
      type: 'text',
    },
    {
      key: 'leaveUG',
      label: 'Leave UG',
      type: 'text',
    },
    {
      key: 'totalTimeUG',
      label: 'Total Time UG',
      type: 'text',
    },
  ];

  return (
    <div className="w-full m-auto">
      <div className="">
        <img src={Chart} alt="" className="w-full h-[50vh]" />
      </div>

      <div className="border-t-[1px] border-[#4AA8FE] my-5  ml-auto"></div>
      <Table
        data={tableData}
        columns={columns}
        searchText={''}
        sortable={false}
        searchOnColumn={''}
        separateLine={true}
        scrollable={true}
        dataRenderLimitMdScreen={8}
        dataRenderLimitLgScreen={15}
        tableHeightClassLg="tableheightForlg"
        tableHeightClassMd="tableheightFormd"
      />
    </div>
  );
};

export default ReportPersonnelWithoutMiner;
