import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
} from 'typeorm';
import { ProductionGoal } from '../../production_goals/entities/production_goal.entity';

@Entity('shifts')
export class Shift {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'ext_shift_id' })
  extShiftId: number;

  @Column({ name: 'mine_id' })
  mineId: number;

  @Column({ name: 'shift_name' })
  shiftName: string;

  @Column({ name: 'start_time' })
  startTime: string;

  @Column({ name: 'end_time' })
  endTime: string;

  @Column({ name: 'is_active' })
  isActive: boolean;

  @OneToMany(() => ProductionGoal, (goal) => goal.shift)
  shifts: Shift[];

  @Column({name: 'shift_type'})
  shift_type: string;
}
