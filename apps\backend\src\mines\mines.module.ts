import { Module, forwardRef } from '@nestjs/common';
import { MinesController } from './mines.controller';
import { MinesService } from './mines.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Mine } from './entities/mine.entity';
import { AuditModule } from '../audit/audit.module';
import { Timezones } from '../timezones/entities/timezones.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Mine,Timezones]), AuditModule],
  controllers: [MinesController],
  providers: [MinesService],
  exports: [MinesService],
})
export class MinesModule {}
