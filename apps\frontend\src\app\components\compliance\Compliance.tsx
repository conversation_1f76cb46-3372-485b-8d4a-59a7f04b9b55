import { getUserById } from '../../../api/users/userapis';
import decodeJWT from '../../../utils/jwtdecoder';
import { useQuery } from '@tanstack/react-query';
import { getComplianceData } from '../../../api/compliance/complianceapis';
import Plot from 'react-plotly.js';
import Loader from '../common/Loader';
import { getFeatures } from '../../../api/users/userapis';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import { UserMenu } from '../common/UserMenu';

export default function Compliance() {
  const { data, isLoading } = useQuery({
    queryKey: ['user_by_id'],
    queryFn: () => getUserById(Number(decodeJWT()?.userId)),
    refetchOnWindowFocus: false,
  });
  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  const {
    data: complianceData,
    isLoading: boolean,
    isFetching,
  } = useQuery({
    queryKey: ['compliance-data', decodeJWT()?.mineid],
    queryFn: getComplianceData,
    refetchOnWindowFocus: false,
    retry: 0,
  });

  const regulationData =
    complianceData?.data.topRegulationsForMine
      .map((ele: any) => ({
        x: ele.issuedCount,
        y: ele.regulation,
      }))
      .filter((data: any) => data.y !== undefined) || [];
  const data1 = regulationData?.map((point: any) => point?.x);

  useEffect(() => {
    if (localStorage.getItem('refreshingPage')) {
      localStorage.removeItem('refreshingPage');
      setTimeout(() => {
        toast.info(
          `Your Role has been updated to ${
            decodeJWT()?.role.charAt(0).toUpperCase() +
            decodeJWT()?.role.slice(1)
          }`
        );
      }, 1000);
    }
  });

  if (
    features?.data.some(
      (feature: any) => feature.FeatureName == 'Compliance'
    ) ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <>
        <div className="sticky w-full top-0 z-30 px-10 xl:px-10 2xl:px-16 box  bg-top">
          <div className=" border-b-[1px] border-[#80c2fe]  pb-4 pt-4">
            <div className="grid grid-cols-2 w-full">
              <div className="flex justify-start items-center">
                <h6 className=" font-bold text-white text-[32px] ">
                  Dashboard
                </h6>
              </div>
              <div className="flex justify-end items-center">
                {/* <h6 className="p-2 font-bold text-white text-[32px] ">
                  Compliance
                </h6> */}
                <div className="flex justify-end items-center -mr-5 relative">
                  <h6 className=" font-bold text-white text-[32px]">
                    Compliance
                  </h6>
                  <span className="ml-4 ">
                    <UserMenu />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        {isLoading || isFetching ? (
          <div className="pt-[100px]">
            <div className="flex justify-center items-center h-full">
              {<Loader />}
            </div>
            <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
              Loading....
            </div>
          </div>
        ) : (
          <div className="h-1/2 pt-6 px-10 xl:px-10 2xl:px-16">
            <div className="flex  pb-1">
              <div className="w-[50%] pr-3">
                <div className="h-[92px]  border border-[#4AA8FE] rounded-md flex justify-center items-center">
                  <div className="flex flex-col">
                    <span className="text-[#FFD084] text-center text-base py-0">
                      Total MSHA Citations (Last 12 months)
                    </span>
                    <span className="text-[#FFFFFF] text-center text- font-bold text-[32px] py-0">
                      {complianceData?.data.numIssuances != null
                        ? complianceData?.data.numIssuances
                        : 0}
                    </span>
                  </div>
                </div>
              </div>
              <div className="w-[50%] ">
                <div className="h-[92px]  border border-[#4AA8FE] rounded-md flex justify-center items-center">
                  <div className="flex flex-col">
                    <span className="text-[#FFD084] text-center text-base py-0">
                      S&S Rate (Last 12 months)
                    </span>
                    <span className="text-[#FFFFFF] text-center text- font-bold text-[32px] py-0">
                      {complianceData?.data.rateOfSignificantIssuances != null
                        ? complianceData?.data.rateOfSignificantIssuances
                        : '0%'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex pb-5">
              <div className="w-[60%] pr-3 pt-3 ">
                <div className="userMTableBg pl-5 rounded-xl">
                  <p className="font-bold text-white text-2xl p-3 border-none">
                    Top 5 Regulations
                  </p>
                  <Plot
                    className="w-[80%]"
                    data={[
                      {
                        type: 'bar',
                        x: regulationData?.map((point: any) => point?.x),
                        orientation: 'h',
                        marker: {
                          color: 'rgba(255, 194, 93, 1)',
                        },

                        hovertemplate:
                          'Regulation: <b>%{y}</b>' +
                          '<br>Count: <b>%{x}</b> <extra></extra>',
                      },
                    ]}
                    layout={{
                      margin: { t: 0, r: 0, b: 50, l: 120, pad: 5 },
                      bargap: 0.4,

                      xaxis: {
                        color: 'white',
                        tickfont: { size: 10 },
                        title: {
                          text: 'Citation Count',
                          font: {
                            family: 'Inter, sans-serif',
                            size: 13,
                          },
                        },
                        linewidth: 1,
                        gridcolor: 'rgba(0, 0, 0, 0)',
                        fixedrange: true,
                      },
                      yaxis: {
                        color: 'white',
                        tickfont: { size: 10 },
                        tickvals: [0, 1, 2, 3, 4],
                        ticktext: regulationData?.map((point: any) => point?.y),
                        title: {
                          text: 'Regulations',
                          standoff: 100,
                          font: {
                            family: 'Inter, sans-serif',
                            size: 13,
                          },
                        },
                        fixedrange: true,
                      },
                      plot_bgcolor: 'rgba(0, 0, 0, 0)',
                      paper_bgcolor: 'rgba(0, 0, 0, 0)',
                      height: 240,

                      hoverlabel: {
                        bgcolor: 'rgba(26,37,47,1)',
                      },
                    }}
                    config={{
                      displayModeBar: false,
                      scrollZoom: false,
                      responsive: true,
                      dragmode: false,
                    }}
                  />
                </div>
              </div>
              <div className="w-[40%] ">
                <div className="pt-3">
                  <div className="h-[92px]  border border-[#4AA8FE] rounded-md flex justify-center items-center">
                    <div className="flex flex-col">
                      <span className="text-[#FFD084] text-center text-base py-0">
                        NFDL Injuries
                      </span>
                      <span className="text-[#FFFFFF] text-center text- font-bold text-[32px] py-0">
                        {complianceData?.data.nfdlCount != null
                          ? complianceData?.data.nfdlCount
                          : 0}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="pt-3">
                  <div className="h-[92px]  border border-[#4AA8FE] rounded-md flex justify-center items-center">
                    <div className="flex flex-col">
                      <span className="text-[#FFD084] text-center text-base py-0">
                        S&S Violation
                      </span>
                      <span className="text-[#FFFFFF] text-center text- font-bold text-[32px] py-0">
                        {complianceData?.data.numSignificantIssuances != null
                          ? complianceData?.data.numSignificantIssuances
                          : 0}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="pt-3">
                  <div className="h-[92px]  border border-[#4AA8FE] rounded-md flex justify-center items-center">
                    <div className="flex flex-col">
                      <span className="text-[#FFD084] text-center text-base py-0">
                        NFDL Rate
                      </span>
                      <span className="text-[#FFFFFF] text-center text- font-bold text-[32px] py-0">
                        {complianceData?.data.nfdlRate != null
                          ? complianceData?.data.nfdlRate
                          : '0%'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </>
    );
  } else {
    return '';
  }
}
