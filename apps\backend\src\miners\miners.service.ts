import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Miner } from './entities/miner.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class MinersService {
  constructor(
    @InjectRepository(Miner)
    private minerRepository: Repository<Miner>
  ) {}

  async getCompanyMiners(user) {
    const companyId = user.mineCompanyId ? user.mineCompanyId : user.companyid;
    const userId = user.userId;
    const queryBuilder = this.minerRepository
      .createQueryBuilder('miners')
      .select([
        'mine.id AS MineId',
        'miners.id AS MinerId',
        "CONCAT(miners.first_name,' ', miners.last_name) AS minerName",
        'CASE WHEN watchlistItem.miner_id IS NULL THEN 0 ELSE 1 END AS IsWatched',
        'miners.isActive AS isActive',
      ])
      .innerJoin('mines', 'mine', 'mine.id = miners.mine_id')
      .innerJoin('companies', 'company', 'company.id = mine.company_id')
      .leftJoin(
        'watchlist_items',
        'watchlistItem',
        'miners.id = watchlistItem.miner_id AND watchlistItem.user_id = :userId',
        { userId: userId }
      )
      .where('company.id = :companyId', { companyId: companyId })
      .andWhere('miners.isActive = :isActive', { isActive: true });
    const minerData = await queryBuilder.getRawMany();

    return minerData;
  }
}
