import { Module, forwardRef } from '@nestjs/common';
import { AuditService } from './audit.service';
import { AuditLog } from './entity/audit-log.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MinesModule } from '../mines/mines.module';
import { AuditController } from './audit.controller';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    TypeOrmModule.forFeature([AuditLog]),
  ],
  providers: [AuditService],
  exports: [AuditService],
  controllers: [AuditController],
})
export class AuditModule {}
