import { useEffect, useMemo, useState } from 'react';
import Table, { ColumnDef } from '../../common/Table';
import CommonChartForReport from './reportChartForGas';
import { CSVIcon } from '../../../../assets/icons/icons';
import CommonChartSync from './commonSyncChart';
import mixpanel from 'mixpanel-browser';
import decodeJWT from '../../../../utils/jwtdecoder';
import { getPageNamesFromUrl } from '../../PageName';
import { useParams } from 'react-router-dom';

const data = [
  {
    label: '7 Scoop South Charger',
    id: 'A091',
    gas1Max: '11.5ppm',
    gas1Avg: '0.7ppm',
    gas2Max: '8.8ppm',
    gas2Avg: '1.42ppm',
    alarms: '1',
    gases: ['CO', 'H2'],
    values: [
      [2, 5, 3, 6, 0, 9],
      [5, 2, 4, 6, 0, 1],
    ],
  },
  {
    label: '7Y S North XC-12',
    id: 'A092',
    gas1Max: '0.1ppm',
    gas1Avg: '0.1ppm',
    gas2Max: '-',
    gas2Avg: '-',
    alarms: '1',
    gases: ['CO'],
    values: [[5, 3, 4, 6, 2, 1]],
  },
  {
    label: '7Y 5 North XC-12',
    id: 'A093',
    gas1Max: '0.1ppm',
    gas1Avg: '0.1ppm',
    gas2Max: '-',
    gas2Avg: '-',
    alarms: '1',
    gases: ['CO'],
    values: [[2, 7, 3, 6, 3, 2]],
  },
  {
    label: 'Main West Belt XC-6',
    id: 'A094',
    gas1Max: '0.4ppm',
    gas1Avg: '0.2ppm',
    gas2Max: '-',
    gas2Avg: '-',
    alarms: '1',
    gases: ['CO'],
    values: [[8, 3, 4, 7, 0, 1]],
  },
  {
    label: '7Y 5 North XC-3',
    id: 'A095',
    gas1Max: '0.3ppm',
    gas1Avg: '0.1ppm',
    gas2Max: '8.8ppm',
    gas2Avg: '8.8ppm',
    alarms: '1',
    gases: ['CO', 'H2'],
    values: [
      [0, 1, 4, 8, 3, 1],
      [8, 3, 4, 7, 0, 1],
    ],
  },
  {
    label: '7 Scoop North Charger',
    id: 'A096',
    gas1Max: '0.1ppm',
    gas1Avg: '0.1ppm',
    gas2Max: '19.2ppm',
    gas2Avg: '2.1ppm',
    alarms: '1',
    gases: ['CO', 'H2'],
    values: [
      [1, 2, 7, 5, 0, 3],
      [8, 3, 4, 7, 0, 1],
    ],
  },
  {
    label: '7Y 3 North XC-22',
    id: 'A097',
    gas1Max: '0.3ppm',
    gas1Avg: '0.1ppm',
    gas2Max: '-',
    gas2Avg: '-',
    alarms: '1',
    gases: ['CO'],
    values: [[2, 5, 3, 6, 0, 9]],
  },
];

const ReportGasComp = () => {
  const params = useParams();
  const decoded = decodeJWT();
  const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
  const [sync, setSync] = useState(false);
  const selectedGases = data?.filter((ele: any) =>
    selectedRowIds?.includes(ele?.id)
  );

  const [showModal, setShowModal] = useState(false);
  const [fileName, setFileName] = useState('');
  const uniqueGases = useMemo(() => {
    return [...new Set(selectedGases.flatMap((item) => item.gases))];
  }, [selectedGases]);
  const [gasStates, setGasStates] = useState<Record<string, boolean>>({});
  const memoizedUniqueGases = useMemo(() => {
    return Array.from(new Set(uniqueGases));
  }, [uniqueGases]);
  useEffect(() => {
    setGasStates((prev) => {
      const updated = memoizedUniqueGases.reduce((acc, gas) => {
        acc[gas] = prev[gas] ?? true;
        return acc;
      }, {} as Record<string, boolean>);

      const prevKeys = Object.keys(prev);
      const updatedKeys = Object.keys(updated);

      const isSame =
        prevKeys.length === updatedKeys.length &&
        prevKeys.every((key) => updated[key] === prev[key]);

      return isSame ? prev : updated;
    });
  }, [memoizedUniqueGases]);

  const handleToggle = (gas: string) => {
    setGasStates((prev: any) => ({
      ...prev,
      [gas]: !prev[gas],
    }));
  };

  const handleCheckboxChange = (row: any) => {
    setSelectedRowIds((prev) => {
      const isSelected = prev.includes(row.id);
      const updated = isSelected
        ? prev.filter((id) => id !== row.id)
        : [...prev, row.id];

      mixpanel.track('Sensor Check', {
        // PageName: 'Atmosphere Gas Report',
        Page_Name: getPageNamesFromUrl(params['*'] ?? 'Atmosphere Gas Report'),

        MineName: decoded?.minename,
      });

      return updated;
    });
  };

  const columns: ColumnDef[] = [
    {
      key: 'status',
      label: `<div class="inline-flex items-center pt-1">
    <label
      class="relative flex items-center cursor-pointer"
      htmlFor="selectAll">
    <input type="checkbox" id="selectAll" class="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] checked:before:bg-gray-900 hover:before:opacity-10"/>
    <span class="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-3 w-3"
          viewBox="0 0 20 20"
          fill="#1A252F"
          stroke="#1A252F"
          strokeWidth="1"
        >
          <path
            fillRule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clipRule="evenodd"
          ></path>
        </svg>
      </span>
    </label>
  </div>`,
      type: 'sr_no',
      render: (row: any) => (
        <div className="inline-flex items-center pt-1">
          <label
            className="relative flex items-center cursor-pointer"
            htmlFor="check"
          >
            <input
              type="checkbox"
              className="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded border-2 border-gray-400 transition-all before:absolute before:block before:h-3 before:w-3 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-[#FFB132] checked:bg-[#FFB132] checked:before:bg-gray-900 hover:before:opacity-10"
              id="selectCheckbox"
              onChange={() => {
                handleCheckboxChange(row);
              }}
            />
            <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                viewBox="0 0 20 20"
                fill="#1A252F"
                stroke="#1A252F"
                strokeWidth="1"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </span>
          </label>
        </div>
      ),
    },
    { key: 'label', label: 'Label', type: 'text' },
    { key: 'id', label: 'ID', type: 'text' },
    {
      key: 'gas1Max',
      label: 'Gas 1 Max',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.gas1Max}</div>
          <div className="text-[12px] text-white/50 float-right">
            {row?.gas1Max != '-' ? 'CO' : ''}
          </div>
        </div>
      ),
    },
    {
      key: 'gas1Avg',
      label: 'Gas 1 Avg',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.gas1Avg}</div>
          <div className="text-[12px] text-white/50 float-right">
            {row?.gas1Avg != '-' ? 'CO' : ''}
          </div>
        </div>
      ),
    },
    {
      key: 'gas2Max',
      label: 'Gas 2 Max',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.gas2Max}</div>
          <div className="text-[12px] text-white/50 float-right">
            {row?.gas2Max != '-' ? 'H2' : ''}
          </div>
        </div>
      ),
    },
    {
      key: 'gas2Avg',
      label: 'Gas 2 Avg',
      type: 'text',
      render: (row) => (
        <div className=" w-max">
          <div className="text-[14px] font-medium">{row?.gas2Avg}</div>
          <div className="text-[12px] text-white/50 float-right">
            {' '}
            {row?.gas2Avg != '-' ? 'H2' : ''}
          </div>
        </div>
      ),
    },
    { key: 'alarms', label: 'Alarms', type: 'text' },
  ];

  const downloadCSV = () => {
    const csvRows = [];

    // Add headers
    const headers = [
      'Label',
      'ID',
      'Gas 1 Max',
      'Gas 1 Avg',
      'Gas 2 Max',
      'Gas 2 Avg',
      'Alarms',
    ];
    csvRows.push(headers.join(','));

    // Add data
    data.forEach((row) => {
      const values = [
        row.label,
        row.id,
        row.gas1Max,
        row.gas1Avg,
        row.gas2Max,
        row.gas2Avg,
        row.alarms,
      ];
      csvRows.push(values.join(','));
    });

    const blob = new Blob([csvRows.join('\n')], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };
  return (
    <div className="px-[4%]">
      <div className="text-white overflow-hidden">
        {sync &&
        Object.keys(gasStates).filter((key) => gasStates[key])?.length == 2 &&
        selectedRowIds?.length != 0 ? (
          <>
            <CommonChartSync
              data={{
                row:
                  selectedGases?.map((ele) => ({
                    label: ele?.label,
                    values: [...ele?.values],
                    gases: ele?.gases,
                  })) || [],
              }}
              gases={Object.keys(gasStates).filter((key) => gasStates[key])}
            />
          </>
        ) : (
          <CommonChartForReport
            data={{
              row: selectedGases.map((ele) => ({
                label: ele.label,
                values: [...ele.values],
                gases: ele.gases,
              })),
            }}
            gases={
              selectedRowIds?.length != 0
                ? Object.keys(gasStates).filter((key) => gasStates[key])
                : []
            }
          />
        )}
      </div>
      <div className="pt-8 pb-2 userMTableBg px-5 rounded-xl">
        {' '}
        <div className="flex justify-between items-start gap-4 mb-4">
          {/* Left section: Gas buttons and Sync */}
          <div className="flex gap-4 flex-wrap">
            {uniqueGases.map((gas) => (
              <button
                key={gas}
                className={`text-white px-4 py-2 rounded-lg text-sm font-medium  ${
                  gasStates[gas] ? 'bg-[#FFB132]' : ''
                }`}
                onClick={() => {
                  mixpanel.track('Report Filter Module', {
                    Module_Name: gas, // this is the new state after toggle
                    Page_Name: getPageNamesFromUrl(params['*'] ?? ''),
                  });
                  handleToggle(gas);
                }}
              >
                {gas}
              </button>
            ))}

            {uniqueGases.length > 1 && (
              <button
                onClick={() => {
                  mixpanel.track('Module Sync', {
                    Module_Name: 'sync',
                    Page_Name: getPageNamesFromUrl(params['*'] ?? ''),
                  });
                  setSync(!sync);
                }}
                className={`${
                  sync
                    ? 'bg-[#FFB132] border-none'
                    : 'border-[2px] border-dashed py-[6px] px-[30px]'
                } text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline`}
              >
                Sync
              </button>
            )}
          </div>

          {/* Right section: Export button */}
          <div
            className="flex items-center text-[16px] text-[#FFB132] gap-2 cursor-pointer"
            onClick={() => {
              mixpanel.track('CSV Export', {
                // PageName: 'Atmosphere Gas Report',
                Page_Name: getPageNamesFromUrl(
                  params['*'] ?? 'Atmosphere Gas Report'
                ),
              });

              setShowModal(true);
            }}
          >
            <CSVIcon /> Export to CSV
          </div>
        </div>
        <Table
          columns={columns}
          data={data}
          searchText={''}
          searchOnColumn=""
          backgroundColor={false}
          scrollable={true}
          sortable={true}
          dataRenderLimitMdScreen={4}
          dataRenderLimitLgScreen={5}
          tableHeightClassLg={`h-[240px]`}
          tableHeightClassMd={`h-[240px]`}
        />
      </div>
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[#0F0F0F] text-white p-4 rounded-xl w-[25%] shadow-xl">
            <h2 className="text-xl font-bold mb-4 text-center">
              Export to CSV
            </h2>

            <label className="block mb-2 font-semibold">File Name</label>
            <input
              type="text"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              className="w-full px-4 py-2 rounded-md text-black focus-none outline-none"
              placeholder="Enter file name"
            />

            <div className="flex justify-between mt-6 w-full">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 w-[48%] border border-blue-500 text-white rounded-md hover:bg-blue-500"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  downloadCSV();
                  setShowModal(false);
                }}
                className="px-4 py-2 w-[48%] bg-blue-500 hover:bg-blue-600 text-white rounded-md"
              >
                Export Data
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default ReportGasComp;
