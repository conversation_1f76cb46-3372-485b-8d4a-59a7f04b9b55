import { getGreeting } from '../support/app.po';

describe('frontend-e2e', () => {
  beforeEach(() => {
    cy.on('uncaught:exception', (e) => {
      if (e.message.includes('EOF is not defined')) {
        return false;
      }
    });

    cy.visit('http://localhost:4200/app');
  });

  it('should visit given url', () => {
    cy.visit('http://localhost:4200/app');
  });

  it('should successfully login with valid credentials', () => {
    cy.fixture('AdminLoginData').then((AdminLoginData) => {
      const data = AdminLoginData.users[0];
      cy.get('#username').type(data.username);
      cy.get('#password').type(data.password);
      // cy.contains('Sign In').click();
      cy.get('#signin').click();
      cy.url().should('include', '/Dashboard');
    });  
  });
  it('should display error message for empty fileds', () => {
    cy.get('#signin').should('be.visible').click();

    cy.get('#usernameError').should('contain.text', 'Please enter username');
    cy.get('#passwordError').should('contain.text', 'Please enter password');
  });

  it('should display error message for invalid username', () => {
    cy.fixture('AdminLoginData').then((AdminLoginData) => {
      const data = AdminLoginData.users[1];
      cy.get('#username').should('be.visible').type(data.invaliUser);
      cy.get('#password').should('be.visible').type(data.validPass);
      cy.get('#signin').click();
    });
  });
  it('should display error message for invalid password', () => {
    cy.fixture('AdminLoginData').then((AdminLoginData) => {
      const data = AdminLoginData.users[2];

      cy.get('#username').should('be.visible').type(data.validUserName);
      cy.get('#password').should('be.visible').type(data.Invalidpass);

      cy.get('#signin').click();
    });
  });

  it('should redirect to forgot password page when clicked', () => {
    cy.fixture('AdminLoginData').then((AdminLoginData) => {
      const data = AdminLoginData.users[4];
      cy.get('#forgotPassword').click();
      cy.url().should('include', '/app/forgotpassword');
      cy.get('#emailToReset').type(data.validEmail);
      cy.get('body').click();
      cy.get('#emailToReset').should('have.value', data.validEmail);
      // cy.get('#resetPass').click();
    });
  });

  it('should display error for unregistered email', () => {
    cy.fixture('AdminLoginData').then((AdminLoginData) => {
      const data = AdminLoginData.users[6];
      cy.get('#forgotPassword').click();
      cy.url().should('include', '/app/forgotpassword');
      cy.get('#emailToReset').type(data.invalidEmail);
      cy.get('body').click();
      cy.get('#emailToReset').should('have.value', data.invalidEmail);
      cy.get('#resetPass').click();
    });
  });
});
