import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  GoalData,
  checkActiveGoal,
  addGoal,
  editGoal,
  deleteGoal,
} from '../../api/goals/goalapis';

export function useAddGoal() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: GoalData) => addGoal(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-goal'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['goals'], exact: true });
    },
  });
}

export function useFindActiveGoal() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) => checkActiveGoal(data?.sectionId, data?.shiftId),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['check-section-shift'] });
      }
    },
  });
}

export function useEditGoal() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: GoalData) => {
      const id: any = data?.id;
      delete data.id;
      return editGoal(id, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['edit-goal'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['goals'], exact: true });
    },
  });
}

export function useDeleteGoal() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteGoal(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['delete-goal'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['goals'], exact: true });
    },
  });
}