import { Module } from '@nestjs/common';
import { RoleFeatureAccessService } from './role_feature_access.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleFeatureAccess } from './entities/role-feature-access.entity';
import { RoleFeatureAccessController } from './role_feature_access.controller';
import { AuditModule } from '../audit/audit.module';
import { MinesModule } from '../mines/mines.module';
import { RolesModule } from '../roles/roles.module';

@Module({
  providers: [RoleFeatureAccessService],
  imports: [
    TypeOrmModule.forFeature([RoleFeatureAccess]),
    AuditModule,
    RolesModule,
    MinesModule,
  ],
  exports: [RoleFeatureAccessService],
  controllers: [RoleFeatureAccessController],
})
export class RoleFeatureAccessModule {}
