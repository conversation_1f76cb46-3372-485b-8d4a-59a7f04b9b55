import { useCallback, useEffect, useState } from 'react';
import {
  ActiveTabIcon,
  ProdActiveTabIcon,
} from '../../../../assets/icons/icons';
import Table, { ColumnDef } from '../../common/Table';
import { useQuery } from '@tanstack/react-query';
import { getReportSectionShiftData } from '../../../../api/production/productionReportapi';
import decodeJWT from '../../../../utils/jwtdecoder';
import { IwtEnv } from '../../../../api/apiClient';
import Plot from 'react-plotly.js';
import {
  useOutletContext,
  useParams,
  useNavigate,
  Link,
} from 'react-router-dom';
import GraphSkeleton from '../../common/GraphSkeleton';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../../PageName';
declare var iwtEnv: IwtEnv;

const defaultState = [
  {
    heading: 'Mined',
    value: '0',
    unit: 'ft',
  },
  {
    heading: 'Feet Mined/Hour',
    value: '0',
    unit: 'fmph',
  },
  {
    heading: 'Belt Availability',
    value: '0',
    unit: '%',
  },
  {
    heading: 'Total Downtime',
    value: '0',
    unit: 'mins',
  },
  {
    heading: 'Last Downtime Started',
    value: '00:00',
    unit: 'pm',
  },
];

export default function ProdReportSection() {
  const [selectedSection, setSelectedSection] = useState<number>();
  const [selectedShift, setSelectedShift] = useState<number>();
  const [sectionShiftData, setSectionShiftData] = useState<any>();
  const [sectionsList, setSectionsList] = useState<any>();
  const [shiftsList, setShiftList] = useState<any>();
  const [currentSectionShiftData, setCurrentSectionShiftData] = useState<any>();
  const [reportSectionData, setReportSectionData] = useState(defaultState);
  const [goalLinedata, setGoalLineData] = useState<any>();
  const params = useParams();
  const url = getPageNamesFromUrl(params['*']);
  const decode = decodeJWT();

  const [searchText, setLastUpdatedDate, filterData] = useOutletContext() as [
    string,
    any,
    any
  ];

  const {
    data: reportSectionShiftData,
    refetch: refetchReportSectionShiftData,
    status: apiStatus,
  } = useQuery({
    queryKey: [
      'UpdatedReportSectionShiftData',
      Number(decodeJWT()?.mineid),
      filterData.startDate,
    ],
    queryFn: () =>
      getReportSectionShiftData(
        Number(decodeJWT()?.mineid),
        filterData.startDate
      ),
    refetchOnWindowFocus: false,
    enabled: filterData.startDate !== null,
  });

  const { sectionId }: any = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    if (reportSectionShiftData) {
      setSectionShiftData(reportSectionShiftData?.data);
      setSectionsList(reportSectionShiftData?.data?.sections);
      setShiftList(reportSectionShiftData?.data?.shifts);
    }
  }, [reportSectionShiftData]);

  const savedSectionId: any = sessionStorage.getItem('prodReportSection');

  const savedShiftId: any = sessionStorage.getItem('prodReportShift');

  useEffect(() => {
    if (sectionId) {
      setSelectedSection(sectionId);
      sessionStorage.setItem('prodReportSection', sectionId);
    } else if (savedSectionId) {
      setSelectedSection(savedSectionId);
    } else {
      setSelectedSection(
        sectionId ?? reportSectionShiftData?.data?.sections[0]?.sectionId
      );
    }
  }, [reportSectionShiftData?.data]);

  useEffect(() => {
    if (savedShiftId) {
      setSelectedShift(savedShiftId);
    } else {
      setSelectedShift(reportSectionShiftData?.data?.shifts[0]?.shiftId);
    }
  }, [reportSectionShiftData?.data?.shifts[0]?.shiftId]);

  useEffect(() => {
    const newPath = params?.mineId
      ? `/app/Mines/${params?.mineId}/Production/report/sections`
      : '/app/Production/report/sections';
    navigate(newPath, { replace: true });

    setCurrentSectionShiftData(
      reportSectionShiftData?.data?.shiftsMetrics.filter(
        (x: any) => x.sectionId == selectedSection && x.shiftId == selectedShift
      )
    );
  }, [selectedSection, selectedShift, filterData.startDate]);

  useEffect(() => {
    setCurrentSectionShiftData(
      reportSectionShiftData?.data?.shiftsMetrics.filter(
        (x: any) => x.sectionId == selectedSection && x.shiftId == selectedShift
      )
    );
  }, [reportSectionShiftData?.data, params]);

  useEffect(() => {
    if (currentSectionShiftData && currentSectionShiftData?.length > 0) {
      setReportSectionData([
        {
          heading: 'Mined',
          value: currentSectionShiftData[0]?.mined
            ? currentSectionShiftData[0]?.mined
            : '0',
          unit: currentSectionShiftData[0]?.mined ? 'ft' : 'ft',
        },
        {
          heading: 'Feet Mined/Hour',
          value: currentSectionShiftData[0]?.feetMinedPerHour
            ? currentSectionShiftData[0]?.feetMinedPerHour
            : '0',
          unit: currentSectionShiftData[0]?.feetMinedPerHour ? 'fmph' : 'fmph',
        },
        {
          heading: 'Belt Availability',
          value: '0',
          unit: '%',
        },
        {
          heading: 'Total Downtime',
          value: currentSectionShiftData[0]?.totalDowntime
            ? currentSectionShiftData[0]?.totalDowntime
            : '0',
          unit:
            currentSectionShiftData[0]?.totalDowntime === null
              ? 'mins'
              : 'mins',
        },
        {
          heading: 'Last Downtime Started',
          value: '00:00',
          unit: 'pm',
        },
      ]);
      setGoalLineData(
        Array(currentSectionShiftData[0]?.shiftHours.length).fill(
          currentSectionShiftData[0]?.goal
        )
      );
    } else {
      setGoalLineData([]);
      setReportSectionData(defaultState);
    }
  }, [currentSectionShiftData]);

  const columns: ColumnDef[] = [
    {
      key: 'type',
      label: 'Type',
      type: 'text',
    },
    {
      key: 'timeLogged',
      label: 'Time Logged',
      type: 'text',
    },
    {
      key: 'description',
      label: 'Description',
      type: 'text',
    },
    {
      key: 'productionLost',
      label: 'Production Lost',
      type: 'text',
    },
    {
      key: '',
      label: '',
      type: 'element',
      render: (row: any) => (
        <>
          <button className="bg-transparent  text-white-300 font-semibold  py-2 px-4 border-[1px] border-[#4AA8FE]  rounded-lg mr-10">
            Clear
          </button>
        </>
      ),
    },
  ];

  const assetsData = ['CM12', 'SC2', 'SC3', 'RB4'];

  const alertsData = [
    {
      type: 'Planned Downtime',
      timeLogged: '1:07pm',
      description: 'CM12 down',
      productionLost: '-42 feet',
    },
    {
      type: 'Planned Downtime',
      timeLogged: '1:07pm',
      description: 'CM12 down',
      productionLost: '-42 feet',
    },
    {
      type: 'Planned Downtime',
      timeLogged: '1:07pm',
      description: 'CM12 down',
      productionLost: '-42 feet',
    },
    {
      type: 'Planned Downtime',
      timeLogged: '1:07pm',
      description: 'CM12 down',
      productionLost: '-42 feet',
    },
  ];

  const data = reportSectionData;
  let sectionGoal = 0;
  if(currentSectionShiftData && currentSectionShiftData.length > 0) {
    sectionGoal = currentSectionShiftData
      ? Math?.max(
          currentSectionShiftData[0]?.goal,
          currentSectionShiftData[0]?.shiftGraphData.map((d: any) => d.feetMined)[
            currentSectionShiftData[0]?.shiftGraphData?.length - 1
          ]
            ? currentSectionShiftData[0]?.shiftGraphData.map(
                (d: any) => d.feetMined
              )[currentSectionShiftData[0]?.shiftGraphData?.length - 1]
            : 0
        )
      : 0;
  }

  function convertHoursToDates(hours: any[]) {
    if (!Array.isArray(hours)) {
      throw new TypeError('The "hours" parameter should be an array.');
    }

    const now = new Date();
    const currentDate = now.toISOString().split('T')[0];
    const yesterdayDate = new Date(now);
    yesterdayDate.setUTCDate(now.getUTCDate() - 1);
    const yesterdayDateStr = yesterdayDate.toISOString().split('T')[0];

    return hours?.map((hour) => {
      let dateStr;

      if (hour >= 22) {
        dateStr = yesterdayDateStr;
      } else {
        dateStr = currentDate;
      }

      const hourIn24Format = hour % 24;

      const dateString = `${dateStr}T${String(hourIn24Format).padStart(
        2,
        '0'
      )}:00:00Z`;

      const dateObject = new Date(dateString);

      const currentDateObject = new Date();
      currentDateObject.setUTCFullYear(dateObject.getUTCFullYear());
      currentDateObject.setUTCMonth(dateObject.getUTCMonth());
      currentDateObject.setUTCDate(dateObject.getUTCDate());
      currentDateObject.setUTCHours(dateObject.getUTCHours());
      currentDateObject.setUTCMinutes(dateObject.getUTCMinutes());
      currentDateObject.setUTCSeconds(dateObject.getUTCSeconds());

      return currentDateObject;
    });
  }

  function rangeValue(dateRange: Date[]) {
    if (!Array.isArray(dateRange)) {
      throw new TypeError('The "dateRange" parameter should be an array.');
    }
    const firstDate = new Date(dateRange[0]);
    const lastDate = new Date(dateRange[dateRange.length - 1]);
    const lastDatePlusOneHour = new Date(lastDate.getTime() + 60 * 60 * 1000);

    let adjustedLastDatePlusOneHour;

    if (firstDate > lastDate) {
      adjustedLastDatePlusOneHour = new Date(lastDatePlusOneHour);
      adjustedLastDatePlusOneHour.setDate(lastDatePlusOneHour.getDate() + 1);
    } else {
      adjustedLastDatePlusOneHour = lastDatePlusOneHour;
    }

    return [firstDate, adjustedLastDatePlusOneHour];
  }

  let formattedDates: Date[] = [];

  if (
    currentSectionShiftData &&
    currentSectionShiftData.length > 0
  ) {
    const shiftHours = currentSectionShiftData[0]?.shiftHours;

    if (!Array.isArray(shiftHours)) {
      formattedDates = getDatesFromTimes([shiftHours]);
    } else {
      formattedDates = getDatesFromTimes(shiftHours);
    }
  }

  let finalRangeVal = rangeValue(formattedDates);
  let xaxisLabel = [];
  if(currentSectionShiftData && currentSectionShiftData.length > 0) {
    xaxisLabel =
      currentSectionShiftData?.length > 0
        ? currentSectionShiftData[0]?.shiftGraphData.map(
            (d: { hour: any }) => d.hour
          )
        : [];
  }

  function getDatesFromTimes(times: number[]): Date[] {
    const dates: Date[] = [];
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    const date = today.getDate();

    const tomorrow = new Date(year, month, date + 1);
    let currentDate = new Date(year, month, date);

    times.forEach((time, index) => {
      const hours = Math.floor(time);
      const minutes = Math.floor((time - hours) * 60);

      dates.push(
        new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          currentDate.getDate(),
          hours,
          minutes
        )
      );

      if (hours > Math.floor(times[index + 1])) {
        currentDate = tomorrow;
      }
    });
    return dates;
  }

  const newDates = getDatesFromTimes(xaxisLabel);

  const repSecLayout = {
    // barmode: 'stack',
    xaxis: {
      title: 'Hours',
      type: 'date',
      zeroline: false,
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      tickangle: 40,
      categoryorder: 'array',
      tickmode: 'array',
      tickvals: formattedDates
        ? formattedDates
        : [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      ticktext:
        currentSectionShiftData && currentSectionShiftData?.length > 0
          ? currentSectionShiftData[0]?.shiftHours.map(
              (x: any) => (x > 9 ? x : '0' + x) + ':00'
            )
          : [
              '00:00',
              '01:00',
              '02:00',
              '03:00',
              '04:00',
              '05:00',
              '06:00',
              '07:00',
              '08:00',
              '09:00',
              '10:00',
              '11:00',
              '12:00',
            ],
      range: finalRangeVal ? finalRangeVal : [0, 24],
      tickformat: '%H:%M',
    },
    yaxis: {
      title: { text: 'Feet Mined', standoff: 20 },
      type: 'linear',
      range: sectionGoal
        ? [-1, sectionGoal < 10 ? 11 : sectionGoal + sectionGoal / 2]
        : [-1, 11],
      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 50%)',
      zeroline: true,
      zerolinecolor: 'rgba(128, 194, 254, 1)',
      fixedrange: true,
    },
    showlegend: false,
    margin: { t: 0, l: 70, b: 70, r: 30, pad: 10 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
  };

  const repSecData = [
    {
      x:
        currentSectionShiftData && currentSectionShiftData?.length > 0
          ? currentSectionShiftData[0]?.shiftHours
          : '',
      y: goalLinedata,
      type: 'scatter',
      marker: {
        color: 'white',
      },
      mode: 'lines',
      line: {
        dash: 'dot',
      },
      hovertemplate: 'Goal: <b>%{y}</b>' + ' <extra></extra>',
    },

    {
      x: newDates,
      y:
        currentSectionShiftData && currentSectionShiftData?.length > 0
          ? currentSectionShiftData[0]?.shiftGraphData.map(
              (d: any) => d.feetMined
            )
          : '',
      type: 'scatter',
      marker: {
        color: 'rgba(255, 194, 94, 1)',
      },
      mode: 'lines',
      line: {
        color: 'rgba(255, 194, 94, 1)',
      },
      customdata: xaxisLabel,
      hovertemplate:
        'Feet Mined: <b>%{y}</b>' +
        '<br>Hour: <b>%{customdata}</b> <extra></extra>',
    },
  ];

  const selectSection =
    currentSectionShiftData && currentSectionShiftData?.length > 0
      ? currentSectionShiftData[0]?.shiftGraphData?.length == 0
      : true;

  function getSectionNameFromId(sectionId: number) {
    return reportSectionShiftData?.data?.sections.filter(
      (a: any) => a.sectionId == sectionId
    )[0]?.sectionName;
  }

  return (
    <div className="">
      <div className="bg-[#2c5c8bf6]">
        <div className="py-0 w-[93%] mx-auto">
          <div className="flex border-[#4AA8FE] border-b-[1px]">
            <div className="flex flex-row mt-3 W-[50%] overflow-x-scroll ">
              {sectionsList?.map((ele: any, index) => (
                <div key={index}>
                  {ele.sectionId == selectedSection ? (
                    <div className="text-[#FFD084] mx-3 text-[14px] cursor-pointer border-b-[6px]  border-[#FFB132]">
                      {ele.sectionName}{' '}
                      <div className="ml-[-8px] relative top-1 w-[30px]"></div>
                    </div>
                  ) : (
                    <div
                      className="text-white mx-3 text-[14px] cursor-pointer"
                      onClick={() => {
                        setSelectedSection(ele.sectionId);
                        sessionStorage.setItem(
                          'prodReportSection',
                          ele?.sectionId
                        );
                        mixpanel.track('Section Click', {
                          MshaId: decode?.mshaId,
                          Section_Name: ele.sectionName,
                          Page_Name: url,
                        });
                      }}
                    >
                      {ele.sectionName}
                    </div>
                  )}
                </div>
              ))}
            </div>
            <div className="h-auto border-l  border-[#4AA8FE] "></div>
            <div className="flex flex-row mt-3 W-[50%] overflow-x-scroll">
              {shiftsList?.map((ele: any, index) => (
                <div key={index}>
                  {ele.shiftId == selectedShift ? (
                    <div className="text-[#FFD084] mx-3 text-[14px] cursor-pointer border-b-[6px]  border-[#FFB132]">
                      {ele.shiftName}{' '}
                      <div className="aling-center">
                        <div className="ml-[-8px] relative top-1 w-[30px]">
                          {/* <ActiveTabIcon className="ml-3" /> */}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="text-white mx-3 text-[14px] cursor-pointer"
                      onClick={() => {
                        setSelectedShift(ele.shiftId);
                        sessionStorage.setItem('prodReportShift', ele?.shiftId);
                        mixpanel.track('Shift Click', {
                          MshaId: decode?.mshaId,
                          ShiftId: ele?.shiftId,
                          Shift_Name: ele.shiftName,
                        });
                      }}
                    >
                      {ele.shiftName}
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className={`flex-grow flex justify-end `}></div>
          </div>
        </div>
        <div className="grid grid-cols-5 gap-4 pt-6 w-[93%] mx-auto pb-6">
          {data?.map((ele: any, index) => (
            <div key={index}
              className={`h-[100px] border-[#4AA8FE] rounded-lg ${
                ele?.heading === 'Belt Availability' ||
                ele?.heading === 'Last Downtime Started'
                  ? 'border-dashed border-[1px] opacity-40'
                  : 'border-[1px]'
              }`}
            >
              <div
                className={`${
                  ele?.heading === 'Belt Availability' ||
                  ele?.heading === 'Last Downtime Started'
                    ? 'opacity-40'
                    : ''
                }`}
              >
                <div className="text-center text-[14px] text-[#FFD084] font-normal pt-3">
                  {ele?.heading}
                </div>
                <div className="text-center text-[32px] text-white font-bold pt-3">
                  {ele?.value ? ele.value : 0}
                  <span className="text-white text-[12px] mx-1">
                    {ele?.unit}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="flex px-[4%] pt-4 mb-6">
        <div className=" w-[60%]">
          {selectSection || apiStatus == 'pending' ? (
            <GraphSkeleton
              className={'2xl:!h-[24.4vh] xl:!h-[43.5vh] mt-6 mr-10'}
              tab="Rsection"
              apiStatus={apiStatus}
            />
          ) : (
            <Plot
              className="bg-transparent h-72 !w-[90%]"
              data={repSecData}
              // layout={repSecLayout}
              layout={{
                ...repSecLayout,
                legend: {
                  orientation: 'h',
                  x: 0.5,
                  xanchor: 'center',
                  y: 1.15,
                  font: { color: 'white' },
                },
              }}
              config={{ displayModeBar: false, responsive: true }}
            />
          )}
        </div>
        <div className="w-[40%]">
          <div className="grid grid-cols-2">
            <div className="pl-3">
              <Link
                to={
                  params?.mineId
                    ? selectedSection
                      ? `/app/Mines/${
                          params?.mineId
                        }/Location/report/sections/${
                          filterData.startDate
                        }/${getSectionNameFromId(selectedSection)}`
                      : '#'
                    : selectedSection
                    ? `/app/Location/report/sections/${
                        filterData.startDate
                      }/${getSectionNameFromId(selectedSection)}`
                    : '#'
                }
                onClick={() => {
                  if (getSectionNameFromId(selectedSection) !== '') {
                    mixpanel.track('On Section Click', {
                      Page_Name: getPageNamesFromUrl(params['*'] ?? ''),
                      Section_Name: getSectionNameFromId(selectedSection),
                    });
                  }
                }}
              >
                <div className="h-[120px] border-[#4AA8FE] rounded-lg border-[1px] my-6 cursor-pointer">
                  <div className="text-center text-[14px] text-[#FFD084] pt-3 font-normal">
                    On-Section
                  </div>
                  <div className="text-center text-[56px] text-white font-bold ">
                    {currentSectionShiftData && currentSectionShiftData?.length > 0
                      ? currentSectionShiftData[0]?.onSection
                        ? currentSectionShiftData[0]?.onSection
                        : 0
                      : 0}
                  </div>
                </div>
              </Link>
              <div className="h-[120px] border-[#4AA8FE] rounded-lg border-[1px] my-2">
                <div className="text-center text-[14px] text-[#FFD084] pt-3">
                  Cuts
                </div>
                <div className="text-center text-[56px] text-white font-bold ">
                  {currentSectionShiftData && currentSectionShiftData?.length > 0
                    ? currentSectionShiftData[0]?.cuts
                      ? currentSectionShiftData[0]?.cuts
                      : 0
                    : '0'}
                </div>
              </div>
            </div>
            <div>
              <div className="border-[#4AA8FE] rounded-lg border-dashed border-[1px] my-6 ml-6 opacity-40">
                <div className="opacity-40 mb-4">
                  {assetsData?.map((ele: any, index) => (
                    <div key={index}
                      className="flex px-[4%] my-2">
                      <div>
                        <div className="ml-2 mt-5 border-white text-white text-[10px] text-center border-dashed p-1 border-[1px] w-min">
                          Icon
                        </div>
                      </div>
                      <div className="text-white text-[36px] font-bold mx-2">
                        {ele}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="w-[93%] mx-auto">
        <div className="bg-[#25354354] border-[#4AA8FE] border-dashed border-[1px] rounded-sm opacity-40">
          <div className="flex mb-2 justify-between mx-4 opacity-40">
            <h2 className="text-[28px] text-white font-bold mt-3">{`Alerts(${alertsData?.length})`}</h2>
            <p className="text-white text-[16px] mt-4 underline">
              View all alerts
            </p>
          </div>
          <div className="w-[97%] m-auto h-full pt-0 opacity-40">
            <Table
              columns={columns}
              data={alertsData ?? []}
              searchText=""
              searchOnColumn="minerName"
              backgroundColor={false}
              scrollable={true}
              sortable={true}
              dataRenderLimitMdScreen={4}
              dataRenderLimitLgScreen={5}
              tableHeightClassLg="h-[240px]"
              tableHeightClassMd="h-[240px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
