import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { UserRole } from '../../users/entities/user-role.entity';
import { RoleFeatureAccess } from '../../role_feature_access/entities/role-feature-access.entity';
import { Company } from '../../companies/entities/company.entity';

@Entity('roles')
export class Role {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'name', nullable: false })
  name: string;

  @Column({ name: 'company_id', type: 'int', nullable: false })
  companyId: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @OneToMany(() => UserRole, (userRole) => userRole.role)
  userRoles: UserRole;

  @ManyToOne(() => Company, (company) => company.roles)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @OneToMany(
    () => RoleFeatureAccess,
    (roleFeatureAccess) => roleFeatureAccess.role
  )
  roleFeatureAccess: RoleFeatureAccess[];
}
