import { getComplianceData } from '../../api/compliance/complianceapis';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function useGetComplianceData() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => getComplianceData(),
    onSettled: async (data: any, error: any) => {
      if (error) {
        return error.response?.data?.message;
      } else {
        await queryClient.invalidateQueries({ queryKey: ['compliance-data'] });
      }
    },
    onSuccess: function () {
      queryClient.invalidateQueries({
        queryKey: ['compliance-data'],
        exact: true,
      });
    },
  });
}
