import { Params, useParams } from 'react-router-dom';
import { object } from 'yup';

const pageNameMapping = [
  { pageName: 'LocationDashboard', url: 'Location/dashboard' },
  { pageName: 'LocationLiveCheckins', url: 'Location/live/checkins' },
  { pageName: 'LocationLiveSection', url: 'Location/live/sections' },
  { pageName: 'LocationLiveWatchlist', url: 'Location/live/watchlist' },
  { pageName: 'LocationReportCheckins', url: 'Location/report/checkins' },
  { pageName: 'LocationReportSection', url: 'Location/report/sections' },
  { pageName: 'LocationReportPersonnel', url: 'Location/report/personnel' },
  { pageName: 'ProductionLiveMine', url: 'Production/live/mine' },
  { pageName: 'ProductionLiveSections', url: 'Production/live/sections' },
  { pageName: 'ProductionLiveAlerts', url: 'Production/live/alerts' },
  { pageName: 'ProductionReportMine', url: 'Production/report/mine' },
  { pageName: 'ProductionReportSection', url: 'Production/report/sections' },
  { pageName: 'ProductionReportComapre', url: 'Production/report/compare' },

  { pageName: 'AtmosphereDashboard', url: 'Atmosphere/dashboard' },
  { pageName: 'AtmosphereLiveGas', url: 'Atmosphere/live/gas' },
  {
    pageName: 'AtmosphereLiveEnvironmental',
    url: 'Atmosphere/live/Environmental',
  },
  { pageName: 'AtmosphereLiveVentilation', url: 'Atmosphere/live/Ventilation' },
  {
    pageName: 'AtmosphereLiveNotifications',
    url: 'Atmosphere/live/Notifications',
  },

  { pageName: 'AtmosphereReportGas', url: 'Atmosphere/report/gas' },
  {
    pageName: 'AtmosphereReportEnvironmental',
    url: 'Atmosphere/report/Environmental',
  },
  {
    pageName: 'AtmosphereReportVentilation',
    url: 'Atmosphere/report/Ventilation',
  },
  {
    pageName: 'AtmosphereReportNotifications',
    url: 'Atmosphere/report/Notifications',
  },

  { pageName: 'Compliance', url: '/Compliance' },
  { pageName: 'Users', url: 'Setting/users' },
  { pageName: 'Features', url: 'Setting/Features' },
  { pageName: 'AuditLogs', url: '/AuditLogs' },
  { pageName: 'Mines', url: 'Mines' },
  { pageName: 'Goals', url: 'Setting/Goals' },
  { pageName: 'Templates', url: 'Forms/Templates' },
  { pageName: 'Forms', url: 'Forms' },
  { pageName: 'Completed', url: 'Forms/Completed' },
  { pageName: 'New', url: 'Forms/New' },
  { pageName: 'NewForm', url: 'Forms/New/NewForm' },
  { pageName: 'Form', url: 'Forms/New/Form' },
  { pageName: 'Shifts', url: 'Setting/Shifts' },
];

export function getPageNamesFromUrl(requestedUrl: string) {
  const normalizeUrl = (url: string) =>
    url.replace(/\d+/g, '').toLowerCase().replace(/\/$/, '');

  const normalizedUrl = normalizeUrl(requestedUrl);
  const matchedPages = pageNameMapping
    .filter(({ url }) => normalizedUrl.includes(normalizeUrl(url)))
    .sort((a, b) => b.url.length - a.url.length)
    .map(({ pageName }) => pageName);
  return matchedPages.length > 0 ? matchedPages[0] : 'unknownpage';
}
