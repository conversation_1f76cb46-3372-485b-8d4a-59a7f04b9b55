import { getUserById } from '../../../api/users/userapis';
import decodeJWT from '../../../utils/jwtdecoder';
import { useQuery } from '@tanstack/react-query';
import SubMenuInnerNavBar from '../common/SubMenuInnerNavBar';
import { useParams } from 'react-router-dom';
import { getFeatures } from '../../../api/users/userapis';

const ProdLive = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['user_by_id'],
    queryFn: () => getUserById(Number(decodeJWT()?.userId)),
    refetchOnWindowFocus: false,
  });
  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  const params = useParams();

  if (
    features?.data.some(
      (feature: any) => feature.FeatureName == 'Production'
    ) ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <SubMenuInnerNavBar
        leftTitle="Live"
        rightTitle="Production"
        rightSubTitle1="Last Updated"
        tabNames={['mine', 'sections', 'alerts']}
        showSearchBox={false}
        pathToRender={
          decodeJWT()?.role == 'superuser'
            ? `Mines/${params?.mineId}/Production/live`
            : 'Production/live'
        }
        defaultTab="live/mine"
        rightSubTitle2="Last Updated E-Form"
      />
    );
  } else {
    return '';
  }
};

export default ProdLive;
