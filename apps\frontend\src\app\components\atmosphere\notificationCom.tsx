import { useOutletContext, useParams } from 'react-router-dom';
import {
  AlertTableCompliance,
  CSVIcon,
} from '../../../../src/assets/icons/icons';
import Table, { ColumnDef } from '../common/Table';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import decodeJWT from '../../../../src/utils/jwtdecoder';
import {
  getATMLiveNotification,
  getATMReportNotification,
} from '../../../../src/api/atmosphere/atmoshpere';
import dayjs from 'dayjs';
import Loader from '../common/Loader';
import * as XLSX from 'xlsx';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';

const columns: ColumnDef[] = [
  {
    key: 'time',
    label: 'Time',
    type: 'text',
    render: (row) => (
      <div className=" w-max">
        <div className="text-[14px] font-medium">{row?.time}</div>
        <div className="text-[12px] text-white/50 float-right">{row?.day}</div>
      </div>
    ),
  },
  { key: 'type', label: 'Type', type: 'text' },
  { key: 'section', label: 'Section', type: 'text' },
  { key: 'label', label: 'Label', type: 'text' },
  { key: 'nid', label: 'ID', type: 'text' },
  {
    key: 'compliance',
    label: 'Compliance',
    type: 'element',
    render: (row) => (
      <AlertTableCompliance
        className={`${row?.compliance ? '' : 'invisible'} m-auto`}
      />
    ),
  },
];

const NotificationComp = () => {
  const [showModal, setShowModal] = useState(false);
  const [fileName, setFileName] = useState('');
  const params = useParams();
  const decoded = decodeJWT();

  const [dateRangeSelected] = useOutletContext() as [string, any, any];

  const { data: notificationData, isLoading } = useQuery({
    queryKey: ['notificationData', dateRangeSelected],
    queryFn: () => {
      if (params['*']?.includes('report')) {
        return getATMReportNotification(
          decoded?.mineid,
          dayjs(dateRangeSelected?.startDate).format('YYYY-MM-DD'),
          dayjs(dateRangeSelected?.endDate).format('YYYY-MM-DD')
        );
      } else {
        return getATMLiveNotification(decoded?.mineid);
      }
    },
    refetchOnWindowFocus: false,
  });
  let data = notificationData?.data?.notifications;

  function getDateToExcel(date: string, time: string): string {
    return dayjs(date).format('MM/DD/YYYY') + ' ' + time;
  }

  const downloadExcel = () => {
    const worksheetData = [
      ['Label', 'ID', 'Section', 'Date/Time', 'Type'],
      ...data.map((row: any) => [
        row.label,
        row.nid,
        row.section,
        getDateToExcel(row?.day, row?.time),
        row.type,
      ]),
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // Set all columns width to 40 characters
    worksheet['!cols'] = Array(worksheetData[0].length).fill({ wch: 25 });

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    // Export
    XLSX.writeFile(workbook, `${fileName}.xlsx`);
  };
  return (
    <>
      {isLoading ? (
        <>
          <div className="h-[50vh] flex items-center w-full justify-center">
            <Loader />
          </div>
        </>
      ) : (
        <>
          {' '}
          <div className="px-[4%] ">
            <div
              className={` ${params['*']?.includes('report') ? '' : 'hidden'}`}
            >
              <div
                className={`flex justify-end text-[16px] text-[#FFB132] gap-2 my-2 cursor-pointer ${
                  notificationData?.data && data?.length != 0 ? '' : 'invisible'
                }`}
                onClick={() => {
                  mixpanel.track('CSV Export', {
                    // PageName: 'Atmosphere Notification Report',
                    Page_Name: getPageNamesFromUrl(
                      params['*'] ?? 'Atmosphere Notification Report'
                    ),
                  });

                  setShowModal(true);
                }}
              >
                <CSVIcon /> Export to CSV
              </div>
            </div>

            {notificationData?.data && data?.length != 0 ? (
              <>
                <div className="flex mb-2 ">
                  <h2 className=" flex text-xl font-bold text-white mt-3 ">
                    {`All Notifications ${
                      params['*']?.includes('report') ? '' : 'Today'
                    }  (${notificationData?.data?.notifications?.length || 0})`}
                  </h2>
                </div>
                <div className="h-[60vh] overflow-auto">
                  <Table
                    columns={columns}
                    data={data}
                    searchText={''}
                    searchOnColumn=""
                    backgroundColor={false}
                    scrollable={true}
                    sortable={true}
                    dataRenderLimitMdScreen={4}
                    dataRenderLimitLgScreen={5}
                    tableHeightClassLg={`h-[240px]`}
                    tableHeightClassMd={`h-[240px]`}
                  />
                </div>
              </>
            ) : (
              <div className="text-[26px] font-bold  text-center text-white mt-[50px]">
                {' '}
                {`There are  ${
                  !params['*']?.includes('report') ? 'currently no live' : 'no'
                } notifications data`}
              </div>
            )}

            {showModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-[#0F0F0F] text-white p-4 rounded-xl w-[25%] shadow-xl">
                  <h2 className="text-xl font-bold mb-4 text-center">
                    Export to CSV
                  </h2>

                  <label className="block mb-2 font-semibold">File Name</label>
                  <input
                    type="text"
                    value={fileName}
                    onChange={(e) => setFileName(e.target.value)}
                    className="w-full px-4 py-2 rounded-md text-black focus-none outline-none"
                    placeholder="Enter file name"
                  />

                  <div className="flex justify-between mt-6 w-full">
                    <button
                      onClick={() => setShowModal(false)}
                      className="px-4 py-2 w-[48%] border border-blue-500 text-white rounded-md hover:bg-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => {
                        downloadExcel();
                        setShowModal(false);
                      }}
                      className="px-4 py-2 w-[48%] bg-blue-500 hover:bg-blue-600 text-white rounded-md"
                    >
                      Export Data
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </>
  );
};

export default NotificationComp;
