import Datepicker from 'react-tailwindcss-datepicker';
import { useState, useEffect, useRef } from 'react';
import dayjs from 'dayjs';
import { useParams } from 'react-router-dom';
import { shortcuts } from '../../../utils/constant';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import decodeJWT from '../../../utils/jwtdecoder';

const SingleSelectedDatePicker = (props: any) => {
  const today = dayjs();
  const params = useParams();
  const wrapperRef = useRef(null);
  const [value, setValue] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  const handleValueChange = async (newValue: any) => {
    const selectDate = JSON.stringify(newValue.startDate);
    mixpanel.track('Date Selection', {
      Page_Name: getPageNamesFromUrl(params['*']),
      MshaId: decodeJWT()?.mshaId,
    });

    if (params[`*`]?.includes('Forms/Completed')) {
      setValue(newValue);
      props?.setSelectedDate(newValue);
      sessionStorage.setItem('completedFormsDateRange', selectDate);
    }
    if (params[`*`]?.includes('Location/report/sections')) {
      setValue(newValue);
      props?.setSelectedDate(newValue);
      sessionStorage.setItem('locationReportDateRange', selectDate);
    }
    if (params[`*`]?.includes('/report/checkins')) {
      setValue(newValue);
      props?.setDate(newValue);
      sessionStorage.setItem('locationReportDateRange', selectDate);
    }
    if (params['*']?.includes('Production/report/sections')) {
      setValue(newValue);
      props?.setRepSecDate(newValue);
      sessionStorage.setItem('prodRepSecDateRange', selectDate);
    }
    if (
      newValue?.startDate === null &&
      newValue?.endDate === null &&
      (params[`*`]?.includes('Location/report/sections') ||
        params[`*`]?.includes('/report/checkins'))
    ) {
      sessionStorage.removeItem('locationReportDateRange');
    }
    if (
      newValue?.startDate === null &&
      newValue?.endDate === null &&
      params['*']?.includes('Production/report/sections')
    ) {
      sessionStorage.removeItem('prodRepSecDateRange');
    }
    if (
      newValue?.startDate === null &&
      newValue?.endDate === null &&
      params['*']?.includes('Forms/Completed')
    ) {
      sessionStorage.removeItem('completedFormsDateRange');
    }
  };

  useEffect(() => {
    const savedDateRange = sessionStorage.getItem('completedFormsDateRange');

    if (params['*']?.includes('Forms/Completed') && savedDateRange) {
      let savedDate;
      try {
        savedDate = JSON.parse(savedDateRange);
      } catch (error) {
        console.error(
          'Invalid JSON in savedDateRange or JSON is undefined:',
          error
        );
        return;
      }

      if (savedDate) {
        setValue({
          startDate: savedDate,
          endDate: savedDate,
        });
        props?.setDate({ startDate: savedDate, endDate: savedDate });
        props?.setSelectedDate({ startDate: savedDate, endDate: savedDate });
      }
    }
  }, [params]);

  useEffect(() => {
    const savedDateRange = sessionStorage.getItem('locationReportDateRange');
    const prodRepSecDates = sessionStorage.getItem('prodRepSecDateRange');

    if (
      (params['*']?.includes('Location/report/sections') ||
        params['*']?.includes('/report/checkins')) &&
      savedDateRange
    ) {
      let savedDate;
      try {
        savedDate = JSON.parse(savedDateRange);
      } catch (error) {
        console.error(
          'Invalid JSON in savedDateRange or JSON is undefined:',
          error
        );
        return;
      }

      if (savedDate) {
        setValue({
          startDate: savedDate,
          endDate: savedDate,
        });
        props?.setDate({ startDate: savedDate, endDate: savedDate });
        props?.setSelectedDate({ startDate: savedDate, endDate: savedDate });
      }
    } else if (
      params['*']?.includes('Production/report/sections') &&
      prodRepSecDates
    ) {
      const savedProdDate = JSON.parse(prodRepSecDates);
      setValue({
        startDate: savedProdDate,
        endDate: savedProdDate,
      });
      props?.setRepSecDate({
        startDate: savedProdDate,
        endDate: savedProdDate,
      });
    }
  }, [params]);

  useEffect(() => {
    if (
      (params['*']?.includes('Location/report/sections') ||
        params['*']?.includes('/report/checkins')) &&
      !sessionStorage.getItem('locationReportDateRange')
    ) {
      setValue({
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0],
      });
      props?.setDate({
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0],
      });
      props?.setSelectedDate({
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0],
      });
    }

    if (
      params['*']?.includes('Production/report/sections') &&
      !sessionStorage.getItem('prodRepSecDateRange')
    ) {
      props?.setRepSecDate({
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0],
      });
    }
  }, [params]);

  useEffect(() => {
    const inputElement = document?.querySelector('.showtitle input');
    if (inputElement) {
      inputElement.title = `${dayjs(value?.startDate).format('MMM DD, YY')}`;
    }
  }, [value]);

  const handleCalendarIconClick = () => {
    const input = wrapperRef.current?.querySelector('input');
    if (input) input.focus();
  };

  let datepickervalue:any = {
    startDate: new Date(),
    endDate: new Date(),
  };
  if(value.startDate) {
    datepickervalue.startDate = new Date(value.startDate);
  }
  if(value.endDate) {
    datepickervalue.endDate = new Date(value.endDate);
  }
  let datepickerstartfrom = value?.startDate ? new Date(value?.startDate) : null;
  
  return (
    <div className="custom-datepicker relative " ref={wrapperRef}>
      <Datepicker
        value={datepickervalue}
        asSingle={props?.asSingle}
        displayFormat="MMM DD, YYYY"
        separator={'-'}
        onChange={handleValueChange}
        showShortcuts={props?.showShortcuts}
        className={'custom-datepicker'}
        popoverDirection="down"
        useRange={false}
        configs={{
          shortcuts: shortcuts,
        }}
        maxDate={new Date(new Date().setHours(0, 0, 0, 0))}
        startFrom={datepickerstartfrom}
      />

      <div
        className="absolute bottom-[10px] right-[10px] cursor-pointer peer"
        onClick={handleCalendarIconClick}
        title=" Edit date range"
      >
        <svg
          className="h-5 w-5 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z"
          />
        </svg>
      </div>
    </div>
  );
};

export default SingleSelectedDatePicker;
