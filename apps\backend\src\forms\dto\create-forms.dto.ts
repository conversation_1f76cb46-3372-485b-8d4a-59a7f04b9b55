import {
  IsDefined,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateFormDto {
  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  mineId: number;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  formTemplateId: number;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  formTemplateDefinitionId: number;

  @IsString({ message: 'Name must be a string' })
  @Length(1, 200, {
    message: 'Name length must be between 1 and 200 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  content: string;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
