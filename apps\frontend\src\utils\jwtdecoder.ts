import { jwtDecode } from 'jwt-decode';

export interface JwtPayload {
  username: string;
  email: string;
  userId: number;
  companyid: number;
  firstname: string;
  lastname: string;
  role: string;
  mineid: number;
  mshaId: string,
  minename: string
}

export default function decodeJWT() {
  const token: string | null = localStorage.getItem('token');
  if (token) {
    try {
      const decoded: JwtPayload = jwtDecode(token);
      return decoded;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }
}
