import Plot from 'react-plotly.js';
import { useParams } from 'react-router-dom';

export default function MultiLineGraph() {
  const params = useParams();

  const prodliveLayout = {
    barmode: 'stack',
    xaxis: {
      title: 'HOUR',
      type: 'category',
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      // tickangle: 0,
      categoryorder: 'array',
      // tickmode: 'array',
    },
    yaxis: {
      title: 'Feet Mined',
      type: 'linear',
      range: [0, 2220],
      tickvals: [0, 200, 400, 600, 800, 1000, 1200, 1400, 1600, 1800, 2000],
      ticktext: [0, 200, 400, 600, 800, 1000, 1200, 1400, 1600, 1800, 2000],
      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 75%)',
      // gridwidth: '2.5px',
      zeroline: true,
      zerolinecolor: 'rgba(74, 168, 256, 75%)',
    },
    showlegend: false,
    margin: { t: 0, l: 60, b: 40, r: 20 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
  };

  const repSecLayout = {
    barmode: 'stack',
    xaxis: {
      title: 'HOUR',
      type: 'category',
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      tickangle: 0,
      categoryorder: 'array',
      // tickmode: 'array',
    },
    yaxis: {
      title: 'Feet Mined',
      type: 'linear',
      range: [0, 800],
      tickvals: [0, 100, 200, 300, 400, 500, 600],
      ticktext: [0, 100, 200, 300, 400, 500, 600],
      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 50%)',
      zeroline: true,
      zerolinecolor: 'rgba(128, 194, 254, 1)',
    },
    showlegend: false,
    margin: { t: 0, l: 55, b: 70, r: 30 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
  };

  const prodLivedata = [
    {
      x: [8, 9, 10, 11, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
      y: [
        1600, 1600, 1600, 1600, 1600, 1600, 1600, 1600, 1600, 1600, 1600, 1600,
        1600, 1600, 1600, 1600,
      ],
      type: 'scatter',
      marker: {
        color: 'rgba(255, 194, 94, 1)',
      },
      mode: 'lines',
      line: {
        color: 'white',
        dash: 'dot',
      },
    },

    {
      x: [8, 9, 10, 11, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
      y: [0, 120, 240, 360, 480, 600, 720, 840, 960, 1080, 1210, 1480, , , , ,],
      type: 'scatter',
      marker: {
        color: 'rgba(255, 194, 94, 1)',
      },
      mode: 'lines',
      line: {
        color: 'rgba(254, 117, 74, 1)',
        dash: 'dot',
      },
    },
    {
      x: [8, 9, 10, 11, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
      y: [0, 120, 180, 360, 410, 490, 650, 720, 860, 980, 1410, , , , ,],
      type: 'scatter',
      marker: {
        color: 'rgba(255, 194, 94, 1)',
      },
      mode: 'lines',
      line: {
        color: 'rgba(255, 194, 94, 1)',
      },
    },
  ];

  const repSecData = [
    {
      x: [6, 7, 8, 9, 10, 11, 12, 1, 2, 3],
      y: [500, 500, 500, 500, 500, 500, 500, 500, 500, 500],
      type: 'scatter',
      marker: {
        color: 'white',
      },
      mode: 'lines',
      line: {
        // color: 'rgba(255, 194, 94, 1)',
        dash: 'dot',
      },
    },

    {
      x: [6, 7, 8, 9, 10, 11, 12, 1, 2, 3],
      y: [0, 50, 120, 160, 250, 280, 300, 450, 550, 600],
      type: 'scatter',
      marker: {
        color: 'rgba(255, 194, 94, 1)',
      },
      mode: 'lines',
      line: {
        color: 'rgba(255, 194, 94, 1)',
      },
    },
  ];

  return (
    <div
      className={`w-full ${
        params['*']?.includes('Production/live/mine') ? 'h-72' : 'h-72'
      }`}
      w-full
      style={{ overflow: 'hidden' }}
    >
      <Plot
        className={`bg-transparent w-full ${
          params['*']?.includes('Production/live/mine') ? `h-72` : `h-72`
        }`}
        data={
          params['*']?.includes('Production/live/mine')
            ? prodLivedata
            : repSecData
        }
        layout={
          params['*']?.includes('Production/live/mine')
            ? prodliveLayout
            : repSecLayout
        }
        config={{ displayModeBar: false, responsive: true }}
      />
    </div>
  );
}
