import { Controller, Get, Param, UseGuards, Request } from '@nestjs/common';
import { SectionsService } from './sections.service';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { ApiTags } from '@nestjs/swagger';

@Controller('portal/v1/sections')
@ApiTags('sections')
export class SectionsController {
  constructor(private readonly sectionsService: SectionsService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Request() req) {
    let mineId = req.user.mineid;
    return this.sectionsService.findAll(mineId);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: number) {
    return this.sectionsService.findOne(+id);
  }
}
