import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  checkShiftName,
  ShiftData,
  addShift,
  editShift,
  updateShiftStatus,
} from '../../api/shifts/shiftapis';

export function useCheckShiftNameMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (name: string) => checkShiftName(name),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['check-shift-name'] });
      }
    },
  });
}

export function useAddShiftMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: ShiftData) => addShift(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-shift'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['active-shifts'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useEditShiftMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) => {
      const id: any = data?.id;
      delete data.id;
      return editShift(id, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['edit-shift'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['active-shifts'].includes(query.queryKey[0]);
        }
      });
    },
  });
}

export function useUpdateShiftStatusMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return updateShiftStatus(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['disable-shift'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        predicate: (query:any) => {
          return ['active-shifts'].includes(query.queryKey[0]);
        }
      });
    },
  });
}