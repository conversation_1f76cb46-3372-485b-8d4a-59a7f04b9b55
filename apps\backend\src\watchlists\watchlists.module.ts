import { Module } from '@nestjs/common';
import { WatchlistsController } from './watchlists.controller';
import { WatchlistsService } from './watchlists.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WatchlistItem } from './entities/watchlist.entity';
import { AuditModule } from '../audit/audit.module';
import { MinesModule } from '../mines/mines.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([WatchlistItem]),
    AuditModule,
    MinesModule,
  ],
  controllers: [WatchlistsController],
  providers: [WatchlistsService],
})
export class WatchlistsModule {}
