import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { FormCategory } from '../../form_categories/entities/form_category.entity';
import { FormTemplateDefinition } from '../../form_template_definitions/entities/form_template_definitions.entity';
import { Form } from '../../forms/entities/forms.entity';

@Entity('form_templates')
export class FormTemplate {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'mine_id', type: 'int', nullable: false })
  mineId: number;

  @Column({ name: 'form_category_id', type: 'int', nullable: false })
  formCategoryId: number;

  @Column({ name: 'name', nullable: false })
  name: string;

  @Column({ name: 'description', nullable: false })
  description: string;

  @Column({ name: 'is_delete', default: false, nullable: true })
  isDelete: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @ManyToOne(() => FormCategory)
  @JoinColumn({ name: 'form_category_id' })
  formCategory: FormCategory;

  @OneToMany(() => FormTemplateDefinition, formTemplateDefinition => formTemplateDefinition.formTemplate)
  formTemplateDefinitions: FormTemplateDefinition[];

  @OneToMany(() => Form, form => form.formTemplate)
  forms: Form[];
}
