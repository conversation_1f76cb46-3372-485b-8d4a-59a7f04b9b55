{"$schema": "./node_modules/nx/schemas/nx-schema.json", "affected": {"defaultBase": "master"}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/cypress/**/*", "!{projectRoot}/**/*.cy.[jt]s?(x)", "!{projectRoot}/cypress.config.[jt]s", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "previewTargetName": "preview", "testTargetName": "test", "serveTargetName": "serve", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/cypress/plugin", "options": {"targetName": "e2e", "componentTestingTargetName": "component-test"}}, {"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}}], "generators": {"@nx/react": {"application": {"babel": true, "style": "css", "linter": "eslint", "bundler": "vite"}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint"}}}}