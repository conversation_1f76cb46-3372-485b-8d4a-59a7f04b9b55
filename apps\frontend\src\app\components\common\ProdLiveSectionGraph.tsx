import { useState } from 'react';
import Plot from 'react-plotly.js';
import GraphSkeleton from './GraphSkeleton';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import decodeJWT from '../../../utils/jwtdecoder';
import { useParams } from 'react-router-dom';

export default function ProdLiveSectionGraph(props: any) {
  const [showDetails, setShowDetails] = useState(false);
  const params = useParams();

  function generateTimes(
    startTime: string,
    endTime: string,
    intervalMinutes: number = 60
  ): number[] {
    const times: number[] = [];
    let currentTime = new Date(`2000-01-01T${startTime}`);
    let endTimeObj = new Date(`2000-01-01T${endTime}`);

    if (endTimeObj < currentTime) {
      endTimeObj.setDate(endTimeObj.getDate() + 1);
    }

    while (currentTime <= endTimeObj) {
      const hours = currentTime.getHours();
      times.push(hours);

      currentTime.setMinutes(currentTime.getMinutes() + intervalMinutes);
    }
    const lastTime = new Date(`2000-01-01T${endTime}`);
    if (lastTime.getHours() === 23 && lastTime.getMinutes() >= 0) {
      times[times.length] = 24;
    }

    return times;
  }

  const shiftsArr: any = [];
  const maintenanceShift: any = [];
  props?.shifts?.map((ele: any) => {
    if (ele?.shiftType == 'Maintenance') {
      maintenanceShift.push(generateTimes(ele?.startTime, ele?.endTime));
    } else {
      shiftsArr.push(generateTimes(ele?.startTime, ele?.endTime));
    }
  });

  const yesterdayLine = {
    x: props?.data?.yesterdayCutsData?.coordinates?.map(
      (ele: any) => ele?.cutEndTime
    ),
    y: props?.data?.yesterdayCutsData?.coordinates?.map(
      (ele: any) => ele?.feetMined
    ),
    mode: 'lines',
    text: 'Yesterday',
    type: 'scatter',
    xanchor: 'middle',
    xref: 'paper',
    name: '',
    line: {
      color: 'white',
      width: 1,
    },
    hoverinfo: 'none',
  };
  const trendLine = {
    x: props?.data?.trendlineCutsData?.coordinates?.map(
      (ele: any) => ele?.cutEndTime
    ),
    y: props?.data?.trendlineCutsData?.coordinates?.map(
      (ele: any) => ele?.feetMined
    ),
    mode: 'lines',
    text: 'trendline',
    type: 'scatter',
    name: '',
    line: {
      color: 'rgba(254,117,74,100%)',
      width: 1,
      dash: 'dash',
    },
    hoverinfo: 'none',
  };
  const bestdayLine = {
    x: props?.data?.bestDayCutsData?.coordinates?.map(
      (ele: any) => ele?.cutEndTime
    ),
    y: props?.data?.bestDayCutsData?.coordinates?.map(
      (ele: any) => ele?.feetMined
    ),
    mode: 'lines',
    text: 'bestday',
    type: 'scatter',
    name: '',
    line: {
      dash: 'dash',
      color: 'white',
      width: 1,
    },
    hoverinfo: 'none',
  };
  const liveCutLine = {
    x: props?.data?.liveCutsData?.coordinates?.map(
      (ele: any) => ele?.cutEndTime
    ),
    y: props?.data?.liveCutsData?.coordinates?.map(
      (ele: any) => ele?.feetMined
    ),
    mode: 'lines',
    text: 'live',
    type: 'scatter',
    name: '',
    line: {
      color: 'rgba(255,194,94,100%)',
      width: 3,
    },
    hoverinfo: 'none',
  };

  const yesterdayText = {
    x: [
      props?.data?.yesterdayCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.yesterdayCutsData?.coordinates?.length - 1],
    ],
    y: [
      props?.data?.yesterdayCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[props?.data?.yesterdayCutsData?.coordinates?.length - 1],
    ],
    mode: 'markers+text',
    text:
      props?.data?.yesterdayCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.yesterdayCutsData?.coordinates?.length - 1] == 0 &&
      props?.data?.yesterdayCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[props?.data?.yesterdayCutsData?.coordinates?.length - 1] == 0
        ? ''
        : 'Yesterday',
    textfont: {
      color: 'white',
    },
    marker: { size: 0, color: 'transparent' },
    textposition: 'bottom center',
    type: 'scatter',
    hoverinfo: 'text',
    hovertext: `(${
      props?.data?.yesterdayCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.yesterdayCutsData?.coordinates?.length - 1]
    },${
      props?.data?.yesterdayCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[props?.data?.yesterdayCutsData?.coordinates?.length - 1]
    })`,
    hoverlabel: {
      bgcolor: 'white',
    },
  };

  const trendLineText = {
    x: [
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.trendlineCutsData?.coordinates?.length - 1],
    ],
    y: [
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[props?.data?.trendlineCutsData?.coordinates?.length - 1],
    ],
    mode: 'markers+text',
    text:
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.trendlineCutsData?.coordinates?.length - 1] == 0 &&
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[props?.data?.trendlineCutsData?.coordinates?.length - 1] == 0
        ? ''
        : 'Trendline',
    textfont: {
      color: 'rgba(254,117,74,100%)',
    },
    marker: { size: 0, color: 'transparent' },
    textposition: 'top center',
    type: 'scatter',
    hoverinfo: 'text',
    hovertext: `(${
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.trendlineCutsData?.coordinates?.length - 1]
    },${
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[props?.data?.trendlineCutsData?.coordinates?.length - 1]
    })`,
    hoverlabel: {
      bgcolor: 'rgba(254,117,74,100%)',
    },
  };

  const bestdayText = {
    x: [
      props?.data?.bestDayCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.bestDayCutsData?.coordinates?.length - 1],
    ],
    y: [
      props?.data?.bestDayCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[props?.data?.bestDayCutsData?.coordinates?.length - 1],
    ],
    mode: 'markers+text',
    text: 'Best Day',
    textfont: {
      color: 'white',
    },
    marker: { size: 0, color: 'transparent' },
    textposition: 'top center',
    type: 'scatter',
    hoverinfo: 'text',
    hovertext: `(${
      props?.data?.bestDayCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.bestDayCutsData?.coordinates?.length - 1]
    },${
      props?.data?.bestDayCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[props?.data?.bestDayCutsData?.coordinates?.length - 1]
    })`,
    hoverlabel: {
      bgcolor: 'white',
    },
  };

  const trendzeroText = {
    x: [
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[0],
    ],
    y: [
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[0],
    ],
    mode: 'markers',
    marker: { size: 0, color: 'transparent' },
    type: 'scatter',
    hoverinfo: 'text',
    hovertext: `(${
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[0]
    },${
      props?.data?.trendlineCutsData?.coordinates?.map(
        (ele: any) => ele?.feetMined
      )[0]
    })`,
    hoverlabel: {
      bgcolor: 'white',
    },
  };

  const liveCutText = {
    x: [
      props?.data?.liveCutsData?.coordinates?.map(
        (ele: any) => ele?.cutEndTime
      )[props?.data?.liveCutsData?.coordinates?.length - 1],
    ],
    y: [
      props?.data?.liveCutsData?.coordinates?.map((ele: any) => ele?.feetMined)[
        props?.data?.liveCutsData?.coordinates?.length - 1
      ],
    ],
    mode: 'markers',
    marker: { size: 14, color: 'rgba(255,194,94,100%)' },
    type: 'scatter',
    hoverinfo: 'none',
  };

  let evenIndexArray: any = [];
  let oddIndexArray: any = [];
  shiftsArr.forEach((array: any, index: number) => {
    const nextArray = shiftsArr[index + 1];
    if (nextArray) {
      const arraysLastElement = array[array.length - 1];
      const nextArraysFirstElement = nextArray[0];
      if (arraysLastElement === nextArraysFirstElement) {
        array.pop();
      }
    }

    if (index % 2 === 0) {
      evenIndexArray = evenIndexArray.concat(array);
    } else {
      oddIndexArray = oddIndexArray.concat(array);
    }
  });
  const maintenanceShiftArr: any = [];
  maintenanceShift.forEach((arr: any) => {
    maintenanceShiftArr.push(...arr);
  });

  let maxNumber = Math.max(
    props?.data?.trendlineCutsData?.coordinates?.map(
      (ele: any) => ele?.feetMined
    )[props?.data?.trendlineCutsData?.coordinates?.length - 1]
      ? props?.data?.trendlineCutsData?.coordinates?.map(
          (ele: any) => ele?.feetMined
        )[props?.data?.trendlineCutsData?.coordinates?.length - 1]
      : 0,
    props?.data?.mineGoal ? props?.data?.mineGoal : 0,
    props?.data?.liveCutsData?.coordinates?.map((ele: any) => ele?.feetMined)[
      props?.data?.liveCutsData?.coordinates?.length - 1
    ]
      ? props?.data?.liveCutsData?.coordinates?.map(
          (ele: any) => ele?.feetMined
        )[props?.data?.liveCutsData?.coordinates?.length - 1]
      : 0,
    props?.data?.bestDayCutsData?.coordinates?.map(
      (ele: any) => ele?.feetMined
    )[props?.data?.bestDayCutsData?.coordinates?.length - 1]
      ? props?.data?.bestDayCutsData?.coordinates?.map(
          (ele: any) => ele?.feetMined
        )[props?.data?.bestDayCutsData?.coordinates?.length - 1]
      : 0,
    props?.data?.yesterdayCutsData?.coordinates?.map(
      (ele: any) => ele?.feetMined
    )[props?.data?.yesterdayCutsData?.coordinates?.length - 1]
      ? props?.data?.yesterdayCutsData?.coordinates?.map(
          (ele: any) => ele?.feetMined
        )[props?.data?.yesterdayCutsData?.coordinates?.length - 1]
      : 0
  );

  maxNumber = maxNumber ? maxNumber : 0;

  const annotations = props?.data?.xAxisPoints?.map((point: any) => {
    return {
      x: point,
      y: -0.15,
      xref: 'x',
      yref: 'paper',
      text: String(point),
      showarrow: false,
      font: {
        color: evenIndexArray?.includes(point)
          ? 'rgba(255,194,94,100%)'
          : maintenanceShiftArr?.includes(point)
          ? 'red'
          : oddIndexArray?.includes(point)
          ? 'rgba(38, 31, 82, 1)'
          : 'white',
        size: 12,
      },
    };
  });

  const annotation2 = props?.data?.liveCutsData?.coordinates?.map(
    (ele: any) => ele?.feetMined
  )[props?.data?.liveCutsData?.coordinates?.length - 1]
    ? {
        x: props?.data?.liveCutsData?.coordinates?.map(
          (ele: any) => ele?.cutEndTime
        )[props?.data?.liveCutsData?.coordinates?.length - 1],
        y: props?.data?.liveCutsData?.coordinates?.map(
          (ele: any) => ele?.feetMined
        )[props?.data?.liveCutsData?.coordinates?.length - 1],
        text: `${
          props?.data?.liveCutsData?.coordinates?.map(
            (ele: any) => ele?.feetMined
          )[props?.data?.liveCutsData?.coordinates?.length - 1]
        }`,
        showarrow: true,
        arrowhead: 2,
        ax: 0,
        ay: -30,
        font: {
          color: 'white',
        },
        align: 'center',
        bordercolor: 'rgba(255,194,94,100%)',
        borderwidth: 2,
        borderpad: 4,
        bgcolor: 'rgba(255,194,94,100%)',
        opacity: 0.8,
      }
    : {};

  const layout = {
    xaxis: {
      title: {
        text: 'Hours',
        standoff: -20,
        font: {
          color: 'white',
        },
      },
      color:
        props?.data != undefined && props?.data ? 'rgba(0, 0, 0, 0)' : 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
      automargin: true,
      ticklen: 16,
      range: [0, 25.5],
      tickvals: [
        0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
        20, 21, 22, 23, 24,
      ],
      ticktext: [
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        '10',
        '11',
        '12',
        '13',
        '14',
        '15',
        '16',
        '17',
        '18',
        '19',
        '20',
        '21',
        '22',
        '23',
        '24',
      ],
      fixedrange: true,
      zeroline: false,
    },
    yaxis: {
      title: {
        text: 'Feet Mined',
        standoff: 20,
      },
      type: 'linear',

      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 50%)',
      gridwidth: 1,
      range: [-1, maxNumber < 10 ? 11 : maxNumber + maxNumber / 2],
      fixedrange: true,
      autoscale: true,
    },
    shapes: [
      props?.data?.mineGoal && {
        type: 'line',
        xref: 'paper',
        x0: 0,
        y0: props?.data?.mineGoal,
        x1: 1,
        y1: props?.data?.mineGoal,
        line: { color: 'white', width: 2, dash: 'dot' },
      },
    ],
    showlegend: false,
    margin:
      props?.mine == 'mine'
        ? { t: 30, l: 70, b: 30, r: 30, pad: 10 }
        : { t: 30, l: 70, b: 30, r: 30, pad: 10 },
    automargin: true,
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    dragmode: 'pan',
    modeBarButtonsToRemove: ['lasso2d', 'select2d'],
    displaylogo: false,
    annotations: props?.data?.liveCutsData
      ? props?.data?.liveCutsData?.coordinates?.map(
          (ele: any) => ele?.feetMined
        )[props?.data?.liveCutsData?.coordinates?.length - 1]
        ? [annotation2, ...annotations]
        : [...annotations]
      : [],
  };

  const data = showDetails
    ? [
        yesterdayLine,
        yesterdayText,
        trendLineText,
        liveCutLine,
        bestdayText,
        bestdayLine,
        liveCutText,
        trendLine,
        trendzeroText,
        {
          x: props?.data?.mineGoal
            ? [
                props?.data?.xAxisPoints[0],
                props?.data?.xAxisPoints[props?.data?.xAxisPoints?.length - 1],
              ]
            : [],
          y: props?.data?.mineGoal
            ? [props?.data?.mineGoal, props?.data?.mineGoal]
            : [],
          mode: 'lines',
          name: '',
          line: {
            dash: 'dot',
            width: 0,
            color: 'rgba(0, 0, 0, 0)',
          },
          hoverinfo: 'none',
        },
      ]
    : [
        trendLineText,
        liveCutLine,
        liveCutText,
        trendLine,
        trendzeroText,
        {
          x: props?.data?.mineGoal
            ? [
                props?.data?.xAxisPoints[0],
                props?.data?.xAxisPoints[props?.data?.xAxisPoints?.length - 1],
              ]
            : [],
          y: props?.data?.mineGoal
            ? [props?.data?.mineGoal, props?.data?.mineGoal]
            : [],
          mode: 'lines',
          name: '',
          line: {
            dash: 'dot',
            width: 0,
            color: 'rgba(0, 0, 0, 0)',
          },
          hoverinfo: 'none',
        },
      ];

  return (
    <div>
      {props?.apiStatus == 'success' ? (
        Object?.keys(props?.data == undefined ? {} : props?.data)?.length !==
        0 ? (
          props?.data?.liveCutsData?.coordinates.length != 0 ||
          props?.data?.bestDayCutsData?.coordinates?.length != 0 ||
          props?.data?.yesterdayCutsData?.coordinates?.length != 0 ? (
            <div className="2xl:!h-[35.5vh] xl:!h-[56vh]">
              <Plot
                data={data}
                layout={layout}
                className="h-80 !w-[100%]"
                config={{
                  displayModeBar: false,
                  responsive: true,
                  dragmode: false,
                }}
              />
              <div className="float-end relative top-[-33px] top-[-33px] right-[40px]">
                <div className="flex">
                  <div className="text-white mx-2">Show Details</div>
                  <div
                    className={`relative w-[32px] h-[16px]  rounded-full mt-1`}
                  >
                    <label
                      className={'cursor-pointer'}
                      title={`${
                        showDetails
                          ? 'Click to Hide Details'
                          : ' Click to Show Details'
                      }`}
                    >
                      <input
                        type="checkbox"
                        tabIndex={Number('-1')}
                        className="sr-only peer"
                        checked={showDetails}
                        onChange={() => {
                          setShowDetails(!showDetails); // This toggles the checkbox
                          mixpanel.track('Show Detail Graph', {
                            Page_Name: getPageNamesFromUrl(params['*']),
                            MshaId: decodeJWT()?.mshaId,
                          });
                        }}
                      />
                      <div
                        className={`w-full h-full bg-gray-200  bg-opacity-25 border-[0.5px] border-gray-200 outline-none peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-[162%] rtl:peer-checked:after:-translate-x-[162%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white  after:rounded-full after:h-[10px] after:w-[10px] after:my-[1px] after:mx-[2px]  after:transition-all  peer-checked:bg-[#96FB60] peer-checked:bg-opacity-25  peer-checked:border-[1px] peer-checked:border-[#96FB60]`}
                      ></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <GraphSkeleton
              className={
                props?.mine == 'mine'
                  ? '2xl:!h-[35.5vh] xl:!h-[50vh] mt-10  mr-4'
                  : '2xl:!h-[30.4vh] xl:!h-[48vh] mr-8 mt-10'
              }
              apiStatus={props?.apiStatus}
            />
          )
        ) : (
          <GraphSkeleton
            className={
              props?.mine == 'mine'
                ? '2xl:!h-[35.5vh] xl:!h-[50vh] mt-10 mr-4'
                : '2xl:!h-[30.4vh] xl:!h-[48vh] mr-8 mt-10'
            }
            apiStatus={props?.apiStatus}
          />
        )
      ) : (
        <GraphSkeleton
          className={
            props?.mine == 'mine'
              ? '2xl:!h-[35.5vh] xl:!h-[50vh] mt-10 mr-4'
              : '2xl:!h-[30.4vh] xl:!h-[48vh] mr-8 mt-10'
          }
          apiStatus={props?.apiStatus}
        />
      )}
      {!showDetails &&
      props?.data?.liveCutsData?.coordinates?.length == 0 &&
      props?.data?.mineGoal == 0 &&
      props?.data?.bestDayCutsData?.coordinates?.length != 0 ? (
        <p
          className={`text-white relative top-[-250px]  xl:text-end 2xl:text-center  text-[28px] ${
            props?.mine == 'mine' ? '' : 'xl:right-[80px]'
          }`}
        >
          No data available
        </p>
      ) : (
        ''
      )}
    </div>
  );
}
