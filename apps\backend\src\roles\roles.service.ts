import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Role } from './entities/role.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>
  ) {}

  async create(createRoleDto: CreateRoleDto) {
    return await this.roleRepository.save(createRoleDto);
  }

  async findAll(companyId: number) {
    return await this.roleRepository.find({
      where: { companyId: companyId },
    });
  }

  async findOne(id: number) {
    return await this.roleRepository.findOne({
      where: { id },
      relations: { roleFeatureAccess: true },
    });
  }

  async update(id: number, updateRoleDto: UpdateRoleDto) {
    const roleToUpdate = await this.roleRepository.findOneBy({ id });

    if (!roleToUpdate) {
      throw new NotFoundException('Role not Found!');
    }

    const updatedRole = Object.assign(roleToUpdate, updateRoleDto);
    return await this.roleRepository.save(updatedRole);
  }

  async remove(id: number) {
    const role = await this.roleRepository.findOneBy({ id });

    if (!role) {
      throw new NotFoundException('Role not Found!');
    }
    return this.roleRepository.remove(role);
  }
}
