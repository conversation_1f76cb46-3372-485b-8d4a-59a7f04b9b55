import { Test, TestingModule } from '@nestjs/testing';
import { RolesService } from './roles.service';
import { Role } from './entities/role.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

describe('RolesService', () => {
  let service: RolesService;

  const mockRoleRepository = {
    save: jest.fn(),
    find: jest.fn(),
    findOneBy: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesService,
        {
          provide: getRepositoryToken(Role),
          useValue: mockRoleRepository,
        },
      ],
    }).compile();

    service = module.get<RolesService>(RolesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('create => Should create a new role and return its data', async () => {
    // arrange
    const createRoleDto = {
      companyId: 1,
      name: 'test user 1',
    } as CreateRoleDto;

    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1',
    } as Role;

    jest.spyOn(mockRoleRepository, 'save').mockReturnValue(role);

    // act
    const result = await service.create(createRoleDto);

    // assert
    expect(mockRoleRepository.save).toHaveBeenCalled();
    expect(mockRoleRepository.save).toHaveBeenCalledWith(createRoleDto);

    expect(result).toEqual(role);
  });

  it('findAll => should return an array of roles', async () => {
    // arrange
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1',
    };
    let companyId = 1;
    const roles = [role];
    jest.spyOn(mockRoleRepository, 'find').mockReturnValue(roles);

    // act
    const result = await service.findAll(companyId);

    // assert
    expect(mockRoleRepository.find).toHaveBeenCalled();
    expect(result).toEqual(roles);
  });

  it('getRolesByCompanyId => should return an array or roles by a companyId', async () => {
    // arrange
    const companyId = 1;
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1'
    };
    const roles = [role];
    jest.spyOn(mockRoleRepository, 'find').mockReturnValue(role);

    // act
    const result = await service.findOne(companyId);

    // assert
    expect(mockRoleRepository.find).toHaveBeenCalled();
    expect(mockRoleRepository.find).toHaveBeenCalledWith({companyId});
    expect(result).toEqual(roles);
  });

  it('findOne => should find a role by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 1',
    };

    jest.spyOn(mockRoleRepository, 'findOneBy').mockReturnValue(role);

    // act
    const result = await service.findOne(id);

    // assert
    expect(mockRoleRepository.findOneBy).toHaveBeenCalled();
    expect(mockRoleRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(result).toEqual(role);
  });

  it('update => should find a role by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateRoleDto = {
      companyId: 1,
      name: 'test user 1',
    } as UpdateRoleDto;
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user 2',
    };

    const updatedRole = Object.assign(role, updateRoleDto);
    jest.spyOn(mockRoleRepository, 'save').mockReturnValue(updatedRole);

    // act
    const result = await service.update(id, role);

    // assert
    expect(mockRoleRepository.findOneBy).toHaveBeenCalled();
    expect(mockRoleRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockRoleRepository.save).toHaveBeenCalled();
    expect(mockRoleRepository.save).toHaveBeenCalledWith(updatedRole);
    expect(result).toEqual(updatedRole);
  });

  it('remove => should find a role by a given id, remove and then return Number of affected rows', async () => {
    // arrange
    const id = 1;
    const role = {
      id: 1,
      companyId: 1,
      name: 'test user',
    };

    jest.spyOn(mockRoleRepository, 'remove').mockReturnValue(role);

    // act
    const result = await service.remove(id);

    // assert
    expect(mockRoleRepository.findOneBy).toHaveBeenCalled();
    expect(mockRoleRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockRoleRepository.remove).toHaveBeenCalled();
    expect(mockRoleRepository.remove).toHaveBeenCalledWith(id);
    expect(result).toEqual(role);
  });
});
