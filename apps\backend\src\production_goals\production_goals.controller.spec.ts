import { Test, TestingModule } from '@nestjs/testing';
import { ProductionGoalsController } from './production_goals.controller';
import { ProductionGoalsService } from './production_goals.service';
import { CreateProductionGoalDto } from './dto/create-production_goal.dto';
import { ProductionGoal } from './entities/production_goal.entity';
import { UpdateProductionGoalDto } from './dto/update-production_goal.dto';

describe('ProductionGoalsController', () => {
  let controller: ProductionGoalsController;
  
  const mockProductionGoalsService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductionGoalsController],
      providers: [{
          provide: ProductionGoalsService,
          useValue: mockProductionGoalsService,
      }]
    }).compile();

    controller = module.get<ProductionGoalsController>(ProductionGoalsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('create => should create a new goal by the given data', async () => {
    // arrange
    let now = new Date().toISOString();
    const createProductionGoalDto = {
      shiftId: 1,
      sectionId: 1,
      goal: 100,
      effectiveDate: now,
      createdBy: 1,
    } as CreateProductionGoalDto;

    const goal = {
      id: 1,
      shiftId: 1,
      sectionId: 1,
      goal: 100,
      effectiveDate: now,
      createdBy: 1,
    } as ProductionGoal;

    jest.spyOn(mockProductionGoalsService, 'create').mockReturnValue(goal);

    // act
    const result = await controller.create(createProductionGoalDto);

    // assert
    expect(mockProductionGoalsService.create).toHaveBeenCalled();
    expect(mockProductionGoalsService.create).toHaveBeenCalledWith(createProductionGoalDto);
    expect(result).toEqual(goal);
  });

  it('findAll => should return an array of goals', async () => {
    // arrange
    const goal = {
      id: 1,
      shiftId: 1,
      sectionId: 1,
      goal: 100,
      effectiveDate: new Date().toISOString(),
      createdBy: 1,
    };

    const goals = [goal];
    jest.spyOn(mockProductionGoalsService, 'findAll').mockReturnValue(GeolocationPosition);

    // act
    const result = await controller.findAll();

    // assert
    expect(mockProductionGoalsService.findAll).toHaveBeenCalled();
    expect(result).toEqual(goals);
  });

  it('findOne => should find a goal by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const goal = {
      id: 1,
      shiftId: 1,
      sectionId: 1,
      goal: 100,
      effectiveDate: new Date().toISOString(),
      createdBy: 1,
    };

    jest.spyOn(mockProductionGoalsService, 'findOne').mockReturnValue(goal);

    // act
    const result = await controller.findOne(id);

    // assert
    expect(mockProductionGoalsService.findOne).toHaveBeenCalled();
    expect(mockProductionGoalsService.findOne).toHaveBeenCalledWith(+id);
    expect(result).toEqual(goal);
  });

  it('update => should find a goal by a given id and update its data', async () => {
    // arrange
    let before = new Date().toISOString();
    let now = new Date().toISOString();
    const id = 1;
    const updateProductionGoalDto = {
      endDate: now,
    } as UpdateProductionGoalDto;

    const goal = {
      id: 1,
      shiftId: 1,
      sectionId: 1,
      goal: 200,
      effectiveDate: before,
      endDate: now,
    };

    jest.spyOn(mockProductionGoalsService, 'update').mockReturnValue(goal);

    // act
    const result = await controller.update(id, updateProductionGoalDto);

    // assert
    expect(mockProductionGoalsService.update).toHaveBeenCalled();
    expect(mockProductionGoalsService.update).toHaveBeenCalledWith(+id, updateProductionGoalDto);
    expect(result).toEqual(goal);
  });

  it('remove => should find a goal by a given id, remove and then return number of affected rows', async () => {
    // arrange
    let before = new Date().toISOString();
    let now = new Date().toISOString();
    const id = 1;
    const goal = {
      id: 1,
      shiftId: 1,
      sectionId: 1,
      goal: 100,
      effectiveDate: before,
      endDate: now,
      createdBy: 1,
    };

    jest.spyOn(mockProductionGoalsService, 'delete').mockReturnValue(goal);

    // act
    const result = await controller.remove(id);

    // assert
    expect(mockProductionGoalsService.delete).toHaveBeenCalled();
    expect(mockProductionGoalsService.delete).toHaveBeenCalledWith(+id);
    expect(result).toEqual(goal);
  });
});
