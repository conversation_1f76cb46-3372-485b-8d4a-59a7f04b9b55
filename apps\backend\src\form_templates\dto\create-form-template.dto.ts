import {
  IsDefined,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateFormTemplateDto {
  @IsString({ message: 'Name must be a string' })
  @Length(1, 100, {
    message: 'Name length must be between 1 and 100 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsString({ message: 'Description must be a string' })
  @Length(1, 200, {
    message: 'Description length must be between 1 and 200 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  description: string;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  mineId: number;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  formCategoryId: number;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
