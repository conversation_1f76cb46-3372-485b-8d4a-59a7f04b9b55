interface CardProps {
  sectionName: string;
  numOfMiners: number;
  instruments: string[];
}

const Card = ({ sectionName, numOfMiners, instruments }: CardProps) => {
  return (
    <div className="border border-slate-500 rounded-md text-white p-5 text-center">
      <p className="font-[400] text-[16px]">{sectionName}</p>
      <p className="font-[700] text-[56px]">{numOfMiners}</p>
      {instruments.map((instrument) => (
        <p
          key={instrument}
          className="font-[400] text-[14px] text-neutral-200/25"
        >
          {instrument}
        </p>
      ))}
    </div>
  );
};

export default Card;
