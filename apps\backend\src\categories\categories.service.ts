import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateCategoryDto } from './dto/create-category.dto';
import { Category } from './entities/category.entity';
@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(Category) private categoryRepo: Repository<Category>
  ) {}

  async create(categoryDto: CreateCategoryDto): Promise<Category> {
    const category = this.categoryRepo.create(categoryDto);

    const res = await this.categoryRepo.save(category);
    return res;
  }

  async find(): Promise<Category[]> {
    const categories = await this.categoryRepo.find();
    return categories;
  }

  async findOne(id: number): Promise<Category> {
    const Category = this.categoryRepo.findOne({
      where: {
        id,
      },
    });

    if (!Category) {
      throw new NotFoundException('Category Not Found!');
    }
    return Category;
  }
}
