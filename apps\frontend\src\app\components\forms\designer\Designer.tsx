
import { useEffect, useMemo, useRef, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getRevision, getLatestRevision } from '../../../../api/forms/formtemplateapis';
import { useParams } from 'react-router-dom';
import Loader from '../../common/Loader';
import FormPreview from './preview/FormPreview';
import FormProperties from './properties/FormProperties';
import { Group, Id, Item, Question, SelectedItem, Tool, } from '../types';
import { useAddDefinitionRevision, useEditFormTemplateDefinition } from '../../../../services/mutations/formtemplatemutations';
import { combineProperties, getDefaults } from '../utilities';
import { toast } from 'react-toastify';
import { CloseIcon, RedoIcon, UndoIcon } from '../../../../assets/icons/icons';
import FormToolButton from './tools/FormToolButton';

const FormDesigner = () => {
  const params = useParams();
  const addDefinitionRevision = useAddDefinitionRevision();
  const editFormTemplateDefinition = useEditFormTemplateDefinition();
  const [showPreview, setShowPreview] = useState(false);
  const [templateDetails, setTemplateDetails] = useState<any>({});
  const [selectedItem, setSelectedItem] = useState<SelectedItem>({id:''});
  const [templateHistory, setTemplateHistory] = useState<any>([]);
  const [historyIndex, setHistoryIndex] = useState(0);
	const [publishActive, setPublishActive] = useState(false);
  const [refreshCount, setRefreshCount] = useState(0);
  const [latestVersionId, setLatestVersionId] = useState<Id>('');
  const [currentVersionId, setCurrentVersionId] = useState<Id>('');
  
	const jsonFileInputRef = useRef<HTMLInputElement>(null);
	const queryClient = useQueryClient();

  const { status, data: formTemplate, isLoading } = useQuery({
    queryKey: ['form-template'],
    queryFn: () => getLatestRevision(params?.formTemplateId),
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    return () => {
      setTemplateHistory([]);
      setHistoryIndex(0);
      setTemplateDetails({});
      setSelectedItem({id:''});
      setRefreshCount(0);
    };
  }, []);
  
  useEffect(() => {
    if(formTemplate?.data) {
      setTemplateDetails({
        id: formTemplate?.data?.formTemplateId,
        definitionId: formTemplate?.data?.formTemplateDefinitionId,
        name: formTemplate?.data?.name,
        description: formTemplate?.data?.description,
        categoryId: formTemplate?.data?.formCategoryId,
        category: formTemplate?.data?.category,
        major: formTemplate?.data?.major,
        minor: formTemplate?.data?.minor,
        revision: formTemplate?.data?.revision,
      });
      if(refreshCount == 0) {
        setLatestVersionId(formTemplate?.data?.formTemplateDefinitionId);
      }
      setCurrentVersionId(formTemplate?.data?.formTemplateDefinitionId);
      
      if(formTemplate?.data?.definition) {
        const definition = JSON.parse(formTemplate?.data?.definition) ?? {};
        definition.properties = definition?.properties ?? {};
        definition.content = definition?.content ?? {};
        if(templateHistory.length > 0) {
          const currentHistory = JSON.parse(JSON.stringify(templateHistory));
          const updatedHistory = currentHistory.slice(0,historyIndex);
          updatedHistory.push(definition);
          setTemplateHistory(updatedHistory);
        }
        else {
          setTemplateHistory([definition]);
          setHistoryIndex(0);
        }
      }
      setPublishActive(!formTemplate?.data?.isPublished);
    }
  }, [formTemplate?.data, refreshCount]);

  const templateProperties = useMemo(() => {
    return (historyIndex >= 0 && templateHistory.length > 0
    && templateHistory.length > historyIndex && templateHistory[historyIndex].properties)
    ? JSON.parse(JSON.stringify(templateHistory[historyIndex]?.properties))
    : {};
  }, [templateHistory, historyIndex]);

  const templateContent = useMemo(() => {
    return (historyIndex >= 0 && templateHistory.length > 0 &&
        templateHistory.length > historyIndex && templateHistory[historyIndex].content)
        ? JSON.parse(JSON.stringify(templateHistory[historyIndex].content))
        : {groups: []};
  }, [templateHistory, historyIndex]);

  const groups = useMemo(() => {
    if(!templateContent.groups) {
      return [];
    }
    return JSON.parse(JSON.stringify(templateContent.groups)) ?? [];
  }, [templateContent]);

  const items = useMemo(() => {
    return groups.map((grp: Group) => {
      return (
        grp.items.map((item: Item) => {
          if(!item.key && item.properties.key) {
            item.key = item.properties.key;
          }
          return item;
        })
      );
    }).flat();
  }, [groups]);

  // const prelimFields = useMemo(() => {
  //   return items.filter((i: Item) => i?.properties?.requiredgroup?.prelimfield);
  // }, [items]);

  // useEffect(() => {
  //   console.log(prelimFields);
  // }, [prelimFields]);


  const siblings = useMemo(() => {
    if(selectedItem.group) {
      return groups;
    }
    if(selectedItem.item) {
      return items.filter((itm:Item) => itm.groupId === selectedItem.item?.groupId);
    }
    return [];
  }, [selectedItem])

  const reorderGroups = async (formGroups:any, index:number, selectedItem:any) => {
    const oldSelectedItemId = selectedItem.id;
    for(let g = index; g < formGroups.length; g++) {
      let oldGrpId = formGroups[g].id;
      let groupId = `group-${g}`;
      formGroups[g].id = groupId;
      formGroups[g].name = groupId;
      formGroups[g].index = g;

      if(selectedItem.hasOwnProperty('group')) {
        if(oldSelectedItemId === oldGrpId) {
          selectedItem.id = groupId;
          selectedItem.group = formGroups[g];
        }
      }

      let grpItems = formGroups[g].items;
      const reorderData = await reorderItems(grpItems, 0, groupId, {id:''});
      formGroups[g].items = reorderData.items;

      await reorderQuestions(formGroups[g].questions, 0, groupId);
    }
    return {groups: formGroups, selectedItem};
  }

  const reorderItems = async (grpItems:any, index:number, groupId:Id, selectedItem:any) => {
    const oldSelectedItemId = selectedItem.id;
    for(let i = index; i < grpItems.length; i++) {
      let oldItemId = grpItems[i].id;
      let itemId = `${groupId}-item-${i}`;
      grpItems[i].id = itemId;
      grpItems[i].index = i;
      grpItems[i].groupId = groupId;

      if(selectedItem.hasOwnProperty('item')) {
        if(oldSelectedItemId === oldItemId) {
          selectedItem.id = itemId;
          selectedItem.item = grpItems[i];
        }
      }
    }
    return {items: grpItems, selectedItem};
  }

  const reorderQuestions = async (grpQuestions:any, index:number, groupId:Id) => {
    for(let q = 0; q < grpQuestions.length; q++) {
        let questionId = `question-${groupId}-${q}`;
        grpQuestions[q].id = questionId;
        grpQuestions[q].index = q;
        grpQuestions[q].groupId = groupId;
      }
    return grpQuestions;
  }

  const setNewContent = async (newGroups:Group[], newSelectedItem?:SelectedItem) => {
    let newContent = JSON.parse(JSON.stringify(templateContent));
    newContent.groups = newGroups ?? newContent.groups ?? [];
    
    handleUpdateContent(newContent, newSelectedItem);
  };

  const [canUndoButton, canRedoButton] = useMemo(() => {
    return [
      historyIndex > 0 && templateHistory.length > 1,
      historyIndex >= 0 && historyIndex < templateHistory.length - 1,
    ];
  }, [historyIndex, templateHistory]);

  async function createRevision(template?:any) {
    const data:any = {};
    //data.id = templateDetails.definitionId;
    data.templateId = templateDetails.id;
    if(template) {
      data.definition = JSON.stringify(template);
    }
    else {
      data.definition = JSON.stringify(templateHistory[historyIndex]);
    }

    try {
      const res:any = await addDefinitionRevision.mutateAsync(data);
      if(res?.status == 200 || res?.status == 201) {
        if(res?.data?.id) {
          setTemplateDetails({
            ...templateDetails,
            definitionId: res.data.id
          });
        }
        setPublishActive(true);
      }
    } catch(err: any) {
      toast.success('There was an issue saving the form.');
      setPublishActive(false);
      console.log(err);
    }
  };

  async function storeUpdatedTemplate(template:any) {
    const data:any = {};
    data.id = templateDetails.definitionId;
    data.definition = JSON.stringify(template);
    
    try {
      const res:any = await editFormTemplateDefinition.mutateAsync(data);
      if(res?.status == 200 || res?.status == 201) {
        setPublishActive(true);
      }
    } catch(err: any) {
      toast.success('There was an issue saving the form.');
      setPublishActive(false);
      console.log(err);
    }
  };

	async function createNewGroup(index:number) {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const newId = `group-${currentGroups.length}`;
    
		const groupToAdd: Group = {
			id: newId,
			name: newId,
			index: currentGroups.length,
			items: [],
			properties: getDefaults('Group'),
      questions: []
		}
    
    let newGroups = currentGroups.toSpliced(index, 0, groupToAdd);
    let newSelectedItem = {id: newId, group: groupToAdd};

    const reorderData = await reorderGroups(newGroups, index, newSelectedItem);
    newGroups = reorderData.groups;
    newSelectedItem = reorderData.selectedItem;

		setNewContent(newGroups, newSelectedItem);
	}

	async function cloneGroup(group: Group) {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const newGroupId = `group-${currentGroups.length}`;
    const groupItems = JSON.parse(JSON.stringify(group.items));
    const groupQuestions = JSON.parse(JSON.stringify(group.questions));
    const newGroupProperties = JSON.parse(JSON.stringify(group.properties)) ?? getDefaults('Group');
    const itemsToClone = [];
    const questionsToClone = [];

    for(let i = 0; i < groupItems.length; i++) {
      let item = groupItems[0];
      const newItemId = `${newGroupId}-item-${itemsToClone.length}`;
      let newItemProperties = item.properties ?? getDefaults('Input');
      if(newItemProperties.key) {
        let guid = crypto.randomUUID().toUpperCase();
        newItemProperties.key = guid;
      }

      const itemToAdd: Item = {
        id: newItemId,
        key: newItemProperties.key,
        index: itemsToClone.length,
        groupId: newGroupId,
        content: item.content ?? '',
        properties: newItemProperties,
      }
      itemsToClone.push(itemToAdd);
    }

    for(let i = 0; i < groupQuestions.length; i++) {
      let question = groupQuestions[0];
      const newQuestionId = `question-${newGroupId}-${questionsToClone.length}`;
      const questionToAdd: Question = {
        id: newQuestionId,
        index: questionsToClone.length,
        groupId: newGroupId,
        name: question.name ?? '',
      }
      questionsToClone.push(questionToAdd);
    }
    
		const groupToAdd: Group = {
			id: newGroupId,
			name: newGroupId,
			index: currentGroups.length,
			items: itemsToClone,
			properties: newGroupProperties,
      questions: questionsToClone,
		}
    
    let newGroups = currentGroups.toSpliced(group.index+1, 0, groupToAdd);
    let newSelectedItem = {id: newGroupId, group: groupToAdd};

    const reorderData = await reorderGroups(newGroups, group.index+1, newSelectedItem);
    newGroups = reorderData.groups;
    newSelectedItem = reorderData.selectedItem;

		setNewContent(newGroups, newSelectedItem);
	}

	async function moveGroup(fromIndex:number, direction: string) {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    let toIndex = 0;
    if(direction == 'up') { toIndex = fromIndex-1}
    if(direction == 'down') { toIndex = fromIndex+1}
    if(direction == 'bottom') { toIndex = currentGroups.length-1}
    
    if(toIndex !== fromIndex && toIndex >= 0 && toIndex < currentGroups.length) {
      const currentGroup = JSON.parse(JSON.stringify(currentGroups[fromIndex]));
      let newSelectedItem = {id: currentGroup.id, group: currentGroup};

      const [groupToMove] = currentGroups.splice(fromIndex, 1);
      currentGroups.splice(toIndex, 0, groupToMove);
      
      const reorderData = await reorderGroups(currentGroups, (fromIndex < toIndex ? fromIndex : toIndex), newSelectedItem);
      let newGroups = reorderData.groups;
      newSelectedItem = reorderData.selectedItem
      setNewContent(newGroups, newSelectedItem);
    }
	}

	async function moveItem(groupId: Id, fromIndex:number, direction: string) {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const groupIndex = currentGroups.findIndex((grp:Group) => grp.id === groupId);
    const currentGroup = currentGroups[groupIndex]
    const currentItems = currentGroup.items;

    let toIndex = 0;
    if(direction == 'left') { toIndex = fromIndex-1}
    if(direction == 'right') { toIndex = fromIndex+1}
    if(direction == 'end') { toIndex = currentItems.length-1}
    
    if(toIndex !== fromIndex && toIndex >= 0 && toIndex < currentItems.length) {
      const currentItem = JSON.parse(JSON.stringify(currentItems[fromIndex]));
      let newSelectedItem = {id: currentItem.id, item: currentItem};

      const [itemToMove] = currentItems.splice(fromIndex, 1);
      currentItems.splice(toIndex, 0, itemToMove);
      
      const reorderData = await reorderItems(currentItems, (fromIndex < toIndex ? fromIndex : toIndex), currentItem.groupId, newSelectedItem);
      currentGroup.items = reorderData.items;
      newSelectedItem = reorderData.selectedItem
      setNewContent(currentGroups, newSelectedItem);
    }
	}

	async function deleteGroup(id:Id) {
    const groupPosition = groups.findIndex((grp:Group) => grp.id == id);
    const groupIndex = groups[groupPosition].index;
		const filteredGroups = groups.filter((grp:Group) => grp.id !== id);
    
    const reorderData = await reorderGroups(filteredGroups, groupIndex, {id:''});
    let newGroups = reorderData.groups;
		setNewContent(newGroups, {id:''});
	}

	function createNewItem(groupId: Id) {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const groupIndex = currentGroups.findIndex((grp:Group) => grp.id === groupId);
    const currentGroup = currentGroups[groupIndex];

    const newId = `${groupId}-item-${currentGroup.items.length}`;
    let type = 'Input';
    let newProperties = getDefaults(type);

		const itemToAdd: Item = {
			id: newId,
      key: newProperties.key,
			index: currentGroup.items.length,
			groupId,
			content: '',
			properties: newProperties,
		}
    currentGroup.items.push(itemToAdd);

    const newSelectedItem = {id: newId, item: itemToAdd};
    setNewContent(currentGroups, newSelectedItem);
	}

	async function cloneItem(item: Item) {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const groupIndex = currentGroups.findIndex((grp:Group) => grp.id === item.groupId);
    const currentGroup = currentGroups[groupIndex];

    const newId = `${item.groupId}-item-${currentGroup.items.length}`;
    let newProperties = item.properties ?? getDefaults('Input');
    if(newProperties.key) {
      let guid = crypto.randomUUID().toUpperCase();
      newProperties.key = guid;
    }
    if(newProperties.text) { newProperties.text = newProperties.type; }
    if(newProperties.label) { newProperties.label = newProperties.type; }
		const itemToAdd: Item = {
			id: newId,
      key: newProperties.key,
			index: currentGroup.items.length,
			groupId: item.groupId,
			content: item.content ?? '',
			properties: newProperties,
		}
    //currentGroup.items.push(itemToAdd);
    currentGroup.items.splice(item.index+1, 0, itemToAdd);
    let updatedItems = currentGroup.items;
    let newSelectedItem = {id: newId, item: itemToAdd};

    const reorderData = await reorderItems(updatedItems, item.index+1, item.groupId, newSelectedItem);
    currentGroup.items = reorderData.items;
    newSelectedItem = reorderData.selectedItem;
    setNewContent(currentGroups, newSelectedItem);
	}

	async function deleteItem(item: Item) {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const groupIndex = currentGroups.findIndex((grp:Group) => grp.id === item.groupId);
    const currentGroup = currentGroups[groupIndex];
    const itemPosition = currentGroup.items.findIndex((itm:Item) => itm.id == item.id);
    const itemIndex = currentGroup.items[itemPosition].index;
    const filteredItems = currentGroup.items.filter((itm:Item) => itm.id !== item.id);
    
    const reorderData = await reorderItems(filteredItems, itemIndex, item.groupId, {id:''});
    let updatedItems = reorderData.items;

    currentGroup.items = updatedItems;
    setNewContent(currentGroups, {id:''});
	}

  useEffect(() => {
    const { id, group, item } = selectedItem;
    let newSelectedItem = JSON.parse(JSON.stringify(selectedItem));
    if(!(group || item)) {
      newSelectedItem = {id: '', template: {details: templateDetails, properties: templateProperties, content: templateContent }};
    }
    
    if(group) {
      const index = groups.findIndex((grp:Group) => grp.id === id);
      newSelectedItem.group = groups[index];
    }
    
    if(item) {
      const index = items.findIndex((itm:Item) => itm.id === id);
      newSelectedItem.item = items[index];
    }
    setSelectedItem(newSelectedItem);
  }, [historyIndex, templateDetails]);

  function handleUndoClick() {
    setHistoryIndex(historyIndex-1);
  }
  function handleRedoClick() {
    setHistoryIndex(historyIndex+1);
  }

  function handleUpdateContent(content:any, newSelectedItem?:SelectedItem) {
    const currentHistory = JSON.parse(JSON.stringify(templateHistory));
    const currentTemplate = JSON.parse(JSON.stringify(templateHistory[historyIndex]));
    currentTemplate.content = content;
    currentTemplate.style = currentTemplate.style ?? {};
    const updatedHistory = currentHistory.slice(0,historyIndex+1);
    updatedHistory.push(currentTemplate);
    
    if(newSelectedItem) {
      setSelectedItem(newSelectedItem);
    }
    setTemplateHistory(updatedHistory);
    setHistoryIndex(historyIndex+1);
    if(formTemplate?.data?.isPublished || currentVersionId != latestVersionId) {
      createRevision(currentTemplate);
    }
    else {
      storeUpdatedTemplate(currentTemplate);
    }
  }

  const updateGroup = (group: Group) => {
    if(group.questions.length <= 0) {
      if(group.properties && group.properties.questionheaderdisplay) {
        group.properties.questionheaderdisplay = 'None';
      }
    }
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const activeIndex = currentGroups.findIndex((grp:Group) => grp.id === group.id);
    currentGroups[activeIndex] = group;
    setNewContent(currentGroups);
  }

  const updateItem = (item: Item) => {
    const currentItems = JSON.parse(JSON.stringify(items));
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const activeIndex = currentItems.findIndex((itm:Item) => itm.id === item.id);
    const itemGroupIndex = currentGroups.findIndex((grp:Group) => grp.id === item.groupId);
    currentItems[activeIndex] = item;
    currentItems[activeIndex].key = item.properties.key;

    currentGroups[itemGroupIndex].items = currentItems.filter((itm:Item) => itm.groupId === item.groupId);
    setNewContent(currentGroups);
  }

  const replaceItem = (item: Item, type: any) => {
    const currentItems = JSON.parse(JSON.stringify(items));
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const itemIndex = currentItems.findIndex((itm:Item) => itm.id === item.id);
    const activeItem = currentItems[itemIndex];
    const itemGroupIndex = currentGroups.findIndex((grp:Group) => grp.id === activeItem.groupId);
    let properties = getDefaults(type);

    activeItem.properties = combineProperties(properties, item.properties);
    activeItem.key = activeItem.properties.key;
    
    currentItems[itemIndex] = activeItem;
    currentGroups[itemGroupIndex].items = currentItems.filter((itm:Item) => itm.groupId === activeItem.groupId);
    setNewContent(currentGroups);
  }

  const exportFormJSON = () => {
    const currentTemplate = templateHistory[historyIndex];
    const jsonString = JSON.stringify(currentTemplate, null, 2);
    const file = new Blob([jsonString], {type: 'application/json'});
    const url = URL.createObjectURL(file);

    const a = document.createElement('a');
    a.href = url;
    a.download = `${templateDetails.name}-${templateDetails.major}.${templateDetails.minor}.${templateDetails.revision}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  const importFormJSON = () => {
		jsonFileInputRef?.current?.click();
  }

  const handleJSONFileSelection = (event:any) => {
    const file = event.target.files[0];
    if(file.type !== 'application/json') {
      toast.error('File must be of type JSON.');
      return;
    }
    const reader = new FileReader();
    reader.onload = (e:any) => {
      const jsonData = JSON.parse(e.target.result);

      setNewContent(jsonData.content.groups, {id:''});

      if(jsonFileInputRef.current) {
        jsonFileInputRef.current.value = '';
      }
      toast.success('File successfully imported.');
    }
    
    reader.onerror = (err) => {
      console.log(err);
      if(jsonFileInputRef.current) {
        jsonFileInputRef.current.value = '';
      }
      toast.error('Importing file failed.');
    }
    reader.readAsText(file, 'UTF-8');
  }

  const selectHistoricalRecord = async (id: Id) => {
		try {
			const res = await queryClient.fetchQuery({
				queryKey: ['form-definition'],
				queryFn: () => getRevision(id)
			});
			if (res?.status == 200 || res?.status == 201) {
        if(formTemplate && formTemplate.data) {
          formTemplate.data = res.data;
          setRefreshCount(refreshCount+1);
        }
			}
		} catch (err: any) {
			toast.error(err.message);
		}
  }

  return (
      <div className="w-full">
        <div className="sticky w-full top-0 z-30 agBreakup2 bg-top px-10 2xl:px-16">
          <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe] pb-1 pt-4 ">
            <div className="">
              <h6 className="pt-2 font-bold text-white text-[32px]">
                Template Designer
              </h6>
            </div>

            <div className="">
              <h6 className="pt-2 font-bold text-white text-[32px] text-right">
                Forms
              </h6>
            </div>
          </div>
        </div>

        <div className="mt-4 px-10 2xl:px-16">
          <div>
              {isLoading ? (
                <div>
                  <div>
                    <div className="flex justify-center items-center h-full pt-[200px] white">
                      {<Loader />}
                    </div>
                    <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                      Loading....
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex justify-between my-2">
                    <h6 className="font-semibold text-[20px] text-white">{templateDetails?.name}</h6>
                    <div className="float-end relative">
                      <div className="flex">
                        <div className="text-white mx-2">Show Preview</div>
                        <div
                          className={`relative w-[32px] h-[16px] rounded-full mt-1`}
                        >
                          <label className={'cursor-pointer'} title={`${showPreview ? 'Click to Hide Preview' : ' Click to Show Preview'}`}>
                            <input
                              type="checkbox"
                              tabIndex={Number('-1')}
                              className="sr-only peer"
                              checked={showPreview}
                              onChange={() => {
                                setShowPreview(!showPreview);
                              }}
                              />
                            <div
                              className={`w-full h-full bg-gray-200  bg-opacity-25 border-[0.5px] border-gray-200 outline-none peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-[162%] rtl:peer-checked:after:-translate-x-[162%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white  after:rounded-full after:h-[10px] after:w-[10px] after:my-[1px] after:mx-[2px]  after:transition-all  peer-checked:bg-[#96FB60] peer-checked:bg-opacity-25  peer-checked:border-[1px] peer-checked:border-[#96FB60]`}
                            ></div>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={`grid gap-4 grid-container relative
                    ${showPreview ? 'grid-container-wide' : 'grid-container'}
                  `}>
                    <div className={`
                        grid-item p-0 col-span-2 col-start-1
                        grid-item-form-area overflow-y-auto
                        ${!showPreview ? 'hover:ring' : ''}
                        ${selectedItem.template && !showPreview ? 'selected' : '' }
                        ${showPreview ? 'grid-item-preview' : 'grid-item cursor-pointer'}
                      `}
                      onClick={() => {
                        if(!showPreview) {
                          setSelectedItem({id: '', template: {details: templateDetails, properties: templateProperties, content: templateContent }});
                        }
                      }}>
                      <FormPreview
                        showPreview={showPreview}
                        groups={groups}
                        items={items}
                        createNewGroup={createNewGroup}
						            moveGroup={moveGroup}
                        moveItem={moveItem}
                        deleteGroup={deleteGroup}
                        createNewItem={createNewItem}
                        cloneGroup={cloneGroup}
                        cloneItem={cloneItem}
                        deleteItem={deleteItem}
                        selectedItem={selectedItem}
                        setSelectedItem={setSelectedItem}
                      />
                    </div>
                    {!showPreview &&
                      <div className="col-span-1 col-start-3 p-4 grid-item grid-item-properties overflow-y-auto relative">
                        {selectedItem.id && selectedItem.id.toString().length > 0 &&
                          <div className="w-full flex justify-end absolute right-4 top-2">
                            <button
                              onClick={(e) => setSelectedItem({id: '', template: {details: templateDetails, properties: templateProperties, content: templateContent }})}
                              className="opacity-60 hover:opacity-100 p-0 m-0 h-3 w-3">
                              <CloseIcon className="h-5 w-5" />
                            </button>
                          </div>
                        }
                        <div className="flex justify-evenly">
                          <FormToolButton label="Undo" icon={<UndoIcon />} isActive={canUndoButton} handleClick={handleUndoClick} />
                          <FormToolButton label="Redo" icon={<RedoIcon />} isActive={canRedoButton} handleClick={handleRedoClick} />
                        </div>
                        <input
                          ref={jsonFileInputRef}
                          className="hidden"
                          type="file"
                          accept=".json"
                          id="jsonFileInput"
                          name="uploadedJSONFile"
                          onChange={handleJSONFileSelection}
                        />
                        <FormProperties
                          currentRevisionId={templateDetails.definitionId}
                          selectedItem={selectedItem}
                          siblings={siblings}
                          updateGroup={updateGroup}
                          updateItem={updateItem}
                          replaceItem={replaceItem}
                          publishActive={publishActive}
                          setPublishActive={setPublishActive}
                          exportFormJSON={exportFormJSON}
                          importFormJSON={importFormJSON}
                          selectHistoricalRecord={selectHistoricalRecord}
                          createRevision={createRevision}
                        />
                      </div>
                    }
                  </div>
                </>
              )}
            </div>
        </div>
      </div>
  );
};

export default FormDesigner;
