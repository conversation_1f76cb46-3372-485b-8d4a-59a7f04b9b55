import { Test, TestingModule } from '@nestjs/testing';
import { FormTemplateDefinitionsService } from './form_template_definitions.service';
import { FormTemplateDefinition } from './entities/form_template_definitions.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CreateFormTemplateDefinitionDto } from './dto/create-form-template-definitions.dto';
import { UpdateFormTemplateDefinitionDto } from './dto/update-form-template-definitions.dto';

describe('FormTemplateDefinitionsService', () => {
  let service: FormTemplateDefinitionsService;

  const mockFormTemplateDefinitionRepository = {
    save: jest.fn(),
    find: jest.fn(),
    findOneBy: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FormTemplateDefinitionsService,
        {
          provide: getRepositoryToken(FormTemplateDefinition),
          useValue: mockFormTemplateDefinitionRepository,
        },
      ],
    }).compile();

    service = module.get<FormTemplateDefinitionsService>(FormTemplateDefinitionsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('create => Should create a new formTemplateDefinition and return its data', async () => {
    // arrange
    const createFormTemplateDefinitionDto = {
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    } as CreateFormTemplateDefinitionDto;

    const formTemplateDefinition = {
      id: 1,
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    } as FormTemplateDefinition;

    jest.spyOn(mockFormTemplateDefinitionRepository, 'save').mockReturnValue(formTemplateDefinition);

    // act
    const result = await service.create(createFormTemplateDefinitionDto);

    // assert
    expect(mockFormTemplateDefinitionRepository.save).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionRepository.save).toHaveBeenCalledWith(createFormTemplateDefinitionDto);

    expect(result).toEqual(formTemplateDefinition);
  });

  it('findAll => should return an array of formTemplateDefinitions', async () => {
    // arrange
    const formTemplateDefinition = {
      id: 1,
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    };
    let mineId = 1;
    const formTemplateDefinitions = [formTemplateDefinition];
    jest.spyOn(mockFormTemplateDefinitionRepository, 'find').mockReturnValue(formTemplateDefinitions);

    // act
    const result = await service.findAll(mineId);

    // assert
    expect(mockFormTemplateDefinitionRepository.find).toHaveBeenCalled();
    expect(result).toEqual(formTemplateDefinitions);
  });

  it('findOne => should find a formTemplateDefinition by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const formTemplateDefinition = {
      id: 1,
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    };

    jest.spyOn(mockFormTemplateDefinitionRepository, 'findOneBy').mockReturnValue(formTemplateDefinition);

    // act
    const result = await service.findOne(id);

    // assert
    expect(mockFormTemplateDefinitionRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(result).toEqual(formTemplateDefinition);
  });

  it('update => should find a formTemplateDefinition by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateFormTemplateDefinitionDto = {
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    } as UpdateFormTemplateDefinitionDto;
    const formTemplateDefinition = {
      id: 1,
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
      isPublished: null,
      publishedAt: null,
      publishedBy: null
    };

    const updatedFormTemplateDefinition = Object.assign(formTemplateDefinition, updateFormTemplateDefinitionDto);
    jest.spyOn(mockFormTemplateDefinitionRepository, 'save').mockReturnValue(updatedFormTemplateDefinition);

    // act
    const result = await service.update(id, formTemplateDefinition);

    // assert
    expect(mockFormTemplateDefinitionRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockFormTemplateDefinitionRepository.save).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionRepository.save).toHaveBeenCalledWith(updatedFormTemplateDefinition);
    expect(result).toEqual(updatedFormTemplateDefinition);
  });

  it('remove => should find a formTemplateDefinition by a given id, remove and then return Number of affected rows', async () => {
    // arrange
    const id = 1;
    const formTemplateDefinition = {
      id: 1,
      mineId: 1,
      formTemplateId: 1,
      definition: '{}',
      major: 1,
      minor: 0,
      revision: 0,
    };

    jest.spyOn(mockFormTemplateDefinitionRepository, 'remove').mockReturnValue(formTemplateDefinition);

    // act
    const result = await service.remove(id);

    // assert
    expect(mockFormTemplateDefinitionRepository.findOneBy).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockFormTemplateDefinitionRepository.remove).toHaveBeenCalled();
    expect(mockFormTemplateDefinitionRepository.remove).toHaveBeenCalledWith(id);
    expect(result).toEqual(formTemplateDefinition);
  });
});
