import React from 'react';

interface ProgressBarProps {
  value: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ value }) => {
  const absValue = Math.abs(value);
  const width = absValue > 100 ? 100 / 2 : absValue / 2;
  const leftPosition = value < 0 ? 50 - width / 2 : 50 + width / 2;
  const colorClass = value < 0 ? 'bg-[#FE4A6A]' : 'bg-[#96FB60]';
  const directionClass = value < 0 ? 'right-auto' : 'left-auto';

  return (
    <div className="h-2 xl:w-40 2xl:w-64 bg-[#FFFFFF]/20 rounded-full relative">
      <div
        className={`h-full rounded-full absolute top-0 ${colorClass} ${directionClass}`}
        style={{
          width: `${width}%`,
          left: `${leftPosition}%`,
          transform: 'translateX(-50%)',
        }}
      ></div>
    </div>
  );
};

export default ProgressBar;
