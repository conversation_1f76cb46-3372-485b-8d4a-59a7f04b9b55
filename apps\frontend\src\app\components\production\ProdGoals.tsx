import { useEffect, useState } from 'react';
import Table, { ColumnDef } from '../common/Table';
import Modal from '../common/Modal';
import { useQuery } from '@tanstack/react-query';
import { getGoals } from '../../../api/goals/goalapis';
import { getShifts } from '../../../api/shifts/shiftapis';
import { getSections } from '../../../api/sections/sectionapis';
import {
  useAddGoal,
  useEditGoal,
  useDeleteGoal,
} from '../../../services/mutations/goalmutations';
import decodeJWT from '../../../utils/jwtdecoder';
import { escapeRegExp } from '../../../utils/constant';
import { EditIcon, TrashIcon } from '../../../assets/icons/icons';
import { Tooltip } from '@material-tailwind/react';
import { toast } from 'react-toastify';

export default function Company() {
  const [currentGoal, setCurrentGoal] = useState<any>();
  const [currentDeleteGoal, setCurrentDeleteGoal] = useState<any>();
  const [goalRowData, setGoalRowData] = useState<any[]>([]);
  const [openDeleteModal, setOpenDeteteModal] = useState(false);
  const [searchPh, setSearchPh] = useState('');

  const addGoal = useAddGoal();
  const editGoal = useEditGoal();
  const deleteGoal = useDeleteGoal();

  const { data: goalData } = useQuery({
    queryKey: ['goals'],
    queryFn: getGoals,
    refetchOnWindowFocus: false,
  });

  const { data: shiftData } = useQuery({
    queryKey: ['shifts'],
    queryFn: getShifts,
    refetchOnWindowFocus: false,
  });

  const { data: sectionData } = useQuery({
    queryKey: ['sections'],
    queryFn: getSections,
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    let goalRows = [];
    if (goalData?.data.length > 0) {
      goalRows = goalData?.data?.map((row: any) => {
        let goal = row ?? {};
        let section = sectionData?.data?.find(
          (section: any) => section?.id === row?.sectionId
        );
        goal.section = section ?? null;
        let shift = shiftData?.data?.find(
          (shift: any) => shift?.id === row?.shiftId
        );
        goal.shift = shift ?? null;
        return goal;
      });
    }

    setGoalRowData(goalRows);
  }, [goalData, shiftData, sectionData]);

  const handleGoalChange = (e: any) => {
    e.preventDefault();

    if (e.currentTarget.name === 'shift') {
      setCurrentGoal({ ...currentGoal, shiftId: e.currentTarget.value });
    }

    if (e.currentTarget.name === 'section') {
      setCurrentGoal({ ...currentGoal, sectionId: e.currentTarget.value });
    }

    if (e.currentTarget.name === 'goal') {
      setCurrentGoal({ ...currentGoal, goal: e.currentTarget.value });
    }
  };

  const handleAddNewGoal = () => {
    const newGoal = {
      id: -1,
      sectionId: null,
      shiftId: null,
      effectiveDate: new Date().toISOString(),
    };
    setGoalRowData([...goalRowData, newGoal]);
    setCurrentGoal(newGoal);
  };

  const handleSaveGoal = async () => {
    if (!currentGoal.sectionId) {
      toast.error('Please select a Working Section');
      return;
    }
    if (!currentGoal.shiftId) {
      toast.error('Please select a Shift');
      return;
    }
    if (!currentGoal.goal) {
      toast.error('Please enter a Production Goal');
      return;
    }

    const goalDate = currentGoal?.effectiveDate
      ? new Date(currentGoal.effectiveDate).toISOString()
      : new Date().toISOString();
    const today = new Date().toISOString();
    const span = new Date(today).valueOf() - new Date(goalDate).valueOf();
    const spanDays = span / 1000 / 60 / 60 / 24;

    if (currentGoal.id === -1 || spanDays > 1) {
      delete currentGoal.id;
      currentGoal.effectiveDate = new Date().toISOString();

      await addGoal.mutateAsync(currentGoal);
      toast.success('You have successfully added a new goal');
    } else {
      await editGoal.mutateAsync(currentGoal);
      toast.success('You have successfully edited the goal');
    }
    setCurrentGoal(null);
  };

  const handleCancelSaveGoal = () => {
    if (currentGoal.id === -1) {
      setGoalRowData(goalRowData.filter((row) => row.id !== -1));
    }

    setCurrentGoal(null);
  };

  const handleDeleteGoal = async () => {
    await deleteGoal.mutateAsync(currentDeleteGoal?.id);
    setOpenDeteteModal(!openDeleteModal);
    setCurrentDeleteGoal(null);
  };

  const handleCancelDelete = () => {
    setOpenDeteteModal(!openDeleteModal);
    setCurrentDeleteGoal(null);
  };

  const regex = new RegExp(`(${escapeRegExp(searchPh)})`, 'i');
  const columns: ColumnDef[] = [
    {
      key: 'workingSection',
      label: 'Working Section',
      type: 'text',
      render: (row) => (
        <div>
          {currentGoal?.id === row?.id ? (
            <select
              className={
                'block w-full p-[7px]  rounded bg-gray-200  focus:border-blue-500 text-[14px] text-black'
              }
              defaultValue={row.sectionId}
              onChange={handleGoalChange}
              name="section"
            >
              <option value={''}>Select Section</option>
              {sectionData?.data?.map((section: any) => (
                <option value={section?.id} key={section?.id}>
                  {section.sectionName.charAt(0).toUpperCase() +
                    section.sectionName.slice(1)}
                </option>
              ))}
            </select>
          ) : (
            <span>{row?.section?.sectionName ?? ''}</span>
          )}
        </div>
      ),
    },
    {
      key: 'shift',
      label: 'Shift',
      type: 'text',
      render: (row) => (
        <div>
          {currentGoal?.id === row?.id ? (
            <select
              className={
                'block w-full p-[7px]  rounded bg-gray-200  focus:border-blue-500 text-[14px] text-black'
              }
              defaultValue={row.shiftId}
              onChange={handleGoalChange}
              name="shift"
            >
              <option value={''}>Select Shift</option>
              {shiftData?.data?.map((shift: any) => (
                <option value={shift?.id} key={shift?.id}>
                  {shift.shiftName.charAt(0).toUpperCase() +
                    shift.shiftName.slice(1)}
                </option>
              ))}
            </select>
          ) : (
            <span>{row?.shift?.shiftName ?? ''}</span>
          )}
        </div>
      ),
    },
    {
      key: 'goal',
      label: 'Production Goal (feet)',
      type: 'text',
      render: (row) => (
        <div>
          {currentGoal?.id === row?.id ? (
            <input
              className={
                'block w-full p-[7px]  rounded bg-gray-200  focus:border-blue-500 text-[14px] text-black'
              }
              type="number"
              defaultValue={row.goal}
              onChange={handleGoalChange}
              name="goal"
            />
          ) : (
            <span>{row.goal}</span>
          )}
        </div>
      ),
    },
    {
      key: 'action',
      label: 'Actions',
      type: 'element',
      render: (row) => (
        <div className="flex justify-center">
          {row?.id === currentGoal?.id ? (
            <div>
              <button
                id="save_new_goal"
                title="Click to save goal"
                className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-4 py-1 text-center items-center mx-1"
                onClick={() => {
                  handleSaveGoal();
                }}
              >
                Save
              </button>
              <button
                id="cancel_new_goal"
                title="Click to cancel saving"
                className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-4 py-1 text-center items-center mx-2"
                onClick={() => {
                  handleCancelSaveGoal();
                }}
              >
                Cancel
              </button>
            </div>
          ) : (
            <>
              <Tooltip content="Edit goal" arrow={false}>
                <div
                  className="cursor-pointer"
                  data-tooltip-target="tooltip-default"
                  onClick={() => {
                    setCurrentGoal(row);
                  }}
                >
                  <EditIcon className="text-white font-black h-5 w-5 mx-2 " />
                </div>
              </Tooltip>

              <Tooltip content="Delete goal" arrow={false}>
                <div
                  className="cursor-pointer"
                  onClick={() => {
                    setCurrentGoal(null);
                    setCurrentDeleteGoal(row);
                    setOpenDeteteModal(true);
                  }}
                >
                  <TrashIcon className="text-white font-black text-[14px] h-5 w-5 mx-2 " />
                </div>
              </Tooltip>
            </>
          )}
        </div>
      ),
    },
  ];

  if (decodeJWT()?.role !== 'manager') {
    return '';
  }

  return (
    <div className="w-full">
      <div className="sticky w-full top-0 z-30 box bg-top px-10 2xl:px-16">
        <div className="grid grid-cols-1 border-b-[1px] border-[#80c2fe] pb-4 pt-4">
          <div className="">
            <h6 className="p-2 font-bold text-white text-[32px] text-right">
              Goal Management
            </h6>
          </div>
        </div>
      </div>
      <div className="mt-8 px-10 2xl:px-16">
        <div>
          <div className="pt-8 pb-2 prodGoalsTableBg px-5 rounded-xl">
            <div className="flex justify-between">
              <div className="block">
                <h6 className="font-semibold text-[20px] text-white">
                  Production Goals
                </h6>
                <p className="font-normal my-1 text-[14px] tracking-normal leading-5 text-[#cccccc] ">
                  Manage existing production goals
                </p>
              </div>
              <div>
                <button
                  id="add_new_goal"
                  title="Click to add goal"
                  className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center  items-center  me-2 mb-2"
                  onClick={() => {
                    handleAddNewGoal();
                  }}
                >
                  Add New Goal
                </button>
              </div>
            </div>
            <div className="mt-5">
              <Table
                columns={columns}
                data={goalRowData ?? []}
                searchText={searchPh}
                sortable={false}
                scrollable={true}
                separateLine={true}
                dataRenderLimitMdScreen={8}
                dataRenderLimitLgScreen={15}
                tableHeightClassLg="tableheightForlg"
                tableHeightClassMd="tableheightFormd"
              />
            </div>
            {openDeleteModal ? (
              <Modal
                Content={
                  <div className="p-4">
                    <div className="text-[24px] text-white text-center ">
                      Are you sure you want to delete?
                    </div>
                    <div className="my-2 text-[56px] text-[#4AA8FE] text-center text-provima text-ellipsis overflow-hidden m-auto"></div>
                    <div className="mt-2 text-center">
                      <button
                        title="Click to Cancel"
                        onClick={() => handleCancelDelete()}
                        className="my-2 bg-black hover:bg-[#4AA8FE] text-white hover:text-black text-[14px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE] mr-2"
                      >
                        Cancel
                      </button>
                      <button
                        title="Click to delete goal"
                        onClick={async (e: any) => {
                          e.preventDefault();
                          handleDeleteGoal();
                        }}
                        className="my-2 bg-[#4AA8FE] hover:bg-black hover:text-white text-black text-[14px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                      >
                        Yes, Permanently Delete
                      </button>
                    </div>
                  </div>
                }
              />
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
