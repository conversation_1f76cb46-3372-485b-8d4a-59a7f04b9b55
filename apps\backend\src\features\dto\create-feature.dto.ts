import {
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
export class CreateFeatureDto {
  @IsString({ message: 'Name must be a string' })
  @Length(1, 255, {
    message: 'Name length must be between 1 and 100 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsString({ message: 'Code must be a string' })
  @Length(1, 50, {
    message: 'Code length must be between 1 and 50 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  code: string;

  @IsNumber()
  @ApiProperty()
  categoryId: number;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
