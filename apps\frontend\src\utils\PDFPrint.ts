export const doPrint = (tagName: string, isLandscape: boolean = true) => {
  const container = document.getElementById(tagName);
  const stylesheetElems = document.head.querySelectorAll(
    'link[rel="stylesheet"]'
  );
  const styleElems = document.head.querySelectorAll('style');
  let stylesheetHtml = '';
  stylesheetElems.forEach((elem) => (stylesheetHtml += elem.outerHTML));
  styleElems.forEach((elem) => (stylesheetHtml += elem.outerHTML));
  const element = document.querySelector('.minerNameHide');

  const printContent = `
    <html>
      <head>
        ${stylesheetHtml}
        ${
          isLandscape
            ? '<style>@media print{@page {size: landscape}}</style>'
            : ''
        }
      </head>
      <body class='box'>
        ${container?.outerHTML
          ?.replace('minerNameHide', '')
          ?.replace('tableheightFormd', '')
          ?.replace('tableheightForlg', '')}
      </body>
    </html>
  `;
  const printContainer = document.createElement('div');

  printContainer.innerHTML = printContent;
  document.title = `${`Report_${
    element ? element?.textContent?.trim() : ''
  }_${new Date()}`}`;
  const oldHTml = document.body.innerHTML;
  document.body.innerHTML = '';
  document.body.className = 'box';
  document.body.appendChild(printContainer);
  window.print();

  // Detect print dialog closure
  const checkPrintDialogClosed = () => {
    if (window && window.closed) {
      // Print dialog was closed
      // Here you can infer that the print dialog was closed
      return true;
    }
    return false;
  };

  const interval = setInterval(() => {
    if (checkPrintDialogClosed()) {
      clearInterval(interval);
      document.title = 'IWT-AP';
      window.location.reload();
    }
  }, 1000);

  // Fallback if onafterprint is not supported
  window.addEventListener('afterprint', () => {
    clearInterval(interval);
    document.title = 'IWT-AP';
    window.location.reload();
    //window.close();
  });
};
