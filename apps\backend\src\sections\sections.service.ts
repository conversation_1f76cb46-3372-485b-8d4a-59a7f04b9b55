import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Section } from './entities/section.entity';
import { Repository } from 'typeorm';

@Injectable()
export class SectionsService {
  constructor(
    @InjectRepository(Section)
    private sectionRepository: Repository<Section>
  ) {}

  async findAll(mineId: number) {
    return this.sectionRepository.find({
      where: {
        mineId: mineId,
        isActive: 1
      },
    });
  }

  async findOne(id: number) {
    return this.sectionRepository.findOneBy({ id });
  }
}
