{"name": "backend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/backend/src", "projectType": "application", "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "backend:build"}, "configurations": {"development": {"buildTarget": "backend:build:development"}, "production": {"buildTarget": "backend:build:production"}}}}, "tags": []}