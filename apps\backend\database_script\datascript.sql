SET IDENTITY_INSERT [dbo].[companies] ON 

INSERT [dbo].[companies] ([id], [name], [code], [created_at], [created_by], [updated_at], [updated_by]) VALUES (1, N'Platform', N'Platform', CAST(N'2024-02-20T15:52:39.9133333' AS DateTime2), NULL, CAST(N'2024-02-20T15:52:39.9133333' AS DateTime2), NULL)
SET IDENTITY_INSERT [dbo].[companies] OFF
SET IDENTITY_INSERT [dbo].[roles] ON 

INSERT [dbo].[roles] ([id], [name], [company_id], [created_at], [created_by], [updated_at], [updated_by]) VALUES (1, N'superuser', 1, CAST(N'2024-02-20T15:53:48.5333333' AS DateTime2), NULL, CAST(N'2024-02-20T15:53:48.5333333' AS DateTime2), NULL)
SET IDENTITY_INSERT [dbo].[roles] OFF
SET IDENTITY_INSERT [dbo].[categories] ON 

INSERT [dbo].[categories] ([id], [name], [description], [created_at], [created_by], [updated_at], [updated_by]) VALUES (1, N'Assets', N'Managing assets effectively is essential for maximizing their value and minimizing risks. This includes tracking and maintaining physical assets such as equipment, machinery, and vehicles.', CAST(N'2024-02-20T00:01:17.2700000' AS DateTime2), 1, CAST(N'2024-02-20T00:01:17.2700000' AS DateTime2), NULL)
INSERT [dbo].[categories] ([id], [name], [description], [created_at], [created_by], [updated_at], [updated_by]) VALUES (2, N'Checkins', N'Managing assets effectively is essential for maximizing their value and minimizing risks. This includes tracking and maintaining physical assets such as equipment, machinery, and vehicles.', CAST(N'2024-02-20T00:01:25.6966667' AS DateTime2), 1, CAST(N'2024-02-20T00:01:25.6966667' AS DateTime2), NULL)
INSERT [dbo].[categories] ([id], [name], [description], [created_at], [created_by], [updated_at], [updated_by]) VALUES (3, N'Reports', N'Managing assets effectively is essential for maximizing their value and minimizing risks. This includes tracking and maintaining physical assets such as equipment, machinery, and vehicles.', CAST(N'2024-02-20T00:01:33.8633333' AS DateTime2), 1, CAST(N'2024-02-20T00:01:33.8633333' AS DateTime2), NULL)
SET IDENTITY_INSERT [dbo].[categories] OFF
SET IDENTITY_INSERT [dbo].[features] ON 

INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by]) VALUES (1, N'Dashboard', N'DASH', 1, CAST(N'2024-02-20T00:04:30.5366667' AS DateTime2), 1, CAST(N'2024-02-20T00:04:30.5366667' AS DateTime2), NULL)
INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by]) VALUES (2, N'Location', N'LOC', 2, CAST(N'2024-02-20T00:04:47.6833333' AS DateTime2), 1, CAST(N'2024-02-20T00:04:47.6833333' AS DateTime2), NULL)
INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by]) VALUES (3, N'Production', N'PROD', 2, CAST(N'2024-02-20T00:04:57.9700000' AS DateTime2), 1, CAST(N'2024-02-20T00:04:57.9700000' AS DateTime2), NULL)
INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by]) VALUES (4, N'E-Form', N'E-FORM', 3, CAST(N'2024-02-20T00:05:16.5000000' AS DateTime2), 1, CAST(N'2024-02-20T00:05:16.5000000' AS DateTime2), NULL)
INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by]) VALUES (5, N'Setting', N'SET', 3, CAST(N'2024-02-20T00:05:31.8766667' AS DateTime2), 1, CAST(N'2024-02-20T00:05:31.8766667' AS DateTime2), NULL)
INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by]) VALUES (6, N'Compliance', N'COMP', 1, CAST(N'2024-02-20T00:05:46.7700000' AS DateTime2), 1, CAST(N'2024-02-20T00:05:46.7700000' AS DateTime2), NULL)
SET IDENTITY_INSERT [dbo].[features] OFF
SET IDENTITY_INSERT [dbo].[role_feature_access] ON 

INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (1, 1, 1, 1, 1, CAST(N'2024-02-20T15:54:46.0133333' AS DateTime2), NULL, CAST(N'2024-02-20T15:54:46.0133333' AS DateTime2), NULL)
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (2, 1, 2, 1, 1, CAST(N'2024-02-20T15:54:55.4733333' AS DateTime2), NULL, CAST(N'2024-02-20T15:54:55.4733333' AS DateTime2), NULL)
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (3, 1, 3, 1, 1, CAST(N'2024-02-20T15:55:00.6700000' AS DateTime2), NULL, CAST(N'2024-02-20T15:55:00.6700000' AS DateTime2), NULL)
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (4, 1, 4, 1, 1, CAST(N'2024-02-20T15:55:05.8600000' AS DateTime2), NULL, CAST(N'2024-02-20T15:55:05.8600000' AS DateTime2), NULL)
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (5, 1, 5, 1, 1, CAST(N'2024-02-20T15:55:13.1300000' AS DateTime2), NULL, CAST(N'2024-02-20T15:55:13.1300000' AS DateTime2), NULL)
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (6, 1, 6, 1, 1, CAST(N'2024-02-20T15:55:18.7700000' AS DateTime2), NULL, CAST(N'2024-02-20T15:55:18.7700000' AS DateTime2), NULL)
SET IDENTITY_INSERT [dbo].[role_feature_access] OFF
SET IDENTITY_INSERT [dbo].[users] ON 

INSERT [dbo].[users] ([id], [username], [company_id], [password], [first_name], [last_name], [phone], [job_title], [email], [is_active],[is_delete], [created_at], [created_by], [updated_at], [updated_by]) VALUES (1, N'IwtUser', 1, N'$2b$10$7lopKF6LQ7jcpbGbquN9XOgnM41OVwgtMz8mczl4HULkk2yCjo3Fq', N'Iwt', N'User', NULL, NULL, N'<EMAIL>', 1,0, CAST(N'2024-02-20T16:02:50.3866667' AS DateTime2), NULL, CAST(N'2024-02-20T16:02:50.3866667' AS DateTime2), NULL)
SET IDENTITY_INSERT [dbo].[users] OFF

SET IDENTITY_INSERT [dbo].[users_roles] ON 

INSERT [dbo].[users_roles] ([id], [user_id], [role_id], [is_active], [created_at], [created_by], [updated_at], [updated_by]) VALUES (1, 1, 1, 1, CAST(N'2024-02-20T17:46:00.1166667' AS DateTime2), 1, CAST(N'2024-02-20T17:46:00.1166667' AS DateTime2), NULL)
SET IDENTITY_INSERT [dbo].[users_roles] OFF

GO
SET IDENTITY_INSERT [dbo].[sections] ON 
GO
INSERT [dbo].[sections] ([id], [mine_id], [ext_section_id], [ext_section_name], [created_at], [updated_at], [is_active]) VALUES (4, 1, 1, N'Section A', CAST(N'2024-05-08T14:51:52.9000000' AS DateTime2), CAST(N'2024-05-08T14:51:52.9000000' AS DateTime2), 1)
GO
INSERT [dbo].[sections] ([id], [mine_id], [ext_section_id], [ext_section_name], [created_at], [updated_at], [is_active]) VALUES (5, 1, 2, N'Section B', CAST(N'2024-05-08T14:52:00.0166667' AS DateTime2), CAST(N'2024-05-08T14:52:00.0166667' AS DateTime2), 1)
GO
INSERT [dbo].[sections] ([id], [mine_id], [ext_section_id], [ext_section_name], [created_at], [updated_at], [is_active]) VALUES (6, 1, 3, N'Section C', CAST(N'2024-05-08T14:52:05.5166667' AS DateTime2), CAST(N'2024-05-08T14:52:05.5166667' AS DateTime2), 1)
GO
INSERT [dbo].[sections] ([id], [mine_id], [ext_section_id], [ext_section_name], [created_at], [updated_at], [is_active]) VALUES (7, 1, 4, N'Section D', CAST(N'2024-05-08T14:52:10.7566667' AS DateTime2), CAST(N'2024-05-08T14:52:10.7566667' AS DateTime2), 1)
GO
SET IDENTITY_INSERT [dbo].[sections] OFF
GO
SET IDENTITY_INSERT [dbo].[shifts] ON 
GO
INSERT [dbo].[shifts] ([id], [ext_shift_id], [mine_id], [shift_name], [start_time], [end_time], [created_at], [updated_at], [is_active]) VALUES (1, 1, 1, N'Day Shift', CAST(N'06:00:00' AS Time), CAST(N'14:00:00' AS Time), CAST(N'2024-05-08T14:45:52.8933333' AS DateTime2), NULL, 1)
GO
INSERT [dbo].[shifts] ([id], [ext_shift_id], [mine_id], [shift_name], [start_time], [end_time], [created_at], [updated_at], [is_active]) VALUES (2, 2, 1, N'Evening Shift', CAST(N'14:00:00' AS Time), CAST(N'22:00:00' AS Time), CAST(N'2024-05-08T14:46:14.1400000' AS DateTime2), NULL, 1)
GO
INSERT [dbo].[shifts] ([id], [ext_shift_id], [mine_id], [shift_name], [start_time], [end_time], [created_at], [updated_at], [is_active]) VALUES (3, 3, 1, N'Night Shift', CAST(N'22:00:00' AS Time), CAST(N'06:00:00' AS Time), CAST(N'2024-05-08T14:49:39.4566667' AS DateTime2), NULL, 1)
GO
SET IDENTITY_INSERT [dbo].[shifts] OFF
GO
SET IDENTITY_INSERT [dbo].[production_goals] ON 
GO
INSERT [dbo].[production_goals] ([id], [section_id], [shift_id], [goal], [effective_date], [end_date], [created_at], [created_by], [updated_at], [updated_by]) VALUES (2, 4, 1, 140, N'2024-05-07T21:32:06.247Z', N'2024-05-08T21:45:08.243Z', CAST(N'2024-05-08T17:42:45.7900000' AS DateTime2), NULL, CAST(N'2024-05-08T17:45:08.2500000' AS DateTime2), NULL)
GO
INSERT [dbo].[production_goals] ([id], [section_id], [shift_id], [goal], [effective_date], [end_date], [created_at], [created_by], [updated_at], [updated_by]) VALUES (3, 5, 1, 140, N'2024-05-07T21:32:06.247Z', N'2024-05-08T21:45:17.219Z', CAST(N'2024-05-08T17:42:56.8600000' AS DateTime2), NULL, CAST(N'2024-05-08T17:45:17.2233333' AS DateTime2), NULL)
GO
INSERT [dbo].[production_goals] ([id], [section_id], [shift_id], [goal], [effective_date], [end_date], [created_at], [created_by], [updated_at], [updated_by]) VALUES (4, 4, 2, 160, N'2024-05-07T21:32:06.247Z', N'2024-05-08T21:45:49.871Z', CAST(N'2024-05-08T17:43:04.6966667' AS DateTime2), NULL, CAST(N'2024-05-08T17:45:49.8733333' AS DateTime2), NULL)
GO
INSERT [dbo].[production_goals] ([id], [section_id], [shift_id], [goal], [effective_date], [end_date], [created_at], [created_by], [updated_at], [updated_by]) VALUES (5, 5, 2, 160, N'2024-05-07T21:32:06.247Z', N'2024-05-08T21:45:54.780Z', CAST(N'2024-05-08T17:43:11.7833333' AS DateTime2), NULL, CAST(N'2024-05-08T17:45:54.7900000' AS DateTime2), NULL)
GO
INSERT [dbo].[production_goals] ([id], [section_id], [shift_id], [goal], [effective_date], [end_date], [created_at], [created_by], [updated_at], [updated_by]) VALUES (6, 4, 1, 180, N'2024-05-08T21:45:08.222Z', NULL, CAST(N'2024-05-08T17:45:08.2633333' AS DateTime2), NULL, CAST(N'2024-05-08T17:45:08.2633333' AS DateTime2), NULL)
GO
INSERT [dbo].[production_goals] ([id], [section_id], [shift_id], [goal], [effective_date], [end_date], [created_at], [created_by], [updated_at], [updated_by]) VALUES (7, 5, 1, 180, N'2024-05-08T21:45:17.207Z', NULL, CAST(N'2024-05-08T17:45:17.2300000' AS DateTime2), NULL, CAST(N'2024-05-08T17:45:17.2300000' AS DateTime2), NULL)
GO
INSERT [dbo].[production_goals] ([id], [section_id], [shift_id], [goal], [effective_date], [end_date], [created_at], [created_by], [updated_at], [updated_by]) VALUES (8, 4, 2, 200, N'2024-05-08T21:45:49.848Z', NULL, CAST(N'2024-05-08T17:43:04.6960000' AS DateTime2), NULL, CAST(N'2024-05-08T17:44:14.2630000' AS DateTime2), NULL)
GO
INSERT [dbo].[production_goals] ([id], [section_id], [shift_id], [goal], [effective_date], [end_date], [created_at], [created_by], [updated_at], [updated_by]) VALUES (9, 5, 2, 200, N'2024-05-08T21:45:54.769Z', NULL, CAST(N'2024-05-08T17:43:11.7830000' AS DateTime2), NULL, CAST(N'2024-05-08T17:44:20.6260000' AS DateTime2), NULL)
GO
SET IDENTITY_INSERT [dbo].[production_goals] OFF
GO
SET IDENTITY_INSERT [dbo].[form_categories] ON 
GO
INSERT [dbo].[form_categories] ([id], [mine_id], [name], [created_at], [created_by], [updated_at], [updated_by]) VALUES (1, 1, N'Safety', CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), 1, CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), NULL)
GO
INSERT [dbo].[form_categories] ([id], [mine_id], [name], [created_at], [created_by], [updated_at], [updated_by]) VALUES (2, 1, N'Production', CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), 1, CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), NULL)
GO
INSERT [dbo].[form_categories] ([id], [mine_id], [name], [created_at], [created_by], [updated_at], [updated_by]) VALUES (3, 1, N'Operations', CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), 1, CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), NULL)
GO
INSERT [dbo].[form_categories] ([id], [mine_id], [name], [created_at], [created_by], [updated_at], [updated_by]) VALUES (4, 1, N'Compliance', CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), 1, CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), NULL)
GO
INSERT [dbo].[form_categories] ([id], [mine_id], [name], [created_at], [created_by], [updated_at], [updated_by]) VALUES (5, 1, N'Human Resources', CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), 1, CAST(N'2025-05-15T14:51:52.9000000' AS DateTime2), NULL)
GO
SET IDENTITY_INSERT [dbo].[form_categories] OFF
GO
SET IDENTITY_INSERT [dbo].[categories] ON 
GO
INSERT [dbo].[categories] ([id], [name], [description], [created_at], [created_by], [updated_at], [updated_by]) VALUES (4, N'E-Forms', N'Managing assets effectively is essential for maximizing their value and minimizing risks. This includes tracking and maintaining physical assets such as equipment, machinery, and vehicles.', CAST(N'2024-02-20T00:01:17.2700000' AS DateTime2), 1, CAST(N'2024-02-20T00:01:17.2700000' AS DateTime2), NULL)
GO
SET IDENTITY_INSERT [dbo].[categories] OFF
GO
SET IDENTITY_INSERT [dbo].[features] ON 
GO
INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by], [sort_order]) VALUES (10, N'Templates', N'FORM', 4, CAST(N'2024-02-20T00:04:30.5366667' AS DateTime2), 1, CAST(N'2024-02-20T00:04:30.5366667' AS DateTime2), NULL, 10)
GO
INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by], [sort_order]) VALUES (11, N'Forms', N'FORM', 4, CAST(N'2024-02-20T00:04:30.5366667' AS DateTime2), 1, CAST(N'2024-02-20T00:04:30.5366667' AS DateTime2), NULL, 11)
GO
INSERT [dbo].[features] ([id], [name], [code], [category_id], [created_at], [created_by], [updated_at], [updated_by], [sort_order]) VALUES (12, N'Shifts', N'PROD', 2, CAST(N'2024-02-20T00:04:30.5366667' AS DateTime2), 1, CAST(N'2024-02-20T00:04:30.5366667' AS DateTime2), NULL, 12)
GO
SET IDENTITY_INSERT [dbo].[features] OFF
GO
SET IDENTITY_INSERT [dbo].[role_feature_access] ON 
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (10, 1, 10, 1, 1, CAST(N'2024-02-20T15:54:46.0133333' AS DateTime2), NULL, CAST(N'2024-02-20T15:54:46.0133333' AS DateTime2), NULL)
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (11, 1, 11, 1, 1, CAST(N'2024-02-20T15:54:46.0133333' AS DateTime2), NULL, CAST(N'2024-02-20T15:54:46.0133333' AS DateTime2), NULL)
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (12, 1, 12, 1, 1, CAST(N'2024-02-20T15:54:46.0133333' AS DateTime2), NULL, CAST(N'2024-02-20T15:54:46.0133333' AS DateTime2), NULL)
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (20, 2, 10, 1, 1, CAST(N'2025-01-02T20:38:42.3066667' AS DateTime2), 2, CAST(N'2025-01-02T20:38:42.3066667' AS DateTime2), NULL)
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (21, 2, 11, 1, 1, CAST(N'2025-01-02T20:38:42.3166667' AS DateTime2), 2, CAST(N'2025-01-02T20:38:42.3166667' AS DateTime2), NULL)
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (22, 2, 12, 1, 1, CAST(N'2025-01-02T20:38:42.3266667' AS DateTime2), 2, CAST(N'2025-01-02T20:38:42.3266667' AS DateTime2), NULL)
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (29, 3, 11, 1, 1, CAST(N'2025-01-02T20:38:42.3900000' AS DateTime2), 2, CAST(N'2025-01-02T20:38:42.3900000' AS DateTime2), NULL)
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (30, 3, 12, 1, 1, CAST(N'2025-01-02T20:38:42.4466667' AS DateTime2), 2, CAST(N'2025-01-02T20:38:42.4466667' AS DateTime2), NULL)
GO
INSERT [dbo].[role_feature_access] ([id], [role_id], [feature_id], [has_read_access], [has_write_access], [created_at], [created_by], [updated_at], [updated_by]) VALUES (31, 4, 11, 1, 1, CAST(N'2025-01-02T20:38:42.4466667' AS DateTime2), 2, CAST(N'2025-01-02T20:38:42.4466667' AS DateTime2), NULL)
GO
SET IDENTITY_INSERT [dbo].[role_feature_access] OFF
GO