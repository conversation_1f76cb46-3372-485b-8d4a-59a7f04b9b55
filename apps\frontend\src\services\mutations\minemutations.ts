import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  MineData,
  addMine,
  editMine,
  deleteMine,
  statusChange,
  checkMineName,
} from '../../api/mines/mineapis';

export function useAddMine() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: MineData) => addMine(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['add-mine'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['mines'], exact: true });
    },
  });
}

export function useFindByMineName() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: MineData) => checkMineName(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['check-mine-name'] });
      }
    },
  });
}

export function useEditMine() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: MineData) => {
      const id: any = data?.id;
      delete data.id;
      return editMine(id, data);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['edit-mine'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['mines'], exact: true });
    },
  });
}

export function useDeleteMine() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteMine(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['delete-mine'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['mines'], exact: true });
    },
  });
}

export function useStatusChange() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return statusChange(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({ queryKey: ['change-status'] });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({ queryKey: ['mines'], exact: true });
    },
  });
}