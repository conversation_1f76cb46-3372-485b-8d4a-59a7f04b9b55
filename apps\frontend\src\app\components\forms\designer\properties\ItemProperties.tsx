import Property from "./Property";
import { Item, Id, Group } from "../../types";
import { expandItemProperties } from "../../utilities";
import { useEffect, useMemo, useState } from "react";
import DDLOptions from "./DDLOptions";

interface Props {
	item: Item;
	siblings: any;
	updateItem: (item: Item) => void;
	replaceItem: (item: Item, value: any) => void;
}

export default function ItemProperties({ item, siblings, updateItem, replaceItem }: Props) {
	const [focusOptionByIndex, setFocusOptionByIndex] = useState(0);
	const [ddlOptions, setDdlOptions] = useState<any>([]);
	const [displayOptions, setDisplayOptions] = useState(false);
	const [activeTab, setActiveTab] = useState('properties');
	const properties = useMemo(() => {
		return item?.properties ?? {}
	}, [item]);

	const expandedProperties = useMemo(() => {
		const currentItem = {id: item.id, item};
		return expandItemProperties(currentItem);
	}, [properties]);

	const prefill = useMemo(() => {
		return properties?.prefill ?? '';
	}, [properties]);

	useEffect(() => {
		let display = false;
		let options = [];
		if(expandedProperties.type && expandedProperties.type.value === 'DropDown') {
			if(expandedProperties.dropdowndatasourcetype && expandedProperties.dropdowndatasourcetype.value === 'Custom') {
				display = true;
				if(expandedProperties.dropdowndatasourcetype.data) {
					options = expandedProperties.dropdowndatasourcetype.data;
				}
			}
		}
		
		setDdlOptions(options);
		setDisplayOptions(display);
	}, [expandedProperties]);

	function clearItemValue(parent:string) {
		let parentProp = expandedProperties[parent];
		let propGroup = parentProp.propGroup;
		for(let key in propGroup) {
			delete propGroup[key].value;
			if(properties[parentProp.name]) {
				delete properties[parentProp.name][key];
			}
		}
		delete parentProp.value;
		delete properties[parentProp.name].value;
		updateItem(item);
	}
	
	function handleUpdatePropValue(name:string, newValue:any, parent?:string, promote?:boolean, fileType?: string) {
		if(name === 'type') {
			replaceItem(item, newValue);
			return;
		}
		let property = expandedProperties[name];
		if(parent) {
			let parentProp = expandedProperties[parent];
			if(!properties[parentProp.name]) {
				properties[parentProp.name] = {};
			}
			let itemGroup = parentProp.propGroup ?? parentProp.buttonGroup ?? null;
			if(itemGroup) {
				property = itemGroup[name];
				if(parentProp.buttonGroup) {
					properties[parentProp.name] = newValue;
				}
				else if(promote === true) {
					//parentProp.value = newValue;
					properties[parentProp.name].value = newValue;
				}
				if(parentProp.overflow) {
					for(let i = 0; i < parentProp.overflow.length; i++) {
						let prop = expandedProperties[parentProp.overflow[i]];
						if(newValue !== 'individual') {
							if(prop.propGroup) {
								//prop.propGroup[name].value = newValue;
								properties[prop.name][name] = newValue;
							}
							else {
								prop.value = newValue;
								if(!properties[prop.name]) {
									properties[prop.name] = {};
								}
								properties[prop.name][name] = newValue;
							}
						}
					}
				}
			}
			if(!parentProp.buttonGroup) {
				properties[parentProp.name][property.name] = newValue;
			}
		} else if(fileType) {
			if(!properties[property.name]) {
				properties[property.name] = {};
			}
			properties[property.name][fileType] = newValue;
		}
		else {
			properties[property.name] = newValue;
		}
		//property.value = newValue;
		updateItem(item);
	}

	function shouldDisplay(property:any, parent?:any) {
		let parentValue = property.value ?? property.defaultValue ?? null;
		if(parent) {
			parentValue = parent.value ?? parent.defaultValue ?? null;
		}
		const condition = property.condition ?? null;
		const dependency = property.dependency ?? null;
		let display = true;

		if(condition && dependency) {
			if(dependency === 'parent') {
				if(condition[parentValue] !== 'display') {
					display = false;
				}
			}
			else if(expandedProperties[dependency]) {
				const depField = expandedProperties[dependency];
				const depValue = depField.value ?? depField.defaultValue ?? null;
				if(condition[depValue] !== 'display') {
					display = false;
				}
			}
		}
		return display;
	}

	function addOptionToDDL(answer?: string) {
		const newItemId = ddlOptions.length+1;
		const optionToAdd: any = {
			itemId: newItemId,
			answer: answer ?? '',
		}
		if(!properties.data) {
			properties.data = [];
		}
		properties.data.push(optionToAdd);
		updateItem(item);
		setFocusOptionByIndex(ddlOptions.length);
	}

	function updateDDLOption(itemId: Id, answer: string) {
		const optionIndex = properties.data.findIndex((opt:any) => opt.itemId === itemId);
		const option = properties.data[optionIndex];
		option.answer = answer;
		updateItem(item);
	}

	function deleteDDLOption(itemId: Id) {
		properties.data = ddlOptions.filter((opt:any) => opt.itemId !== itemId);
		updateItem(item);
	}

	function setPrefill(e:any) {
		let name = e.target.name;
		let newValue = e.target.value;
		if(newValue !== prefill) {
			item.properties[name] = newValue;
			updateItem(item);
		}
	}
	
	return (
		<div className="relative">
			<h1 className="text-center text-sm py-1 opacity-80">{`${item.groupId}: ${item?.properties?.headergroup?.header ?? item?.properties?.labelgroup?.label ?? `item-${item.index}`}`}</h1>
			<div className="w-full flex justify-between py-2">
				<div className={`
						text-center text-sm hover:opacity-80 cursor-pointer mr-2
						${activeTab == 'properties' ? 'opacity-100' : 'opacity-50'}
					`}
					onClick={() => setActiveTab('properties')}
				>
					Properties
				</div>
				{displayOptions &&
					<div className={`
							text-center text-sm hover:opacity-80 cursor-pointer
							${activeTab == 'ddl' ? 'opacity-100' : 'opacity-50'}
						`}
						onClick={() => setActiveTab('ddl')}
					>
						Options
					</div>
				}
				<div className={`
						text-center text-sm hover:opacity-80 cursor-pointer ml-2
						${activeTab == 'continuation' ? 'opacity-100' : 'opacity-50'}
					`}
					onClick={() => setActiveTab('continuation')}
				>
					Continuation
				</div>
			</div>
			{activeTab == 'ddl' &&
				<div className="grid grid-cols-8 gap-2 items-center py-2">
					{ddlOptions.map((o:any, index:number) => {
						return(
							<DDLOptions
								key={o.itemId}
								itemId={o.itemId}
								label={o.itemId.toString()}
								answer={o.answer ?? ''}
								currentIndex={index}
								focusIndex={focusOptionByIndex}
								setFocusOptionByIndex={setFocusOptionByIndex}
								updateOption={updateDDLOption}
								deleteOption={deleteDDLOption}
								addOption={addOptionToDDL}
							/>
						)
					})}
					<DDLOptions
						itemId="temp-id"
						label={(ddlOptions.length+1).toString()}
						answer={''}
						currentIndex={ddlOptions.length}
						focusIndex={focusOptionByIndex}
						setFocusOptionByIndex={setFocusOptionByIndex}
						updateOption={updateDDLOption}
						deleteOption={deleteDDLOption}
						addOption={addOptionToDDL}
					/>
				</div>
			}
			{activeTab == 'continuation' &&
				<div className="my-1">
					<p className="text-white text-[14px] mb-1">
						<span>Select the field to prefill </span>
						<span className="font-bold italic">{`${item?.properties?.headergroup?.header ?? item?.properties?.labelgroup?.label ?? `item-${item.index}`}`}</span>
						<span> on a new form.</span>
					</p>
					<select name="prefill" id="prefill"
						className="block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-[14px] text-black"
						value={prefill ?? ''}
						onChange={(e) => setPrefill(e)} >
						<option value={0}>Select a field</option>
						{siblings?.map((i:any) => <option key={i.id} value={i.id}>{i?.properties?.headergroup?.header ?? i?.properties?.labelgroup?.label ?? i.id}</option>)}
					</select>
				</div>
			}
			{activeTab == 'properties' && Object.keys(expandedProperties)?.length > 0 && Object.keys(expandedProperties).map((p:any, index) => {
				const property = expandedProperties[p];
				const propType = property.propType ?? '';
				const name = property.name ?? null;
				const value = property.value ?? property.defaultValue ?? null;
				const options = property.options ?? null;
				const label = property.label ?? null;
				const hint = property.hint ?? null;
				const icon = property.icon ?? null;
				const action = property.action ?? null;
				const classes = property.classes ?? '';

				if(shouldDisplay(property)) {
					if(propType == 'propGroup') {
						const parentValue = property.value ?? property.defaultValue ?? null;
						const parentName = property.name ?? null;
						let propGroup = property.propGroup ?? null;

						if(propGroup && propGroup[parentValue]) {
							propGroup = propGroup[parentValue];
						}

						if(propGroup) {
							let basisCount = 0;
							for(let key in propGroup) {
								const g = propGroup[key];
								if(shouldDisplay(g, property)) {
									basisCount++;
								}
							}
							
							return (
								<div key={index} className="my-1">
									{label ? 
										<span className="text-white text-[14px]">{label}</span> :
										<div className="flex flex-row gap-x-2">
											{Object.keys(propGroup).map((_g:any, index) => {
												const g = propGroup[_g];
												if(shouldDisplay(g, property)) {
													return (
														<div key={`pg-label-${g.name}-${index}`} title={g?.hint} className={`basis-${(1/basisCount)*100} text-center`}>
															<span className="text-white text-[14px]">{g?.label}</span>
														</div>
													)
												}
											})}
										</div>
									}
									<div className="flex flex-row gap-x-2">
										{Object.keys(propGroup).map((_g:any, index) => {
											const g = propGroup[_g];
											if(shouldDisplay(g, property)) {
												const promote = g.promote ?? false;
												
												return (
													<div key={`pgc-prop-${g.name}-${index}`} className={`basis-${(1/basisCount)*100}`}>
														<Property
															updatePropValue={handleUpdatePropValue}
															clearGroupValue={clearItemValue}
															key={`pg-prop-${g.name}-${index}`}
															id={`${g.name}-${index}`} propType={g.propType} name={g.name} parent={parentName}
															value={g.value ?? g.defaultValue} options={g.options} promote={promote}
															icon={g.icon} action={g.action} classes={g.classes} />
													</div>
												)
											}
										})}
									</div>
								</div>
							)
						}
					}
					if(propType == 'buttonGroup') {
						const buttonGroup = property.buttonGroup ?? null;
						const parentName = property.name ?? null;
						const parentValue = property.value ?? property.defaultValue ?? null;
						const classes = property.classes ?? '';
						return (
							<div key={index} className="my-1">
								<div className="flex flex-row px-6 pt-2">
									{Object.keys(buttonGroup).map((_b:any) => {
										const b = buttonGroup[_b];
										return (
											<div key={`label-${b.name}-${index}`} title={b?.hint} className={`w-1/${(Object.keys(buttonGroup).length)} text-center`}>
												<span className="text-white text-[14px]">{b?.label}</span>
											</div>
										)
									})}
								</div>
								<div className="flex flex-row px-6 pb-2">
									{Object.keys(buttonGroup).map((_b:any) => {
										const b = buttonGroup[_b];
										const promote = b.promote ?? false;
										return (
											<div key={`prop-${b.name}-${index}`} className={`w-1/${(Object.keys(buttonGroup).length)}`}>
												<Property
													updatePropValue={handleUpdatePropValue}
													clearGroupValue={clearItemValue}
													key={`prop-${b.name}-${index}`} promote={promote}
													id={`${b.name}-${index}`} propType={b.propType} name={b.name}
													value={parentValue} icon={b.icon} parent={parentName} classes={b.classes} />
											</div>
										)
									})}
								</div>
							</div>
						)
					}
					return (
						<div key={`container-${name}-${index}`} className="my-1">
							<Property
								updatePropValue={handleUpdatePropValue}
								clearGroupValue={clearItemValue}
								key={`prop-${name}-${index}`}
								id={`${name}-${index}`} propType={propType} name={name} classes={classes}
								value={value} options={options} label={label} hint={hint} icon={icon} action={action} />
						</div>
					)
				}
			})}
		</div>
	);
};