import { Injectable } from '@nestjs/common';
import { SendgridService } from '../sendgrid/sendgrid.service';

@Injectable()
export class EmailService {
  constructor(private readonly sendgridService: SendgridService) {}

  async sendEmail(data: any) {
    try {
      const emailResult = await this.sendgridService.send(data);
      return emailResult;
    } catch (error) {
      console.error('Email Send Error: ' + error.message);
      return error.message;
    }
  }
}
