import Plot from 'react-plotly.js';

export default function BarChart() {
  const layout = {
    barmode: 'stack',
    xaxis: {
      title: 'Working Section',
      type: 'category',
      //   tickformat: '%b-%d',
      color: 'white',
      gridcolor: 'rgba(0, 0, 0, 0)',
    },
    yaxis: {
      title: 'Feet Mined',
      side: 'right',
      type: 'linear',
      range: [0, 220],
      tickvals: [0, 20, 40, 60, 80, 100, 120, 140, 160, 180, 200],
      ticktext: [0, 20, 40, 60, 80, 100, 120, 140, 160, 180, 200],
      color: 'white',
      gridcolor: 'rgba(74, 168, 256, 50%)',
      gridwidth: '2.5px',
      zeroline: true,
      zerolinecolor: 'rgba(128, 194, 254, 1)',
    },
    showlegend: false,
    margin: { t: 0, l: 20, b: 40, r: 60 },
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    showspikes: false,
  };

  var trace1 = {
    x: ['5YN', '6YE', '6YN', '7YE', '8YN', '8YE'],
    y: [96, 24, 78, 67, 21, 51],
    type: 'bar',
    marker: {
      color: 'rgba(255, 194, 94, 1)',
      symbol: 'circle',
      size: 15,
    },
    line: {
      color: '',
    },
    color: 'rgba(255, 208, 132, 1)',
    hoverinfo: 'none',
    width: [0.2, 0.2, 0.2, 0.2, 0.2, 0.2],
  };

  var trace2 = {
    x: ['5YN', '6YE', '6YN', '7YE', '8YN', '8YE'],
    y: [91, 93, 70, 56, 45, 111],
    type: 'bar',
    marker: {
      color: 'rgba(38, 31, 82, 1)',
      symbol: 'circle',
      size: 15,
    },
    line: {
      color: 'rgb(255, 177, 50,100%)',
    },
    hoverinfo: 'none',
    width: [0.2, 0.2, 0.2, 0.2, 0.2, 0.2],
    text: [183, 99, 143, 122, 65, 161],
    textposition: 'outside',
    textfont: { color: 'white' },
  };

  const data = [trace1, trace2];

  return (
    <div className="w-full h-72" style={{ overflow: 'hidden' }}>
      <Plot
        className="bg-transparent w-full h-72"
        data={data}
        layout={layout}
        config={{ displayModeBar: false, responsive: true }}
      />
    </div>
  );
}
