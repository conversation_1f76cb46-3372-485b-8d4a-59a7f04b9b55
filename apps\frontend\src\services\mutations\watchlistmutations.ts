import { useMutation, useQueryClient } from '@tanstack/react-query';

import {
  addUserWatchlist,
  deleteUserWatchlistItem,
  clearUserWatchlist,
  WatchlistData,
  deleteSelectedUserWatchlist,
} from '../../api/users/watchlistapis';

export function useAddUserWatchlist() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: WatchlistData) => addUserWatchlist(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['add-user-watchlist'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        queryKey: ['user-watchlist'],
        exact: true,
      });
      queryClient.invalidateQueries({
        queryKey: ['miners'],
        exact: true,
      });
    },
  });
}

export function useClearUserWatchlist() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: () => clearUserWatchlist(),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['clear-user-watchlist'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        queryKey: ['user-watchlist'],
        exact: true,
      });
      queryClient.invalidateQueries({
        queryKey: ['miners'],
        exact: true,
      });
    },
  });
}

export function useDeleteUserWatchlistItem() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return deleteUserWatchlistItem(id);
    },
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['delete-user-watchlist'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        queryKey: ['user-watchlist'],
        exact: true,
      });
      queryClient.invalidateQueries({
        queryKey: ['miners'],
        exact: true,
      });
    },
  });
}

export function useDeleteSelectedUserWatchlist() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: string[]) => deleteSelectedUserWatchlist(data),
    onSettled: async (data, error) => {
      if (error) console.error(error);
      else {
        await queryClient.invalidateQueries({
          queryKey: ['delete-user-watchlist'],
        });
      }
    },
    onSuccess(data, variables, context) {
      queryClient.invalidateQueries({
        queryKey: ['user-watchlist'],
        exact: true,
      });
      queryClient.invalidateQueries({
        queryKey: ['miners'],
        exact: true,
      });
    },
  });
}
