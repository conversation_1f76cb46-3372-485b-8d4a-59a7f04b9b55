import { Module } from '@nestjs/common';
import { ComplianceService } from './compliance.service';
import { ComplianceController } from './compliance.controller';
import { MinesService } from '../mines/mines.service';
import { MinesModule } from '../mines/mines.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [MinesModule, UsersModule],
  controllers: [ComplianceController],
  providers: [ComplianceService],
})
export class ComplianceModule {}
