import {
  All,
  Controller,
  Get,
  Next,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';

const proxy = createProxyMiddleware({
  target: process.env.ENGINE_API_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/portal/v1/engineapi': '',
  },
  headers: {
    'X-API-KEY': process.env.X_API_KEY,
  },
  on: {
    error: (error, req, res, target) => {
      console.error(error);
    },
  },
});

@Controller('portal/v1/engineapi')
@ApiTags('engineepi')
export class EngineApiController {
  @All('/**')
  @UseGuards(JwtAuthGuard)
  async findOne(@Req() req, @Res() res, @Next() next) {
    console.log('request url', req.url);
    proxy(req, res, next);
  }
}
