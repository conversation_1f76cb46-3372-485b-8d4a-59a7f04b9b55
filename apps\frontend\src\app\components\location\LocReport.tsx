import SubMenuInnerNavBar from '../common/SubMenuInnerNavBar';
import { useState } from 'react';
import decodeJWT from '../../../utils/jwtdecoder';
import { useParams } from 'react-router-dom';
import { getFeatures } from '../../../api/users/userapis';
import { useQuery } from '@tanstack/react-query';

const LocReport = () => {
  const [selectedDate, setSelectedDate] = useState({
    startDate: new Date().toISOString().split('T')[0],
  });
  const params = useParams();

  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });

  const [date, setDate] = useState({
    startDate: new Date().toISOString().split('T')[0],
  }); // Initialize with today's date

   const today = new Date();
   const startOfWeek = new Date(today);
   startOfWeek.setDate(today.getDate() - today.getDay() + 1);
   const endOfWeek = new Date(today);
   endOfWeek.setDate(today.getDate() - today.getDay() + 7);
   const [dateRangeSelected, setDateRangeSelected] = useState({
     startDate: startOfWeek,
     endDate: endOfWeek,
   });

  if (
    features?.data.some((feature: any) => feature.FeatureName == 'Location') ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <SubMenuInnerNavBar
        leftTitle={'Reports'}
        rightTitle="Locations"
        rightSubTitle1="Last Updated"
        tabNames={['checkins', 'sections', 'personnel']}
        showSearchBox={true}
        pathToRender={
          decodeJWT()?.role == 'superuser'
            ? `Mines/${params?.mineId}/Location/report`
            : 'Location/report'
        }
        defaultTab="Checkins"
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
        date={date}
        setDate={setDate}
        personnelRange = {dateRangeSelected}
        setPersonnelRange = {setDateRangeSelected}
      />
    );
  } else {
    return '';
  }
};

export default LocReport;
