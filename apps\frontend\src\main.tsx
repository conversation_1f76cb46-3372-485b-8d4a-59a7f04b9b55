import { StrictMode } from 'react';
import * as ReactDOM from 'react-dom/client';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import mixpanel from 'mixpanel-browser';
import App from './app/app';
import { IwtEnv } from './api/apiClient';

declare var iwtEnv: IwtEnv;

const Project_Token = iwtEnv?.mixpanelProjectToken;

mixpanel.init(Project_Token);

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
const queryClient = new QueryClient();

root.render(
  // <StrictMode>
  <QueryClientProvider client={queryClient}>
    <App />
  </QueryClientProvider>
  // </StrictMode>
);
