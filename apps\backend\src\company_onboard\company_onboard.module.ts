import { Module } from '@nestjs/common';
import { CompanyOnboardController } from './company_onboard.controller';
import { CompanyOnboardService } from './company_onboard.service';
import { CompaniesModule } from '../companies/companies.module';
import { MinesModule } from '../mines/mines.module';
import { UsersModule } from '../users/users.module';
import { RolesModule } from '../roles/roles.module';
import { FormCategoriesModule } from '../form_categories/form_categories.module';
import { FormTemplatesModule } from '../form_templates/form_templates.module';
import { FormTemplateDefinitionsModule } from '../form_template_definitions/form_template_definitions.module';

@Module({
  imports: [
    CompaniesModule,
    MinesModule,
    UsersModule,
    RolesModule,
    FormCategoriesModule,
    FormTemplatesModule,
    FormTemplateDefinitionsModule
  ],
  controllers: [CompanyOnboardController],
  providers: [CompanyOnboardService],
})
export class CompanyOnboardModule {}
