import {
  IsDefined,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateRoleDto {
  @IsString({ message: 'Name must be a string' })
  @Length(1, 100, {
    message: 'Name length must be between 1 and 100 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @IsDefined()
  @ApiProperty()
  companyId: number;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
