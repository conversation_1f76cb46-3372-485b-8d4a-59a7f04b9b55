import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Feature } from './entities/feature.entity';
import { Repository } from 'typeorm';
import { CreateFeatureDto } from './dto/create-feature.dto';
import { UpdateFeatureDto } from './dto/update-feature.dto';

@Injectable()
export class FeaturesService {
  constructor(
    @InjectRepository(Feature)
    private featureRepository: Repository<Feature>
  ) {}

  async create(createFeatureDto: CreateFeatureDto) {
    const feature = this.featureRepository.create(createFeatureDto)
    return await this.featureRepository.save(feature);
  }

  async findAll() {
    return this.featureRepository.find();
  }

  async findOne(id: number) {
    return this.featureRepository.findOneBy({ id });
  }

  async update(id: number, updateFeatureDto: UpdateFeatureDto) {
    const feature = await this.featureRepository.findOneBy({ id });

    if(!feature) {
      throw new NotFoundException('Feature not Found!');
    }

    Object.assign(feature, updateFeatureDto);
    return this.featureRepository.save(feature);
  }

  async delete(id: number) {
    const feature = await this.featureRepository.findOneBy({ id });

    if(!feature) {
      throw new NotFoundException('Feature not Found!');
    }
    return this.featureRepository.remove(feature);
  }
}
