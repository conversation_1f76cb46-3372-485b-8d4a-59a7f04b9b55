import React, { useState } from 'react';
import Table, { ColumnDef } from '../common/Table';
import { SearchIcon } from '../../../assets/icons/icons';
import decodeJWT from '../../../utils/jwtdecoder';
import IWTDatePicker from '../common/IWTDatePicker';
import { useQuery } from '@tanstack/react-query';
import { getAuditLogsData } from '../../../api/auditlogs/auditLogs';
import dayjs from 'dayjs';
import Loader from '../common/Loader';
import { UserMenu } from '../common/UserMenu';

function AuditLogs() {
  const [searchPh, setSearchPh] = useState('');
  const today = new Date();
  today.setDate(today.getDate() - 7);

  const [auditLogDateRange, setAuditLogDateRange] = useState({
    startDate: new Date(today),
    endDate: new Date(),
  });

  const auditlogDataQuery = useQuery({
    queryKey: ['dates_auditLogs', auditLogDateRange],
    refetchOnWindowFocus: false,
    queryFn: () =>
      getAuditLogsData({
        startDate: dayjs(auditLogDateRange?.startDate).format('YYYY-MM-DD'),
        endDate: dayjs(auditLogDateRange?.endDate).format('YYYY-MM-DD'),
      }),
  });
  const { data: auditlogData } = auditlogDataQuery;

  function convertUTCToLocalTime(utcDate: any, format?: any) {
    const date = new Date(utcDate);
    const options = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    };
    // date.setHours(date.getHours() + 5);
    // date.setMinutes(date.getMinutes() + 30);

    if (format == 'Time') {
      const localTimeString = date.toLocaleTimeString(undefined, options);
      return localTimeString;
    } else {
      return date.toLocaleDateString();
    }
  }

  if (decodeJWT()?.role !== 'superuser') {
    return '';
  }

  const columns: ColumnDef[] = [
    {
      key: 'createDatetime',
      label: 'Date',
      type: 'text',
      render: (row) => (
        <span>
          {dayjs(convertUTCToLocalTime(row?.createDatetime))?.format(
            'MM-DD-YYYY'
          )}
        </span>
      ),
    },
    {
      key: 'createDatetime',
      label: 'Time',
      type: 'text',
      render: (row) => (
        <span>{convertUTCToLocalTime(row?.createDatetime, 'Time')}</span>
      ),
    },
    {
      key: 'mineName',
      label: 'Mine',
      type: 'text',
    },
    {
      key: 'createdBy',
      label: 'Changed By',
      type: 'text',
      render: (row) => (
        <div>
          <span>{row?.createdBy}</span>
          <span className="text-[12px] block">{row?.createdByRole}</span>
        </div>
      ),
    },
    {
      key: 'createdFor',
      label: 'Changed For',
      type: 'text',
      render: (row) => (
        <div>
          <span>{row?.createdFor}</span>
          <span className="text-[12px] block">
            {row?.createdForRole.toLowerCase()}
          </span>
        </div>
      ),
    },
    {
      key: 'action',
      label: 'Change Action',
      type: 'text',
      render: (row) => (
        <div>
          <span className="text-white block  w-full break-words whitespace-normal">
            {row?.action}
          </span>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="w-full min-h-[90vh]">
        <div className="sticky w-full top-0 z-30  box  bg-top px-10 2xl:px-16">
          <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe]  pb-4 pt-4   ">
            <div className="">
              {/* <div className="relative  mt-6  w-1/2">
                <span>
                  <SearchIcon className="absolute top-0 left-2 h-6 mr-1 my-1.5  text-white cursor-pointer"></SearchIcon>
                </span>
                <input
                  id="search-result"
                  type="text"
                  className={`border   text-white text-sm pl-8 outline-none bg-transparent rounded border-[#4AA8FE] block w-full p-2  placeholder-white autoComOff`}
                  placeholder="Search User"
                  autoFocus
                  value={searchPh}
                  onChange={(e: any) => setSearchPh(e?.target?.value)}
                />
              </div> */}
            </div>
            <div className="">
              {/* <h6 className="px-2 pb-2 font-bold text-white text-[32px] text-right">
                Audit Logs
              </h6> */}
              <div className="flex justify-end items-center -mr-10 relative">
                <h6 className="font-bold text-white text-[32px]">Audit Logs</h6>
                <span className="ml-4 ">
                  <UserMenu />
                </span>
              </div>
              <div className="w-[270px] float-end">
                <IWTDatePicker
                  auditLogDateRange={auditLogDateRange}
                  setAuditLogDateRange={setAuditLogDateRange}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="mt-8  px-10 2xl:px-16">
          {auditlogDataQuery?.isFetching ? (
            <div>
              <div>
                <div className="flex justify-center items-center h-full pt-[200px] white">
                  {<Loader />}
                </div>
                <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                  Loading....
                </div>
              </div>
            </div>
          ) : (
            <div>
              <div className="pt-8 pb-2 userMTableBg px-5 rounded-xl">
                <div className="block">
                  <h6 className="font-semibold text-[20px] text-white">
                    {`Audit Logs (${
                      auditlogData?.data?.length
                        ? auditlogData?.data?.length
                        : 0
                    })`}
                  </h6>
                  <p className="font-normal my-1 text-[14px] tracking-normal leading-5 text-[#cccccc] ">
                    List of audit logs for selected date range
                  </p>
                </div>
                <div className="mt-5">
                  {auditlogData?.data.length > 0 ? (
                    <Table
                      columns={columns}
                      data={auditlogData?.data ?? []}
                      searchText={searchPh}
                      searchOnColumn="mineName"
                      sortable={false}
                      separateLine={false}
                      scrollable={true}
                      dataRenderLimitMdScreen={8}
                      dataRenderLimitLgScreen={8}
                      tableHeightClassLg="tableheightForlg"
                      tableHeightClassMd="tableheightFormd"
                    />
                  ) : auditLogDateRange.startDate === null &&
                    auditLogDateRange.endDate === null ? (
                    <h2 className="text-[26px] font-bold italic text-center text-white">
                      Please select the date range to view audit logs
                    </h2>
                  ) : (
                    <h2 className="text-[26px] font-bold text-center text-white">
                      There are currently no audit logs for selected date range
                    </h2>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
export default AuditLogs;
