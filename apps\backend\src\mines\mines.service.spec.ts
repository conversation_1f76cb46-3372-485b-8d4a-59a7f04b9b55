import { Test, TestingModule } from '@nestjs/testing';
import { MinesService } from './mines.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Mine } from './entities/mine.entity';
import { CreateMineDto } from './dtos/create-mine.dto';
import { UpdateMineDto } from './dtos/update-mine.dto';
import { ChangeStatusDto } from './dtos/change-status.dto';

describe('MinesService', () => {
  let service: MinesService;
  const mockMineRepository = {
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
    create: jest.fn(),
    findOneBy: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MinesService,
        {
          provide: getRepositoryToken(Mine),
          useValue: mockMineRepository,
        },
      ],
    }).compile();

    service = module.get<MinesService>(MinesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  it('create => Should create a new mine and return its data', async () => {
    // arrange
    const createMineDto = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    } as CreateMineDto;

    const mine = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    } as Mine;

    jest.spyOn(mockMineRepository, 'save').mockReturnValue(mine);

    // act
    const result = await service.create(createMineDto);

    // assert
    expect(mockMineRepository.save).toHaveBeenCalled();
    expect(mockMineRepository.save).toHaveBeenCalledWith(createMineDto);

    expect(result).toMatchObject(mine);
  });
  it('findAll => should return an array or mines', async () => {
    // arrange
    const companyId = '1';
    const mine = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    };
    const mines = [mine];
    jest.spyOn(mockMineRepository, 'find').mockReturnValue(mines);

    // act
    const result = await service.findAll(+companyId);

    // assert
    expect(mockMineRepository.find).toHaveBeenCalled();
    expect(result).toEqual(mines);
  });
  it('findOne => should find a mine by a given id and return its data', async () => {
    // arrange
    const id = 1;
    const mine = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    };

    jest.spyOn(mockMineRepository, 'findOne').mockReturnValue(mine);

    // act
    const result = await service.getMine(id);

    // assert
    expect(mockMineRepository.findOne).toHaveBeenCalled();
    expect(mockMineRepository.findOne).toHaveBeenCalledWith({
      where: {
        id,
      },
      relations: ['company'],
    });
    expect(result).toMatchObject(mine);
  });
  it('update => should find a mine by a given id and update its data', async () => {
    // arrange
    const id = 1;
    const updateMineDto = {
      name: 'new test mine 1',
    } as UpdateMineDto;

    const mine = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    } as Mine;

    const updatedMine = Object.assign(mine, updateMineDto);
    jest.spyOn(mockMineRepository, 'save').mockReturnValue(updatedMine);

    // act
    const result = await service.update(id, updateMineDto);

    // assert
    expect(mockMineRepository.findOne).toHaveBeenCalled();
    expect(mockMineRepository.findOne).toHaveBeenCalledWith({
      where: {
        id,
      },
      relations: ['company'],
    });
    expect(mockMineRepository.save).toHaveBeenCalled();
    expect(mockMineRepository.save).toHaveBeenCalledWith(updatedMine);
    expect(result).toMatchObject(updatedMine);
  });
  it('remove => should find a mine by a given id, remove and then return Number of affected rows', async () => {
    // arrange
    const id = 1;
    const mine = {
      companyId: 1,
      name: 'test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    };
    jest.spyOn(mockMineRepository, 'delete').mockReturnValue(mine);

    // act
    const result = await service.delete(id);

    // assert
    // expect(mockMineRepository.findOneBy).toHaveBeenCalled();
    // expect(mockMineRepository.findOneBy).toHaveBeenCalledWith({ id });
    expect(mockMineRepository.delete).toHaveBeenCalled();
    expect(mockMineRepository.delete).toHaveBeenCalledWith({ id });
    expect(result).toMatchObject(mine);
  });
  it('changestatus => should find a mine by a given id and change its status', async () => {
    // arrange
    const id = 1;
    const changeStatusDto = {
      isActive: false,
    } as ChangeStatusDto;

    const mine = {
      companyId: 1,
      name: 'new test mine 1',
      code: 'tm1',
      location: 'loc1',
      isActive: true,
    } as Mine;

    const updatedMine = Object.assign(mine, changeStatusDto);
    jest.spyOn(mockMineRepository, 'save').mockReturnValue(updatedMine);

    // act
    const result = await service.changeStatus(id, changeStatusDto.isActive);

    // assert
    expect(mockMineRepository.findOne).toHaveBeenCalled();
    expect(mockMineRepository.findOne).toHaveBeenCalledWith({
      where: {
        id,
      },
      relations: ['company'],
    });
    expect(mockMineRepository.save).toHaveBeenCalled();
    expect(mockMineRepository.save).toHaveBeenCalledWith(updatedMine);
    expect(result).toMatchObject(updatedMine);
  });
});
