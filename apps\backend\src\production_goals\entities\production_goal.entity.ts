import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToOne,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Shift } from '../../shifts/entities/shift.entity';
import { Section } from '../../sections/entities/section.entity';

@Entity('production_goals')
export class ProductionGoal {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'section_id', nullable: false })
  sectionId: number;

  @Column({ name: 'shift_id', nullable: false })
  shiftId: number;

  @Column({ name: 'goal', nullable: false })
  goal: number;

  @Column({ name: 'effective_date', nullable: false })
  effectiveDate: Date;

  @Column({ name: 'end_date', nullable: true })
  endDate: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  @ManyToOne(() => Section)
  @JoinColumn({ name: 'section_id' })
  section: Section;

  @ManyToOne(() => Shift)
  @JoinColumn({ name: 'shift_id' })
  shift: Shift;
}
