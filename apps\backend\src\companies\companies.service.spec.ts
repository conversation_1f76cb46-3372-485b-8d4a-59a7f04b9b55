import { Test, TestingModule } from '@nestjs/testing';
import { CompaniesService } from './companies.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Company } from './entities/company.entity';

describe('CompaniesService', () => {
  let service: CompaniesService;

  const mockCompaniesRepository = {
    create: jest.fn().mockImplementation((dto) => dto),
    save: jest
      .fn()
      .mockImplementation((dto) => Promise.resolve({ id: Date.now(), ...dto })),

    update: jest.fn().mockImplementation((id, dto) => {
      Promise.resolve({
        id,
        name: 'Southern Copper Corporation',
        code: 'SCCO',
        createdBy: 1,
        createdAt: Date,
        updatedBy: 1,
        updatedAt: Date,
      });
    }),

    delete: jest.fn().mockImplementation((id) => {
      Promise.resolve({
        id,
        name: 'Southern Copper Corporation',
        code: 'SCCO',
        createdBy: 1,
        createdAt: Date,
        updatedBy: 1,
        updatedAt: Date,
      });
    }),

    find: jest.fn().mockImplementation(() => {
      Promise.resolve([
        {
          id: 1,
          name: 'Southern Copper Corporation',
          code: 'SCCO',
          createdBy: 1,
          createdAt: Date,
          updatedBy: 1,
          updatedAt: Date,
        },
      ]);
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompaniesService,
        {
          provide: getRepositoryToken(Company),
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<CompaniesService>(CompaniesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a new company & return that', async () => {
    const dto = {
      name: 'Southern Copper Corporation',
      code: 'SCCO',
      createdBy: 1,
    };
    expect(
      await service.create({
        name: dto.name,
        code: dto.code,
        createdBy: dto.createdBy,
      })
    ).toEqual({
      id: expect.any(Number),
      name: dto.name,
      code: dto.code,
      createdBy: dto.createdBy,
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
      updatedBy: expect.any(null),
    });
    expect(service.create).toHaveBeenCalledWith(dto);
  });

  it('should update a company & return that', async () => {
    const dto = {
      name: 'Southern Copper Corporation',
      code: 'SCCO',
      updatedBy: 1,
    };
    expect(await service.update(1, { ...dto })).toEqual({
      id: expect.any(Number),
      createdBy: expect.any(Number),
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
      ...dto,
    });
    expect(service.update).toHaveBeenCalledWith(dto);
  });

  it('should get all companies', async () => {
    let dto = [
      {
        id: 1,
        name: 'Southern Copper Corporation',
        code: 'SCCO',
        createdBy: 1,
        createdAt: Date,
        updatedBy: 1,
        updatedAt: Date,
      },
    ];
    expect(await service.find()).toEqual([...dto]);
    expect(service.find).toHaveBeenCalledWith([...dto]);
  });

  it('should get one company', async () => {
    let dto = {
      id: 1,
      name: 'Southern Copper Corporation',
      code: 'SCCO',
      createdBy: 1,
      createdAt: Date,
      updatedBy: 1,
      updatedAt: Date,
    };

    expect(await service.findOne(1)).toEqual(dto);
    expect(service.findOne).toHaveBeenCalledWith(dto);
  });
});
