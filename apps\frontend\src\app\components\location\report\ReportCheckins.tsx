import { Link, useOutletContext, useParams } from 'react-router-dom';
import Table, { ColumnDef } from '../../common/Table';
import { useLocationReportCheckInsData } from '../../../../services/queries/locationdashboardqueries';
import { getLocationReportCheckInsData } from '../../../../api/location/locationdashboardapi';
import decodeJWT from '../../../../utils/jwtdecoder';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import Loader from '../../common/Loader';
import { escapeRegExp } from '../../../../utils/constant';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../../PageName';

const ReportCheckins = () => {
  const [tableCount, setTableCount] = useState();
  const [searchText, setLastUpdatedDate, data] = useOutletContext() as [
    string,
    any,
    any
  ];
  const params = useParams();

  const regex = new RegExp(`(${escapeRegExp(searchText)})`, 'i');
  const columns: ColumnDef[] = [
    {
      key: 'minerName',
      label: 'Miner',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden" title={row?.minerName}>
          <Link
            className="underline"
            to={
              params?.mineId
                ? `/app/Mines/${params?.mineId}/Location/report/personnel/${row?.minerId}`
                : `/app/Location/report/personnel/${row?.minerId}`
            }
            onClick={() => {
              mixpanel.track('Personnel Click', {
              Page_Name: getPageNamesFromUrl(params['*']),
              MshaId: decodeJWT()?.mshaId,
              MinerId: row?.minerId
              }); 
            }}
            dangerouslySetInnerHTML={{
              __html: row?.minerName?.replace(
                regex,
                "<span style='color: #FFD084;'>$1</span>"
              ),
            }}
          ></Link>
        </div>
      ),
    },
    {
      key: 'checkedInTime',
      label: 'Checked In',
      type: 'time',
      render: (row) => (
        <>
          <div className="w-[100px] truncate ...">
            {row?.checkedInTime ? row.checkedInTime : '-'}
          </div>
        </>
      ),
    },
    {
      key: 'firstUG',
      label: 'First UG',
      type: 'time',
      render: (row) => (
        <>
          <div className="w-[100px] truncate ...">
            {row?.firstUG ? row.firstUG : '-'}
          </div>
        </>
      ),
    },
    {
      key: 'lastUG',
      label: 'Last UG',
      type: 'time',
      render: (row) => (
        <>
          <div className="w-[100px] truncate ...">
            {row?.lastUG ? row?.lastUG : '-'}
          </div>
        </>
      ),
    },
    {
      key: 'lastSection',
      label: 'Last Section',
      type: 'text',
      render: (row) => (
        <>
          <div className="w-[100px] truncate ...">
            {row?.lastSection ? row?.lastSection : '-'}
          </div>
        </>
      ),
    },
  ];

  const liveCheckinsQuery = useQuery({
    queryKey: ['location-report-checkins', data?.startDate],
    refetchOnWindowFocus: false,
    queryFn: () =>
      getLocationReportCheckInsData(
        Number(decodeJWT()?.mineid),
        data?.startDate
      ),
    enabled: data?.startDate != null,
  });

  const { data: reportCheckinsData, refetch: refetchReportCheckinsData } =
    liveCheckinsQuery;

  useEffect(() => {
    setTableCount(reportCheckinsData?.data?.minersData?.length);
    
    // refetchReportCheckinsData();
  }, [data?.startDate]);

  return (
    <>
      <div className="box min-h-[70vh]">
        {reportCheckinsData?.data?.minersData?.length > 0 ? (
          <>
            <div className="pt-6    bg-[#2c5b8b] ">
              <div className="w-[92%] m-auto grid grid-cols-2 divide-x tableTitle rounded-tl-lg rounded-tr-lg">
                <p className="font-bold text-white text-2xl p-6 border-none">
                  {`Checked In (${tableCount})`}
                </p>
              </div>
            </div>
            <div className="  rounded-b-[8px]">
              <div className="w-[92%] mt-[-2px] bg-[#21496E] m-auto">
                <Table
                  columns={columns}
                  data={reportCheckinsData?.data?.minersData ?? []}
                  searchText={searchText}
                  scrollable={true}
                  sortable={true}
                  searchOnColumn={'minerName'}
                  separateLine={false}
                  dataRenderLimitMdScreen={8}
                  dataRenderLimitLgScreen={15}
                  tableHeightClassLg="h-[36rem]"
                  tableHeightClassMd="h-[16rem]"
                  paddingClass="pr-6 pl-6 pb-6"
                  setTableCount={setTableCount}
                />
              </div>
            </div>
          </>
        ) : liveCheckinsQuery.isFetching || liveCheckinsQuery.isLoading ? (
          <div>
            <div>
              <div className="flex justify-center items-center h-full pt-8 white">
                {<Loader />}
              </div>
              <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                Loading....
              </div>
            </div>
          </div>
        ) : (
          <p className="w-full h-[104px] flex justify-center items-center font-[600] text-white text-[24px] agBreakup">
            No data available
          </p>
        )}
      </div>
    </>
  );
};

export default ReportCheckins;
