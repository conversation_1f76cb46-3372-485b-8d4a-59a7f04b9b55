import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Res,
  Next,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ComplianceService } from './compliance.service';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { MinesService } from '../mines/mines.service';
import { concat } from 'rxjs';
import { RolesGuard } from '../auth/gurads/roles.guard';
import { Features } from '../auth/gurads/feature.decorator';

const proxy = createProxyMiddleware({
  target: process.env.PC_API_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/portal/v1/compliance': '/api',
  },
  headers: {
    'PC-Api-Key': process.env.PC_API_KEY,
  },
  on: {
    error: (error, req, res, target) => {
      console.error(error);
    },
  },
});

@Controller('portal/v1/compliance')
@ApiTags('compliance')
export class ComplianceController {
  constructor(
    private readonly complianceService: ComplianceService,
    private minesService: MinesService
  ) {}

  @Get('/ComplianceModule')
  @Features(['compliance'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async findOne(@Req() req, @Res() res, @Next() next) {
    const user = req.user;
    const mine = await this.minesService.getMine(parseInt(user.mineid));
    const mineCode = mine.code;
    req.url += '/' + mineCode;
    proxy(req, res, next);
  }

  @Get('/ComplianceModule/AllMines/**')
  @UseGuards(JwtAuthGuard)
  async allMines(@Req() req, @Res() res, @Next() next) {
    proxy(req, res, next);
  }
}
