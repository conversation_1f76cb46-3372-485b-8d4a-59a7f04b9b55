import { SearchIcon } from '../../../assets/icons/icons';
import { useState, ChangeEvent, useEffect } from 'react';

const SelectWithSearch = (props: {
  searchText: any;
  dataKey: string;
  data?: any;
  searchAction?: any;
  actionKeyLimit?: number;
  optionClick?: any;
  placeholder?: string;
  onInputChange?: (value: string) => void;
  notFoundOption?: any;
  maxInputLength?: number;
}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const [isRetrievingData, setIsRetrievingData] = useState(false);
  const [listActive, setListActive] = useState(false);
  const [options, setOptions] = useState<any[]>([]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    updateFilteredOptions(value);
    if (props.onInputChange) {
      props.onInputChange(value);
    }
    if (props.notFoundOption) {
      props.notFoundOption();
    }
  };

  const updateFilteredOptions = (value: any) => {
    let filteredOptions = [];

    const searchValue = typeof value === 'string' ? value.toLowerCase() : '';

    if (props?.dataKey === 'timezone') {
      filteredOptions = props.data?.filter((option: any) => {
        return (
          typeof option === 'object' &&
          option.name &&
          option.name.toLowerCase().includes(searchValue)
        );
      });
    } else {
      filteredOptions = props.data?.filter((option: any) => {
        const keyValue = option[props.dataKey];
        return keyValue?.toLowerCase()?.includes(searchValue);
      });
    }

    if (searchValue !== '') {
      setOptions(filteredOptions);
    } else {
      setOptions([]);
    }
  };

  const handleOptionClick = (option: any) => {
    if (props?.dataKey === 'timezone') {
      setOptions([]);
      setInputValue(option.name);
      props.optionClick(option);
      if (props.onInputChange) {
        props.onInputChange(option?.name);
      }
    } else {
      setOptions([]);
      setInputValue(option[props.dataKey]);
      props.optionClick(option);
    }
  };

  useEffect(() => {
    if (
      inputValue?.length >= (props.actionKeyLimit ?? 4) &&
      !isRetrievingData
    ) {
      if (listActive) {
        setIsRetrievingData(true);
        props.searchAction(inputValue);
      } else {
        setIsRetrievingData(false);
        setListActive(true);
      }
    }
    if (inputValue.length < (props.actionKeyLimit ?? 4)) {
      setIsRetrievingData(false);
    }
  }, [inputValue]);

  useEffect(() => {
    updateFilteredOptions(inputValue);
  }, [props?.data]);

  useEffect(() => {
    setIsRetrievingData(false);
    setListActive(false);
    setInputValue(props?.searchText);
  }, [props?.searchText]);

  useEffect(() => {}, [inputValue]);

  return (
    <div className="relative">
      <div
        id="searchWithPartialInput"
        // className="flex items-center h-12 w-72 text-base p-4 border border-[#4AA8FE] justify-start rounded-md text-white"
        className="flex items-center text-base justify-start bg-gray-200 p-1.5 w-full my-1 rounded text-[16px] text-black"
      >
        <div>
          <SearchIcon fill={'#9ca3af'} />
        </div>
        <input
          type="text"
          value={inputValue}
          placeholder={props.placeholder || 'Select MSHA ID'}
          className="ml-1.5 bg-transparent border-none outline-none focus:border-none w-full"
          onChange={handleInputChange}
          autoFocus={props?.dataKey != 'timezone'}
          maxLength={props.maxInputLength}
        ></input>
      </div>
      {options ? (
        <ul className="w-[100%] absolute z-10 bg-[#253543] text-white r-1 shadow-md rounded-md max-h-[125px] overflow-y-auto">
          {options?.map((option: any, index: number) => (
            <li
              key={`${props?.dataKey === 'timezone' ? option.name : option[props.dataKey]}`}
              onClick={() => handleOptionClick(option)}
              className="px-4 py-1 cursor-pointer hover:bg-[#253543]-100"
            >
              {props?.dataKey === 'timezone'
                ? option.name
                : option[props.dataKey]}
            </li>
          ))}
        </ul>
      ) : (
        <></>
      )}
    </div>
  );
};

export default SelectWithSearch;
