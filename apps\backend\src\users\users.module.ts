import { Module, forwardRef } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { UserRole } from './entities/user-role.entity';
import { UserMine } from './entities/user-mine.entity';
import { RolesModule } from '../roles/roles.module';
import { EmailService } from '../common/email/email.service';
import { SendgridService } from '../common/sendgrid/sendgrid.service';
import { ConfigService } from '@nestjs/config';
import { MinesService } from '../mines/mines.service';
import { MinesModule } from '../mines/mines.module';
import { CompaniesModule } from '../companies/companies.module';
import { Company } from '../companies/entities/company.entity';
import { AuditModule } from '../audit/audit.module';
@Module({
  imports: [
    RolesModule,
    MinesModule,
    TypeOrmModule.forFeature([User, UserRole, UserMine, Company]),
    forwardRef(() => AuditModule),
  ],
  controllers: [UsersController],
  providers: [UsersService, SendgridService, EmailService, ConfigService],
  exports: [UsersService],
})
export class UsersModule {}
