import {
  Body,
  Controller,
  Param,
  Patch,
  Post,
  Get,
  ParseIntPipe,
  HttpStatus,
  HttpException,
  Request,
  UseGuards,
  Req,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { ResetPassword } from './dto/reset-password';
import { RolesGuard } from '../auth/gurads/roles.guard';
import { Features } from '../auth/gurads/feature.decorator';
import { Roles } from '../auth/gurads/roles.decorator';

@Controller('portal/v1/users')
@ApiTags('users')
export class UsersController {
  constructor(private readonly userService: UsersService) {}

  @Get()
  @Roles(['admin', 'superuser'])
  @Features(['users'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async findAll(@Request() req) {
    return await this.userService.findAll(
      req.user.mineCompanyId ? req.user.mineCompanyId : req.user.companyid
    );
  }

  @Get('features')
  @UseGuards(JwtAuthGuard)
  async getFeaturesById(@Request() req) {
    const features = await this.userService.getUsersWithRole(
      parseInt(req.user.userId)
    );
    return features;
  }

  @Get('username/:username')
  @UseGuards(JwtAuthGuard)
  async findByUsername(@Param('username') username: string): Promise<User> {
    return await this.userService.findByUsername(username);
  }

  @Get('email/:email')
  @UseGuards(JwtAuthGuard)
  async findByEmail(@Param('email') email: string): Promise<User> {
    return await this.userService.findByEmail(email);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findById(@Param('id', ParseIntPipe) id: number): Promise<User> {
    return await this.userService.findUserById(id);
  }

  @Post()
  @Roles(['admin', 'superuser'])
  @Features(['users'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async createUser(@Body() user: CreateUserDto, @Request() req) {
    try {
      return await this.userService.createUser(user, req.user);
    } catch (error) {
      throw new HttpException(
        'Failed to create user',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Patch(':id')
  @Roles(['admin', 'superuser'])
  @Features(['users'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async updateUser(
    @Param('id', ParseIntPipe) id: number,
    @Body() UpdateUserDto: UpdateUserDto,
    @Request() req
  ) {
    const user = new User();
    Object.assign(user, UpdateUserDto);
    await this.userService.updateUser(id, UpdateUserDto, req.user);
    return { message: 'User updated successfully' };
  }

  @Patch('status/:id')
  @UseGuards(JwtAuthGuard)
  async updateUserStatus(@Param('id', ParseIntPipe) id: number, @Req() req) {
    const user = new User();
    let updatedUser = await this.userService.updateUserStatus(id, req.user);
    return updatedUser;
  }

  @Get('/autogenerateUsername/:firstname/:lastname')
  @UseGuards(JwtAuthGuard)
  async generateUniqueUsername(
    @Param('firstname') firstname: string,
    @Param('lastname') lastname: string
  ) {
    return this.userService.generateUniqueUsername(firstname, lastname);
  }

  @Patch('/isDelete/:id')
  @Roles(['admin', 'superuser'])
  @Features(['users'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async deleteUser(@Param('id') id: number, @Req() req) {
    let deletedUser = await this.userService.deleteUser(id, req.user);
    return deletedUser;
  }

  @Post('/resetPassword')
  async resetPassword(@Body() resPassword: ResetPassword) {
    await this.userService.resetPassword(resPassword);
  }
}
