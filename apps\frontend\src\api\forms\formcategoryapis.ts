import { ApiClient } from '../apiClient';

export const getFormCategories = async () =>
  await ApiClient.get(`/api/portal/v1/formCategories`);

export const getFormCategoriesUsedByFormTemplates = async () =>
  await ApiClient.get(`/api/portal/v1/formCategories/usedByTemplates`);

export const getFormCategoriesUsedBySavedForms = async () =>
  await ApiClient.get(`/api/portal/v1/formCategories/usedBySavedForms`);

export const getFormCategoriesUsedByCompletedForms = async () =>
  await ApiClient.get(`/api/portal/v1/formCategories/usedByCompletedForms`);

export const addFormCategory = async (data: {name: string}) =>
  await ApiClient.post(`/api/portal/v1/formCategories`, data);

export const deleteFormCategory = async (id: number) =>
  await ApiClient.patch(`/api/portal/v1/formCategories/isDelete/${id}`);
