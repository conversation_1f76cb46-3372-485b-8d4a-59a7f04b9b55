import { Controller, Get, Param, UseGuards, Request, Query } from '@nestjs/common';
import { TimezonesService } from './timezones.service';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { ApiTags } from '@nestjs/swagger';

@Controller('portal/v1/timezones')
@ApiTags('timezones')
export class TimezonesController {
  constructor(private readonly timezonesService: TimezonesService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Request() req) {
    return this.timezonesService.findAll();
  }

  @Get('searchTimezone')
  @UseGuards(JwtAuthGuard)
  async searchTimezone(@Query('partialName') partialName: string) {
    const results = await this.timezonesService.searchTimezones(partialName);
    return results;
  }
}
