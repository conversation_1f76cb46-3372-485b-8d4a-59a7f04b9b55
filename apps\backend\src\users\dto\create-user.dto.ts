import { ApiProperty } from '@nestjs/swagger';
import { Length } from '@nestjs/class-validator';

export class CreateUserDto {
  @ApiProperty()
  username: string;
  @ApiProperty()
  companyId: number;
  @ApiProperty()
  password: string;
  @ApiProperty()
  @Length(1, 20, {
    message: 'First Name length must be between 1 and 20 characters',
  })
  firstName: string;
  @ApiProperty()
  @Length(1, 20, {
    message: 'Last Name length must be between 1 and 20 characters',
  })
  lastName: string;
  @ApiProperty()
  email: string;
  @ApiProperty()
  roleId: number;
  @ApiProperty()
  mineId: number;
}
