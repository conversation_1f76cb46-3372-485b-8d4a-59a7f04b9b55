import { ApiClient } from '../apiClient';

export interface DateSelection {
  userId: number;
  type: string;
  predefinedDate: string;
  startDate: string;
  endDate: string;
}

export const saveDateRange = async (data: DateSelection) => {
  return await ApiClient.post('/api/portal/v1/date-selection', data);
};

export const getDateRangerForUser = async (userId: number, type: string) => {
  return await ApiClient.get(`/api/portal/v1/date-selection/${userId}/${type}`);
};
